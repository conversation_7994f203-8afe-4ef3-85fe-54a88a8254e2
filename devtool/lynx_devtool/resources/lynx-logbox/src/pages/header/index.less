// Copyright 2025 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

.container {
  display: flex;
  flex-direction: column;
  background-color: white;
  margin: -8px;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;
  box-shadow: 0px 2px 5px #e8e8e8;
}

.navigation {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 26px;
  padding: 8px;
  
  &.error {
    background-color: #CD5C5C;
  }
  
  &.warning {
    background-color: #F0E68C;
  }
  
  .nav-button {
    border: none;
    border-radius: 4px;
    cursor: pointer;
    color: black;
    display: flex;
    outline: none;
    
    &--left {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      left: 0;
      width: 75px;
      justify-content: flex-start;
    }
    
    &--right {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      right: 0;
      width: 75px;
      justify-content: flex-end;
    }
    
    &--error {
      background-color: #CD5C5C;
    }
    
    &--warning {
      background-color: #F0E68C;
    }
  }

  .counter {
    text-align: center;
    flex: 1;
  }
}

.header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 50px;
  padding: 8px 14px;
  
  .icon {
    height: 32px;
    width: 32px;
    cursor: pointer;
  }
  
  .title {
    margin-left: 8px;
    font-size: 20px;
    color: black;
  }
  
  .expand {
    flex: 1;
  }
  
  .button-icon {
    font-size: 19px;
    outline: none;
  }
  
  .gap {
    width: 12px;
  }
}
