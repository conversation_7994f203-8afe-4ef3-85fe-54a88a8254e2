{"Lynx Debug desc": "DevTool 调试能力、LogBox、性能工具的总开关", "Lynx DevTool desc": "开启后支持 DOM 元素审查，CSS 调试 JS 调试等\n开关后需重启 app", "V8": "V8 引擎", "V8 desc": "选择“On”，优先使用 V8 引擎运行 JS；\n选择“AlignWithProd”，与线上一致，仅在配置了 enable_v8 的页面使用V8引擎，其他页面使用 PrimJS 引擎\n（注意:对配置了 enable_v8 的页面，建议对两种引擎都进行测试）\n开关后需重启 app", "QuickJS debug": "PrimJS 调试", "QuickJS debug desc iOS": "开启后支持 PrimJS 引擎的JS调试，关闭后则使用线上环境，即 JSC 引擎，且不支持调试\n（注意：调试和线上 JS 引擎不一致，建议对 JSC 引擎也进行测试）\n开关后需重启 app", "QuickJS debug desc Android": "仅在“V8 引擎”选择“Off”或“AlignWithProd”情况下生效，开启后支持 PrimJS 引擎的 JS 调试\n开关后需重启 app", "QuickJS cache": "PrimJS 缓存", "QuickJS cache desc": "开启后支持 JS 缓存，仅在 Android 端关闭 JS 调试情况下生效", "Longpress": "长按弹出调试菜单", "Longpress desc": "开启后单指长按页面能够弹出调试菜单", "DOM inspector": "DOM 元素审查", "DOM inspector desc": "开启后支持 DOM 元素审查", "PixelCopy": "PixelCopy 截屏", "PixelCopy desc": "开启后使用 PixelCopy 方式截屏", "LogBox": "LogBox", "LogBox desc": "开启后运行出错时会弹出错误提示", "Highlight Touch": "高亮触摸", "Highlight Touch desc": "开启后触摸时高亮触摸区域,并且输出事件相关日志", "Performance Tool": "性能工具", "Performance Tool desc": "开启后支持 Trace、TestBench 等\n开关后需重启 app", "Current JS Engine": "当前 JS 引擎"}