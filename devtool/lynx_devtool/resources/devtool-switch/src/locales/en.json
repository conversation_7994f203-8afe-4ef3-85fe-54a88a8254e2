{"Lynx Debug desc": "The main switch for DevTool, LogBox and Performance Tool.", "Lynx DevTool desc": "Enables DOM element inspection, CSS debugging, JS debugging, and more. \nRequires an app restart after toggling.", "V8": "V8 Engine", "V8 desc": "Select 'On' to run JS with the V8 engine. \nSelect 'AlignWithProd' to use the V8 engine on pages configured with enable_v8, and the PrimJS engine on other pages.\n(Note: For pages configured with enable_v8, it is recommended to test with both engines)\nRequires an app restart after toggling.", "QuickJS debug": "PrimJS Debugging", "QuickJS debug desc iOS": "Enables JS debugging with the PrimJS engine. \nWhen disabled, the production environment (the JSC engine) is used and JS debugging is not supported. \n(Note: The JS engine type differs between debugging and production environments, so it is recommended to test with the JSC engine as well.)\nRequires an app restart after toggling.", "QuickJS debug desc Android": "Only effective when 'Enable V8 Engine' is set to 'Off' or 'AlignWithProd'. Enables JS debugging with the PrimJS engine.\nRequires an app restart after toggling.", "QuickJS cache": "PrimJS Cache", "QuickJS cache desc": "Enables JS caching. Only effective when JS debugging is disabled on Android.", "Longpress": "Long Press to Open Debug Menu", "Longpress desc": "Enables the Debug Menu to be opened by long-pressing the page with one finger.", "DOM inspector": "DOM Element Inspection", "DOM inspector desc": "Enables DOM element inspection.", "PixelCopy": "PixelCopy Screenshot", "PixelCopy desc": "Uses PixelCopy for screenshots.", "LogBox": "LogBox", "LogBox desc": "Displays error prompts when errors occur.", "Highlight Touch": "Hightlight Touch", "Highlight Touch desc": "Highlights the touch area and logs event-related messages.", "Performance Tool": "Performance Tool", "Performance Tool desc": "Supports Trace, TestBench, and more. \nRequires an app restart after toggling.", "Current JS Engine": "Current JS engine"}