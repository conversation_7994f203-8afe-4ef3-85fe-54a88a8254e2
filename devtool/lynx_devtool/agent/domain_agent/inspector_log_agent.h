// Copyright 2019 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#ifndef DEVTOOL_LYNX_DEVTOOL_AGENT_DOMAIN_AGENT_INSPECTOR_LOG_AGENT_H_
#define DEVTOOL_LYNX_DEVTOOL_AGENT_DOMAIN_AGENT_INSPECTOR_LOG_AGENT_H_

#include "devtool/base_devtool/native/public/cdp_domain_agent_base.h"
#include "devtool/lynx_devtool/agent/lynx_devtool_mediator.h"

namespace lynx {

namespace piper {
struct ConsoleMessage;
}

namespace devtool {

class InspectorLogAgent : public CDPDomainAgentBase {
 public:
  explicit InspectorLogAgent(
      const std::shared_ptr<LynxDevToolMediator>& devtool_mediator);
  virtual ~InspectorLogAgent();
  virtual void CallMethod(const std::shared_ptr<MessageSender>& sender,
                          const Json::Value& message) override;
  void SendLog(const std::shared_ptr<MessageSender>& sender,
               const lynx::piper::ConsoleMessage& message);

 private:
  typedef void (InspectorLogAgent::*LogAgentMethod)(
      const std::shared_ptr<MessageSender>& sender, const Json::Value& message);
  void Enable(const std::shared_ptr<MessageSender>& sender,
              const Json::Value& message);
  void Disable(const std::shared_ptr<MessageSender>& sender,
               const Json::Value& message);
  void Clear(const std::shared_ptr<MessageSender>& sender,
             const Json::Value& message);

  std::map<std::string, LogAgentMethod> functions_map_;
  const std::shared_ptr<LynxDevToolMediator> devtool_mediator_;
};
}  // namespace devtool
}  // namespace lynx

#endif  // DEVTOOL_LYNX_DEVTOOL_AGENT_DOMAIN_AGENT_INSPECTOR_LOG_AGENT_H_
