# Copyright 2021 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.
import("//lynx/config.gni")

# Targets needed for isolate script to execute.
group("testing") {
  testonly = true
  deps = [
    "//lynx/testing/lynx:lynx_test",
    "//lynx/testing/lynx_devtool:devtool_unit_test",
  ]
}

config("lynx_unit_test_config") {
  defines = [ "LYNX_UNIT_TEST=1" ]

  if (is_mac) {
    # On MacOS, NapiBindingCore source files create JSC runtime by default.
    # And consequently all unittests should link with JSC framework and
    # depends on NapiBindingJSC.
    frameworks = [ "JavaScriptCore.framework" ]
  }
}
