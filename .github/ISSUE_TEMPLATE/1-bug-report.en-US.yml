name: '🐞 Bug Report'
description: Report a bug to Lynx
title: '[Bug]: '
type: Bug
labels: ['pending triage']
body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to report this issue! Before submitting, please note:

         - Confirm that your problem cannot be solved by official documentation.
         - Make sure you've searched in the [Issues](https://github.com/lynx-family/lynx/issues) and haven't found the same issue.
         - If it's not bug report, please post on the [Discussions](https://github.com/lynx-family/lynx/discussions).

  - type: textarea
    id: system-info
    attributes:
      label: System Info
      description: Output of `npx envinfo --system --npmPackages '@lynx-js/*' --binaries`
      placeholder: |
        System:
        Binaries:
        npmPackages:
    validations:
      required: true

  - type: textarea
    id: details
    attributes:
      label: Details
      description: Please describe the bug, it would be better to provide some screenshots.
    validations:
      required: true

  - type: input
    id: reproduce
    attributes:
      label: Reproduce link
      description: 'Please provide a URL of the repository that reproduces the problem. We recommend the [Lynx repro template](https://github.com/lynx-family/lynx-repro) for creating a minimal reproducible example.'
      placeholder: paste link here

  - type: textarea
    id: reproduce-steps
    attributes:
      label: Reproduce Steps
      description: Please provide the simplest steps so that we can quickly reproduce the problem.
      placeholder: |
        For example:
        1. Run `npm run dev`
        2. Find some error messages
    validations:
      required: true
