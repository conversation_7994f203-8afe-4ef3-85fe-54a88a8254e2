name: install common dependencies

description: pull common source and binary dependencies via habitat.

inputs:
  concurrency:
    description: concurrency of requests
    default: '2'
    required: false
  cache-backend:
    description: cache backend for habitat
    default: 'lynx-infra'
    required: false

runs:
  using: composite
  steps:
    - name: setup cache action
      if: ${{ inputs.cache-backend == 'lynx-infra' }}
      uses: lynx-infra/cache@main
      with:
        path: ~/.habitat_cache
        key: ${{ runner.os }}-${{ hashFiles('lynx/.habitat', 'lynx/DEPS') }}
        restore-keys: |
          ${{ runner.os }}-

    - name: setup cache action
      if: ${{ inputs.cache-backend == 'github' }}
      uses: actions/cache@v4
      with:
        path: ~/.habitat_cache
        key: ${{ runner.os }}-${{ hashFiles('lynx/.habitat', 'lynx/DEPS') }}
        restore-keys: |
          ${{ runner.os }}-

    - name: install Python dependencies
      shell: bash
      run: |
        if [ "$RUNNER_OS" == "macOS" ]; then
          python3 -m pip install PyYAML --break-system-packages
          python3 -m pip install requests --break-system-packages
        else
          python3 -m pip install PyYAML
          python3 -m pip install requests
        fi

    - name: run habitat sync
      shell: bash
      working-directory: lynx
      run: tools/hab sync .
      env:
        HABITAT_CONCURRENCY: ${{ inputs.concurrency }}
