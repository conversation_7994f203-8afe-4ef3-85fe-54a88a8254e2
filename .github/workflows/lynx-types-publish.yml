name: lynx_types_publish

on: workflow_dispatch

jobs:
  lynx_types_publish:
    runs-on: macOS-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Download Source
        uses: actions/checkout@v4.2.2
        with:
          path: lynx
      - name: Python Setup
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
      - name: Install Common Dependencies
        uses: ./lynx/.github/actions/common-deps
        with:
          cache-backend: github
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          registry-url: 'https://registry.npmjs.org'
      - uses: pnpm/action-setup@v4.0.0
        with:
          version: 7.33.6
      - name: Publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.REPO_LYNX_TYPES_NPM_TOKEN }}
        run: |-
          cd $GITHUB_WORKSPACE/lynx
          source tools/envsetup.sh
          pushd js_libraries/types
          npm publish --access public
          popd
