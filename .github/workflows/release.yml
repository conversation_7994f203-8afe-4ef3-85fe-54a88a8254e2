name: release
on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'release tag'
        required: false
        type: string
  release:
    types: [published]

jobs:
  get-version:
    runs-on: lynx-ubuntu-22.04-avd-medium
    outputs:
      version: ${{ steps.get_version.outputs.VERSION }}
    steps:
      - name: Get Version
        id: get_version
        run: |-
          if [ ${{ github.event_name }} == 'workflow_dispatch' ]; then
            version=${{ github.event.inputs.tag }}
          else
            version=$(echo ${{ github.ref }} | awk -F "/" '{print $3}')
          fi
          if [[ $version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ || \
                $version =~ ^[0-9]+\.[0-9]+\.[0-9]+-rc\.[0-9]+$ || \
                $version =~ ^[0-9]+\.[0-9]+\.[0-9]+-alpha\.[0-9]+$ ]]; then
            echo "Version is valid"
            echo "VERSION=$version" >> $GITHUB_OUTPUT;
          else
            echo "Version is invalid"
            exit 1
          fi
  release:
    uses: ./.github/workflows/publish-release.yml
    needs: get-version
    with:
      dry-run: false
      version: ${{ needs.get-version.outputs.version }}
    permissions:
      contents: write
    secrets: inherit
