{
    global:
        _ZN4lynx5trace24TraceEventImplementationEPKcRKNS_8perfetto12CounterTrackENS0_14TraceEventTypeERK?S9_;
        _ZN4lynx5trace24TraceEventImplementationEPKcS2_NS0_14TraceEventTypeEPKNS_8perfetto5TrackERK?RKNSt6__ndk18functionIFvNS4_12EventContextEEEE;
        _ZN4lynx5trace24TraceEventImplementationEPKcRKNSt6__ndk112basic_stringIcNS3_11char_traitsIcEENS3_9allocatorIcEEEENS0_14TraceEventTypeEPKNS_8perfetto5TrackERK?RKNS3_8functionIFvNSD_12EventContextEEEE;
        _ZN4lynx5trace20InstanceCounterTrace5impl_E;
        _ZN4lynx5trace9GetFlowIdEv;
        _ZN4lynx5trace25TraceEventCategoryEnabledEPKc;
        _ZN4lynx5trace15TraceController8InstanceEv;
        _ZN4lynx5trace19TraceRuntimeProfileERKNSt6__ndk112basic_stringIcNS1_11char_traitsIcEENS1_9allocatorIcEEEE?i;
        _ZN4lynx8perfetto10TrackEvent12add_flow_idsE?;
        _ZN4lynx8perfetto10TrackEvent25set_timestamp_absolute_usE?;
        _ZN4lynx8perfetto10TrackEvent21add_debug_annotationsEv;
        _ZN4lynx8perfetto10TrackEvent24add_terminating_flow_idsE?;
        _ZN4lynx8perfetto10TrackEvent21add_debug_annotationsERKNSt6__ndk112basic_stringIcNS2_11char_traitsIcEENS2_9allocatorIcEEEESA_;
        _ZN4lynx8perfetto10TrackEvent14set_track_uuidE?;
        _ZN4lynx8perfetto10TrackEvent8set_nameERKNSt6__ndk112basic_stringIcNS2_11char_traitsIcEENS2_9allocatorIcEEEE;
        _ZN4lynx8perfetto10TrackEvent21add_debug_annotationsEONSt6__ndk112basic_stringIcNS2_11char_traitsIcEENS2_9allocatorIcEEEES9_;
        _ZN4lynx8perfetto10TrackEvent16set_legacy_eventEv;
        _ZN4lynx8perfetto11ThreadTrack7CurrentEv;
        _ZN4lynx8perfetto19LynxDebugAnnotation16set_double_valueEd;
        _ZN4lynx8perfetto19LynxDebugAnnotation13set_int_valueE?;
        _ZN4lynx8perfetto19LynxDebugAnnotation14set_uint_valueE?;
        _ZN4lynx8perfetto19LynxDebugAnnotation21set_legacy_json_valueERKNSt6__ndk112basic_stringIcNS2_11char_traitsIcEENS2_9allocatorIcEEEE;
        _ZN4lynx8perfetto19LynxDebugAnnotation14set_bool_valueEb;
        _ZN4lynx8perfetto19LynxDebugAnnotation16set_string_valueEPKc?;
        _ZN4lynx8perfetto19LynxDebugAnnotation8set_nameERKNSt6__ndk112basic_stringIcNS2_11char_traitsIcEENS2_9allocatorIcEEEE;
        _ZN4lynx8perfetto19LynxDebugAnnotation16set_string_valueERKNSt6__ndk112basic_stringIcNS2_11char_traitsIcEENS2_9allocatorIcEEEE;
        _ZN4lynx8perfetto22TrackEvent_LegacyEvent11set_bind_idE?;
        _ZN4lynx8perfetto22TrackEvent_LegacyEvent18set_flow_directionENS0_13FlowDirectionE;
        _ZN4lynx8perfetto22TrackEvent_LegacyEvent15set_unscoped_idE?;
        _ZN4lynx8perfetto22TrackEvent_LegacyEvent9set_phaseEi;
        TraceCounterEx;
        TraceEventBeginEx;
        TraceEventEndEx;
        JNI_OnLoad;
        _ZN4lynx5trace15TraceEventBeginEPKc;
        _ZN4lynx5trace13TraceEventEndEPKc?;
        _ZN4lynx5trace13TraceEventEndEv;
        _ZTVN4lynx5trace15TraceControllerE;
        _ZN4lynx5trace15TraceEventBeginEPKc?;
        _ZN4lynx5trace15TraceEventBeginERKNSt6__ndk112basic_stringIcNS1_11char_traitsIcEENS1_9allocatorIcEEEE?;
        _ZN4lynx5trace15TraceEventBeginIDnJEEEvRKT_DpOT0_;
        _ZN4lynx5trace15TraceEventBeginERKNSt6__ndk112basic_stringIcNS1_11char_traitsIcEENS1_9allocatorIcEEEE;
    local:
        *;
};