# Copyright 2024 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//build/toolchain/clang.gni")
import("//lynx/config.gni")
import("//lynx/tools/gn_tools/cmake_target_template.gni")

assert(is_android)

config("trace_android_private_config") {
  ldflags = [
    "-Wl,--exclude-libs,ALL,--gc-sections",
    "-fuse-ld=lld",
    "-Xlinker",
    "-Map=output.map",
  ]

  # Specify the symbols that need to be exported in order to hide symbols like `__emutls_get_address` that are not intended to be exported.
  # lld will export `__emutls_get_address` with enable lto, it's a bug of lld.
  ldflags +=
      [ "-Wl,--version-script," + rebase_path("lynx_trace_export_symbol.map") ]
}

cmake_target("LynxTrace") {
  cmake_version = "3.4.1"
  target_type = "shared_library"
  output_name = "lynxtrace"

  deps = [
    ":trace_jni_files",
    "//lynx/base/trace/native:trace",
  ]

  libs = [ "log" ]

  configs = [ ":trace_android_private_config" ]
}

group("trace_jni_files") {
  exec_script("//lynx/tools/build_jni/generate_and_register_jni_files.py",
              [
                "-root",
                rebase_path("//"),
                "-path",
                rebase_path("./src/main/jni/jni_configs.yml"),
                "--use-base-jni-header",
              ])
  public_deps = [ "src/main/jni:build" ]
}
