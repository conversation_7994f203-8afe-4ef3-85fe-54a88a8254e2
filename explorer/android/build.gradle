// Copyright 2025 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
buildscript {
    ext {
        kotlin_version = '1.5.32'
        minSdkVersion = 21
        compileSdkVersion = 33
        targetSdkVersion = 29
        buildToolsVersion = '29.0.2'
        ndkVersion = ndk_version
        enable_coverage_bool = (enable_coverage == 'true')

        if (project.hasProperty("buildLynxDebugSo")) {
            buildLynxDebugSo = true
        } else {
            buildLynxDebugSo = false
        }

        value = project.properties["abiList"]
        abiList = value? value.toString().split(",") : ["arm64-v8a"]

        if (project.hasProperty("IntegrationTest")) {
            buildIntegrationDemo = true
        } else {
            buildIntegrationDemo = false
        }
    }
    repositories {
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.0'
        classpath "com.android.tools:r8:3.1.51"
        classpath 'de.undercouch:gradle-download-task:3.1.2'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files

        classpath 'com.neenbedankt.gradle.plugins:android-apt:1.8'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

plugins {
    id "org.jetbrains.kotlin.android" version "1.6.21" apply false
}

allprojects {
    repositories {
        maven { url 'http://pixel.sankuai.com/repository/thirdparty/' }
        maven { url "http://pixel.sankuai.com/repository/mtdp" }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        mavenCentral()
        google()
    }
}

ext.getCppLib = {
    if (project.hasProperty('use_cpp_shared') && project.property('use_cpp_shared') == 'true') {
        return 'c++_shared'
    }
    if (project.hasProperty('use_cpp_static') && project.property('use_cpp_static') == 'true') {
        return 'c++_static'
    }
    return 'c++_static'
}

ext.getFlavorNamesAndBuildTypes = { Project project ->
    def flavorNames = []
    def buildTypes = []
    project.android.productFlavors.all { flavor ->
        flavorNames << flavor.name
    }
    project.android.buildTypes.all { buildType ->
        buildTypes << buildType.name
    }
    def runTasks = gradle.startParameter.taskNames.toString().toLowerCase()
    if (runTasks.contains("generateAllGnCmakeTargets".toLowerCase()) || runTasks.contains("clean")) {
        return [flavorNames, buildTypes]
    }
    // To reduce the generation of cmake scripts, the following types that will
    // not be used are excluded.
    // Notice: We can't exclude flavorNames because not all the flavors are the same in all projects.
    if (!(runTasks.contains("release") || runTasks.contains("publish"))) {
        buildTypes -= "release"
    }
    if (!runTasks.contains("debug")) {
        buildTypes -= "debug"
    }
    return [flavorNames, buildTypes]
}

ext.writeGnArgs = { String gnArgs ->
    exec {
        workingDir "../../"
        commandLine "python3", "tools/android_tools/write_gn_args.py", "--gn-args", gnArgs.replaceAll('"', '#')
    }
}

if (buildLynxDebugSo) {
    // force Gradle to evaluate the child build.gradle files before the parent
    evaluationDependsOnChildren()

    Project lynxTrace = project(':LynxTrace')
    Project lynxAndroid = project(':LynxAndroid')
}


task generateAllGnCmakeTargets {
    // force Gradle to evaluate the child build.gradle files before the parent
    evaluationDependsOnChildren()

    rootProject.allprojects { Project project -> 
        if(project.tasks.findByName('configGnCompileParas')) {
            println project.name + " has configGnCompileParas task."
            dependsOn project.configGnCompileParas
        }
    }
//    exec {
//        workingDir "../../"
//        commandLine "python3", "tools/android_tools/generate_cmake_scripts_by_gn.py"
//        println commandLine
//    }
}

task buildHomePage {
    description "Build home page card."
    exec {
        workingDir "../homepage"
        commandLine "python3", "build.py"
        println commandLine
    }
    copy {
        from "../homepage/dist/"
        into "lynx_explorer/src/main/assets"
        include 'main.lynx.bundle'
        rename { String fileName ->
            'homepage.lynx.bundle'
        }
    }
}

task buildShowcase {
    description "Build showcase card."
    exec {
        workingDir "../showcase"
        commandLine "python3", "./build_and_copy.py"
        println commandLine
    }
}

buildShowcase.onlyIf { build_showcase == 'true' }

if (buildIntegrationDemo) {
    task buildIntegrationDemo {
        description "Build Integeration Test Demo Card."
        exec {
            workingDir "../../testing/integration_test/demo_pages"
            commandLine "python3", "./build_and_copy.py"
            println commandLine
        }
    }
}

task clean(type: Delete) {
    dependsOn generateAllGnCmakeTargets
    dependsOn subprojects*.tasks*.matching { it.name.contains('clean') }
    doLast {
        exec {
            workingDir "../../"
            commandLine "python3", "tools/android_tools/generate_cmake_scripts_by_gn.py", "--clean"
            println commandLine
        }
    }
}
