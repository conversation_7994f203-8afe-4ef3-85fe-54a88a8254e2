<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    tools:ignore="MissingDefaultResource"
    android:gravity="center|center_horizontal|center_vertical">

    <TextView
        android:id="@+id/textView2"
        android:layout_width="300dp"
        android:layout_height="95dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="260dp"
        android:layout_marginEnd="55dp"
        android:layout_marginRight="55dp"
        android:layout_marginBottom="376dp"
        android:lineHeight="30dp"
        android:text="TextView"
        android:textAlignment="center"
        android:textSize="30dp" />

    <ProgressBar
        android:id="@+id/progressBar2"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="238dp"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="87dp"
        android:layout_marginLeft="87dp"
        android:layout_marginTop="258dp"
        android:layout_marginEnd="86dp"
        android:layout_marginRight="86dp"
        android:layout_marginBottom="257dp"
        android:maxWidth="500dip"
        android:minWidth="500dip" />

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="91dp"
        android:layout_height="86dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="101dp"
        android:layout_marginEnd="160dp"
        android:layout_marginRight="160dp"
        app:srcCompat="@drawable/lynx" />


</RelativeLayout>