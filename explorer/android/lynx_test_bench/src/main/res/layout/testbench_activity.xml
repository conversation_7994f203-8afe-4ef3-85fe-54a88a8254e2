<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <support.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimaryDark"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        >
        <ImageView
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:src="@drawable/lynx"
            android:padding="8dp"
            android:layout_gravity="left"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:text="@string/app_name"
            android:textSize="22dp"
            android:textColor="#000000"
            android:gravity="center"
            android:layout_gravity="left"/>
    </support.appcompat.widget.Toolbar>
    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" />
</LinearLayout>
