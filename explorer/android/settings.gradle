// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

// 添加仓库配置
pluginManagement {
    repositories {
        maven { url 'http://pixel.sankuai.com/repository/thirdparty/' }
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

rootProject.name = "Explorer"
include ':LynxExplorer'
project(":LynxExplorer").projectDir = new File(rootProject.projectDir, './lynx_explorer')

//include ':LynxTrace'
//project(":LynxTrace").projectDir = new File(rootProject.projectDir, '../../base/trace/android')
//include ':LynxAndroid'
//project(":LynxAndroid").projectDir = new File(rootProject.projectDir, '../../platform/android/lynx_android')
//include ':LynxJSSDK'
//project(":LynxJSSDK").projectDir = new File(rootProject.projectDir, '../../platform/android/lynx_js_sdk')
include ':LynxProcessor'
project(":LynxProcessor").projectDir = new File(rootProject.projectDir, '../../platform/android/lynx_processor')
