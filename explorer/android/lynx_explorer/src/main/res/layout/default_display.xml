<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".LynxViewShellActivity">
    <support.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimaryDark"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        >
        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:textSize="18sp"
            android:textColor="#000000"
            android:gravity="center_vertical|center_horizontal"
            android:layout_gravity="center_vertical|center_horizontal"
        />
    </support.appcompat.widget.Toolbar>
    <FrameLayout
        android:id="@+id/lynx_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</LinearLayout>
