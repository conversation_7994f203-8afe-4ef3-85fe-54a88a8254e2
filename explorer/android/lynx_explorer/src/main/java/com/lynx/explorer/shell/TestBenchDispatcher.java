// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
package com.lynx.explorer.shell;

import android.content.Context;
import java.util.Map;

public class TestBenchDispatcher extends TemplateDispatcher {
  @Override
  protected void pageRedirection(String url, Context ctx, int activityLaunchFlags,
      Map.Entry<String, TemplateDispatcher> entry) {
    // 空实现
  }

  @Override
  public boolean checkUrl(String url) {
    // 始终返回false，不处理任何URL
    return false;
  }
}
