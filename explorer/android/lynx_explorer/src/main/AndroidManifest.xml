<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.lynx.explorer">
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.CAMERA"/>
  <application
    android:name=".ExplorerApplication"
    android:allowBackup="true"
    android:icon="@drawable/explorer"
    android:label="@string/app_name"
    android:roundIcon="@drawable/explorer"
    android:supportsRtl="true"
    android:theme="@style/AppTheme"
    android:hardwareAccelerated="true"
    android:usesCleartextTraffic="true"
    tools:replace="android:appComponentFactory" android:appComponentFactory="androidx.core.app.CoreComponentFactory">
    <activity
      android:name=".LynxViewShellActivity"
      android:screenOrientation="portrait"
      android:exported="true"
      android:theme="@style/LynxShellTheme">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>
    <activity
      android:name=".scan.QRScanActivity"
      android:screenOrientation="portrait"
      android:exported="true">
    </activity>

    <activity
      android:name="com.lynx.testbench.TestBenchActivity"
      android:exported="true"
      android:theme="@style/LynxShellTheme">
    </activity>

    <!-- 添加ErrInfoActivity的exported属性 -->
    <activity
      android:name="com.sankuai.meituan.arbiter.hook.ErrInfoActivity"
      android:exported="true"
      tools:node="merge">
      <intent-filter>
        <action android:name="com.sankuai.meituan.arbiter.error" />
        <category android:name="android.intent.category.DEFAULT" />
      </intent-filter>
    </activity>
  </application>

</manifest>
