// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'


android {
    compileSdkVersion rootProject.ext.compileSdkVersion

    defaultConfig {
        applicationId "com.lynx.explorer"
        minSdkVersion 24
        targetSdkVersion 32
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "support.test.runner.AndroidJUnitRunner"

      packagingOptions {
        pickFirst 'lib/*/libc++_shared.so'
        pickFirst 'lib/*/liblynx.so'
        pickFirst 'lib/*/liblynxtrace.so'
        pickFirst '**/libnapi.so'
        pickFirst '**/libquick.so'
        pickFirst '**/liblynx_v8_bridge.so'
      }
    }

    signingConfigs {
        release {
            storeFile file(MYAPP_RELEASE_KEYSTORE_FILE)
            storePassword MYAPP_RELEASE_STORE_PASSWORD
            keyAlias MYAPP_RELEASE_KEY_ALIAS
            keyPassword MYAPP_RELEASE_KEY_PASSWORD
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    flavorDimensions "lynx"
    productFlavors {
        asan {
            dimension "lynx"
            matchingFallbacks = ["asan"]
        }
        noasan {
            dimension "lynx"
            matchingFallbacks= ["noasan"]
            getIsDefault().set(true)
        }
    }

    sourceSets {
        asan {
            jni.srcDirs = ["../../core/build/gen"]
        }
        noasan {
            jni.srcDirs = ["../../core/build/gen"]
        }

    }

    lintOptions {
             checkReleaseBuilds false
             abortOnError false
    }
}

dependencies {
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.android.support:support-core-utils:28.0.0'
    implementation 'com.facebook.fresco:fresco:1.10.0'
    implementation 'com.facebook.fresco:animated-gif:1.10.0'
    implementation 'com.facebook.fresco:animated-webp:1.10.0'
    implementation 'com.facebook.fresco:webpsupport:1.10.0'
    implementation 'com.facebook.fresco:animated-base:1.10.0'

    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation "com.squareup.okhttp3:okhttp:4.9.0"
    implementation 'com.squareup.okhttp3:okhttp-urlconnection:4.4.0'
    implementation 'com.squareup.retrofit2:retrofit:2.7.0'
    implementation 'org.lynxsdk.lynx:v8so:11.1.277.3'

    // 使用LynxAndroid的远程包
  implementation ('com.meituan.android.msc:msc-util:1.67.14'){
    exclude group: 'com.google.code.gson', module: 'gson'
    exclude group: 'com.squareup.okhttp', module: 'okhttp'
    exclude group: 'com.squareup.okhttp', module: 'okhttp-mt'
  }
  implementation ('com.meituan.lynx:lynx_android:1.0.2-SNAPSHOT-202506281928') {
    exclude group: 'com.squareup.okhttp', module: 'okhttp'
    exclude group: 'com.squareup.okhttp', module: 'okhttp-mt'
  }
//    implementation 'com.meituan.lynx:lynx_android:1.3.0-SNAPSHOT-20250627143409'
    implementation 'com.meituan.lynx:lynx_js_sdk:1.2.13-SNAPSHOT-20250618'
    implementation 'com.meituan.lynx:lynx_trace:1.0.2-SNAPSHOT-202506281811'
//    //implementation "org.lynxsdk.lynx:lynx-jssdk:3.2.0"
//    //implementation "org.lynxsdk.lynx:lynx-trace:3.2.0"
    implementation "org.lynxsdk.lynx:primjs:2.12.0"
//    implementation project(':LynxAndroid')
//    implementation project(':LynxJSSDK')
//    implementation project(':LynxJSSDK')

//    implementation project(':lynx_service_log')
//    implementation project(':lynx_service_image')
//    implementation project(':lynx_service_devtool')
//    implementation project(':lynx_service_http')
//    implementation project(':LynxDevtool')

    kapt project(':LynxProcessor')
    compileOnly project(':LynxProcessor')
}
