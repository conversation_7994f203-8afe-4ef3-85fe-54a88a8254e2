# Copyright 2021 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//${lynx_dir}/build_overrides/darwin.gni")

common_lynx_public_headers =
    get_path_info([
                    "LynxTemplateRenderDelegate.h",
                    "LynxTemplateRenderDelegateExternal.h",
                    "LynxDynamicComponentFetcher.h",
                    "LynxEnv.h",
                    "LynxEnvKey.h",
                    "base/LynxLog.h",
                    "LynxViewInternal.h",
                    "LynxTheme.h",
                    "LynxView+Identify.h",
                    "LynxTemplateData.h",
                    "LynxError.h",
                    "LynxErrorCode.h",
                    "LynxErrorBehavior.h",
                    "LynxSubErrorCode.h",
                    "LynxTemplateBundle.h",
                    "LynxGlobalObserver.h",
                    "LynxViewClient.h",
                    "module/LynxModule.h",
                    "module/JSModule.h",
                    "module/LynxSetModule.h",
                    "module/LynxContextModule.h",
                    "module/LynxExtensionModule.h",
                    "LynxDebugger.h",
                    "LynxTemplateBundleOption.h",
                    "utils/LynxVersionUtils.h",
                    "utils/LynxHtmlEscape.h",
                    "utils/LynxWeakProxy.h",
                    "utils/LynxUnitUtils.h",
                    "utils/thread_safe_dictionary/LynxThreadSafeDictionary.h",
                    "utils/LynxSizeValue.h",
                    "utils/LynxColorUtils.h",
                    "LynxContext.h",
                    "devtool_wrapper/LynxBaseInspectorOwnerNG.h",
                    "devtool_wrapper/LynxBaseInspectorOwner.h",
                    "devtool_wrapper/LynxMemoryListener.h",
                    "devtool_wrapper/LynxDevtool+Internal.h",
                    "devtool_wrapper/LynxDevtool.h",
                    "devtool_wrapper/LynxDevToolUtils.h",
                    "devtool_wrapper/CustomizedMessage.h",
                    "devtool_wrapper/LynxBaseLogBoxProxy.h",
                    "devtool_wrapper/DevToolOverlayDelegate.h",
                    "devtool_wrapper/OverlayService.h",
                    "LynxScreenMetrics.h",
                    "event_report/LynxEventReporter.h",
                    "LynxUpdateMeta.h",
                    "LynxViewClientV2.h",
                    "LynxServiceInfo.h",
                    "LynxTemplateProvider.h",
                    "navigator/LynxHolder.h",
                    "navigator/LynxCardManager.h",
                    "navigator/cache/LynxLruCacheNode.h",
                    "navigator/cache/LynxLruCache.h",
                    "navigator/NavigationModule.h",
                    "navigator/LynxNavigator.h",
                    "navigator/LynxSchemaInterceptor.h",
                    "navigator/LynxRoute.h",
                    "navigator/LynxBaseCardManager.h",
                    "LynxTemplateRenderProtocol.h",
                    "service/LynxServiceResourceResponseProtocol.h",
                    "service/LynxServiceContainerProtocol.h",
                    "service/LynxServiceHttpProtocol.h",
                    "service/LynxServiceLogProtocol.h",
                    "service/LynxServiceResourceProtocol.h",
                    "service/LynxServiceResourceRequestOperationProtocol.h",
                    "service/LynxServiceTrailProtocol.h",
                    "service/LynxServiceModuleProtocol.h",
                    "service/LynxServiceEventReporterProtocol.h",
                    "service/LynxServiceExtensionProtocol.h",
                    "service/LynxServiceResourceRequestParameters.h",
                    "service/LynxServiceImageProtocol.h",
                    "service/LynxServiceMonitorProtocol.h",
                    "service/LynxServiceI18nProtocol.h",
                    "service/LynxServiceDevToolProtocol.h",
                    "service/LynxServiceSecurityProtocol.h",
                    "service/LynxService.h",
                    "service/LynxServiceSystemInvokeProtocol.h",
                    "service/LynxServiceProtocol.h",
                    "resource/LynxProviderRegistry.h",
                    "resource/LynxMediaResourceFetcher.h",
                    "resource/LynxTemplateResourceFetcher.h",
                    "resource/LynxResourceResponseDataInfoProtocol.h",
                    "resource/LynxResourceRequest.h",
                    "resource/LynxResourceProvider.h",
                    "resource/LynxTemplateResource.h",
                    "resource/LynxGenericResourceFetcher.h",
                    "resource/LynxResourceResponse.h",
                    "performance/LynxExtraTiming.h",
                    "event/LynxEvent.h",
                    "event/LynxEventTarget.h",
                    "event/LynxEventTargetBase.h",
                    "event/LynxTouchEvent.h",
                    "LynxLoadMeta.h",
                    "LynxHttpRequest.h",
                    "LynxConfig.h",
                    "LynxConfigInfo.h",
                    "base/LynxComponentRegistry.h",
                    "base/LynxComponent.h",
                    "base/LynxPerformance.h",
                    "base/LynxConverter+LynxCSSType.h",
                    "base/LynxForegroundProtocol.h",
                    "base/LynxClassAliasDefines.h",
                    "base/LynxExternalResourceFetcherWrapper.h",
                    "base/LynxPageReloadHelperProto.h",
                    "base/LynxPageReloadHelper.h",
                    "base/LynxConverter+UI.h",
                    "base/LynxConverter.h",
                    "base/LynxDefines.h",
                    "base/LynxBooleanOption.h",
                    "base/LynxResourceServiceFetcher.h",
                    "base/LynxThreadManager.h",
                    "base/LynxCSSType.h",
                    "base/LynxAutoGenCSSType.h",
                    "base/LynxBacktrace.h",
                    "base/LynxPageReloadHelper+Internal.h",
                    "base/LynxVersion.h",
                    "base/LynxResourceFetcher.h",
                    "LynxViewEnum.h",
                    "LynxLifecycleDispatcher.h",
                  ],
                  "abspath")

performanceentry_header_lists = exec_script(
        "//build/ls.py",
        [
          "--target-directory",
          rebase_path(
              "//lynx/platform/darwin/common/lynx/public/performance/performance_observer"),
        ],
        "list lines")
common_lynx_public_headers += performanceentry_header_lists

common_lynx_public_headers += lynx_public_extend_sources
