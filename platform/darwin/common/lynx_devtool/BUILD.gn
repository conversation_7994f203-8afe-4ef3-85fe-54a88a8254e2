# Copyright 2022 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.
import("//${lynx_dir}/build_overrides/darwin.gni")
import("//lynx/devtool/lynx_devtool/devtool.gni")
import("//lynx/tools/gn_tools/podspec_target_template.gni")

config("public_config") {
  include_dirs = [
    ".",
    "Helper",
    "Tracing",
    "//lynx/base/trace/darwin",
  ]
  cflags = []
  if (is_ios) {
    include_dirs += [
      "//platform/darwin/ios/Headers/Private",
      "//platform/darwin/ios/lynxDevtool",
      "//platform/darwin/ios/lynxDevtool/Helper",
      "//platform/darwin/ios/lynx/Base",
      "//platform/darwin/ios/public/base",
    ]
    cflags += [
      "-include",
      rebase_path("LynxDevTool-prefix-ios.pch", root_build_dir),
    ]
  }
}

# Please put public headers on this _public_headers list.
# These public headers will be added to the public_header_files variable in the podspec
devtool_public_headers = [
  "Helper/LynxBaseDeviceInfo.h",
  "Helper/LynxScreenCastHelper.h",
  "LynxDebugBridge.h",
  "LynxDevMenu.h",
  "LynxDevtoolEnv.h",
  "LynxDevToolFrameCapturer.h",
  "LynxInspectorConsoleDelegate.h",
  "LynxInspectorOwner+Internal.h",
  "Memory/LynxMemoryController.h",
  "Module/LynxDevToolSetModule.h",
  "Module/LynxTrailModule.h",
  "Module/LynxWebSocketModule.h",
  "Tracing/LynxFPSTrace.h",
  "Tracing/LynxFrameTraceService.h",
  "Tracing/LynxFrameViewTrace.h",
  "Tracing/LynxInstanceTrace.h",
]

subspec_target("devtool") {
  sources = [
    "DevToolOverlayDelegate.mm",
    "Helper/LynxScreenCastHelper.mm",
    "LynxDebugBridge.mm",
    "LynxDevMenu.mm",
    "LynxDevToolFrameCapturer.mm",
    "LynxDevtoolEnv.mm",
    "Memory/LynxMemoryController.mm",
    "Module/LynxDevToolSetModule.mm",
    "Module/LynxTrailModule.mm",
    "Module/LynxWebSocketModule.mm",
    "Tracing/LynxFPSTrace.mm",
    "Tracing/LynxFrameTraceService.mm",
    "Tracing/LynxFrameViewTrace.mm",
    "Tracing/LynxInstanceTrace.mm",
  ]
  sources += devtool_public_headers
  public_header_files = devtool_public_headers
}
devtool_source_set("logbox") {
  include_dirs = [
    ".",
    "LogBox",
  ]

  sources = [
    "LogBox/DevToolLogBoxEnv.h",
    "LogBox/DevToolLogBoxEnv.mm",
    "LogBox/LogBoxFileLoadUtils.h",
    "LogBox/LogBoxFileLoadUtils.mm",
    "LogBox/LynxLogBox.h",
    "LogBox/LynxLogBox.mm",
    "LogBox/LynxLogBoxHelper.h",
    "LogBox/LynxLogBoxManager.h",
    "LogBox/LynxLogBoxManager.m",
    "LogBox/LynxLogBoxOwner.h",
    "LogBox/LynxLogBoxOwner.mm",
    "LogBox/LynxLogBoxProxy.h",
    "LogBox/LynxLogBoxProxy.mm",
    "LogBox/LynxLogNotification.h",
    "LogBox/LynxLogNotification.m",
  ]

  public_configs = devtool_darwin_config
}
