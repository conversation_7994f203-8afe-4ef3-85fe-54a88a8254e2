# Copyright 2023 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//${lynx_dir}/build_overrides/darwin.gni")
import("//lynx/config.gni")
import("//lynx/tools/gn_tools/podspec_target_template.gni")

# public_shared_sources
public_shared_sources = [
  "LynxBackgroundRuntime.h",
  "LynxBlurImageProcessor.h",
  "LynxBooleanOption.h",
  "LynxFatImageProcessor.h",
  "LynxImageBlurUtils.h",
  "LynxImageLoader.h",
  "LynxImageProcessor.h",
  "LynxNinePatchImageProcessor.h",
  "LynxRuntimeLifecycleListener.h",
  "LynxUIFilterImage.h",
  "LynxUIImage.h",
  "LynxUIInlineImage.h",
  "LynxURL.h",
  "LynxWasmFuncRegistry.h",
  "animation/LynxAnimationDelegate.h",
  "animation/LynxAnimationInfo.h",
  "animation/LynxAnimationTransformRotation.h",
  "animation/LynxAnimationUtils.h",
  "animation/LynxKeyframeAnimator.h",
  "animation/LynxKeyframeManager.h",
  "animation/LynxKeyframes.h",
  "animation/LynxLayoutAnimationManager.h",
  "animation/LynxTransitionAnimationManager.h",
  "base/LynxBoxShadowManager.h",
  "base/LynxConverter+Transform.h",
  "base/LynxFontFaceManager.h",
  "base/LynxGradient.h",
  "base/LynxLayoutStyle.h",
  "base/LynxPreprocessingUtils.h",
  "base/LynxPropsProcessor.h",
  "base/LynxTextStyle.h",
  "base/LynxTextUtils.h",
  "base/LynxTransformOriginRaw.h",
  "base/LynxTransformRaw.h",
  "base/LynxUIMethodProcessor.h",
  "base/UIDevice+Lynx.h",
  "base/background/LynxBackgroundCapInsets.h",
  "base/background/LynxBackgroundDrawable.h",
  "base/background/LynxBackgroundImageLayerInfo.h",
  "base/background/LynxBackgroundInfo.h",
  "base/background/LynxBackgroundManager.h",
  "base/background/LynxBackgroundRenderer+Internal.h",
  "base/background/LynxBackgroundRenderer.h",
  "base/background/LynxBackgroundUtils.h",
  "base/background/LynxBoxShadowLayer.h",
  "event/LynxEventDetail.h",
  "event/LynxEventEmitter.h",
  "event/LynxEventHandler.h",
  "event/LynxEventSpec.h",
  "event/LynxKeyboardEventDispatcher.h",
  "event/LynxTouchHandler.h",
  "gesture/LynxBaseGestureHandler.h",
  "gesture/LynxDefaultGestureHandler.h",
  "gesture/LynxFlingGestureHandler.h",
  "gesture/LynxGestureArenaMember.h",
  "gesture/LynxGestureDetectorDarwin.h",
  "gesture/LynxLongPressGestureHandler.h",
  "gesture/LynxNativeGestureHandler.h",
  "gesture/LynxPanGestureHandler.h",
  "gesture/LynxTapGestureHandler.h",
  "hero_transitions/CALayer+LynxHeroTransition.h",
  "hero_transitions/LynxHeroAnimator.h",
  "hero_transitions/LynxHeroModifiers.h",
  "hero_transitions/LynxHeroTransition.h",
  "hero_transitions/LynxUI+LynxHeroTransition.h",
  "hero_transitions/UIView+LynxHeroTransition.h",
  "hero_transitions/UIViewController+LynxHeroTransition.h",
  "shadow_node/LynxCustomMeasureDelegate+Internal.h",
  "shadow_node/LynxCustomMeasureDelegate.h",
  "shadow_node/LynxCustomMeasureShadowNode.h",
  "shadow_node/LynxLayoutNode.h",
  "shadow_node/LynxLayoutNodeManager.h",
  "shadow_node/LynxLayoutTick.h",
  "shadow_node/LynxMeasureDelegate.h",
  "shadow_node/LynxNativeLayoutNode.h",
  "shadow_node/LynxShadowNode+VirtualAnchor.h",
  "shadow_node/LynxShadowNode.h",
  "shadow_node/LynxShadowNodeOwner.h",
  "shadow_node/LynxShadowNodeStyle.h",
  "shadow_node/text/LynxBaseTextShadowNode.h",
  "shadow_node/text/LynxBaselineShiftLayoutManager.h",
  "shadow_node/text/LynxConverter+NSShadow.h",
  "shadow_node/text/LynxEventTargetSpan.h",
  "shadow_node/text/LynxInlineTextShadowNode.h",
  "shadow_node/text/LynxInlineTruncationShadowNode.h",
  "shadow_node/text/LynxRawTextShadowNode.h",
  "shadow_node/text/LynxTextLayoutManager.h",
  "shadow_node/text/LynxTextLayoutSpec.h",
  "shadow_node/text/LynxTextRenderer.h",
  "shadow_node/text/LynxTextRendererCache.h",
  "shadow_node/text/LynxTextShadowNode.h",
  "ui/LUIBodyView.h",
  "ui/LUIConfig.h",
  "ui/LUIErrorHandling.h",
  "ui/LynxBasicShape.h",
  "ui/LynxLayer.h",
  "ui/LynxNewGestureDelegate.h",
  "ui/LynxRootUI.h",
  "ui/LynxScrollEventManager.h",
  "ui/LynxScrollListener.h",
  "ui/LynxUI+Accessibility.h",
  "ui/LynxUI+Fluency.h",
  "ui/LynxUI+Internal.h",
  "ui/LynxUI.h",
  "ui/LynxUIComponent.h",
  "ui/LynxUIContext.h",
  "ui/LynxUIExposure.h",
  "ui/LynxUIOwner.h",
  "ui/LynxUIReportInfoDelegate.h",
  "ui/LynxUITarget.h",
  "ui/LynxUIView.h",
  "ui/LynxViewVisibleHelper.h",
  "ui/UIScrollView+Lynx.h",
  "ui/UIScrollView+LynxFadingEdge.h",
  "ui/UIScrollView+LynxGesture.h",
  "ui/UIView+Lynx.h",
  "ui/frame/LynxFrameView.h",
  "ui/frame/LynxUIFrame.h",
  "ui/list/LynxListAppearEventEmitter.h",
  "ui/list/LynxListDebug.h",
  "ui/list/LynxListScrollEventEmitter.h",
  "ui/list/LynxUIListDelegate.h",
  "ui/list/LynxUIListInspector.h",
  "ui/list/LynxUIListLoader.h",
  "ui/list/LynxUIListScrollEvent.h",
  "ui/list/container/LynxUIListContainer.h",
  "ui/list/list_light/layout/LynxListHorizontalLayoutManager.h",
  "ui/list/list_light/layout/LynxListLayoutManager.h",
  "ui/list/list_light/layout/LynxListLayoutModelLight.h",
  "ui/list/list_light/layout/LynxListVerticalLayoutManager.h",
  "ui/list/list_light/ui/LynxUIListInvalidationContext.h",
  "ui/list/list_light/ui/LynxUIListLight.h",
  "ui/list/list_light/ui/LynxUIListProtocol.h",
  "ui/list/list_light/ui/LynxUIListScrollManager.h",
  "ui/list/list_light/view/LynxListViewCellLight.h",
  "ui/list/list_light/view/LynxListViewLight.h",
  "ui/list/lynx_collection/LynxCollectionDataSource.h",
  "ui/list/lynx_collection/LynxCollectionScroll.h",
  "ui/list/lynx_collection/LynxCollectionScroller.h",
  "ui/list/lynx_collection/LynxCollectionViewCell.h",
  "ui/list/lynx_collection/LynxUICollection+Delegate.h",
  "ui/list/lynx_collection/LynxUICollection+Internal.h",
  "ui/list/lynx_collection/LynxUICollection+PropSetter.h",
  "ui/list/lynx_collection/LynxUICollection.h",
  "ui/list/lynx_collection/LynxUIListItem.h",
  "ui/list/lynx_collection/layout/LynxCollectionInvalidationContext.h",
  "ui/list/lynx_collection/layout/LynxCollectionViewLayout.h",
  "ui/list/lynx_collection/layout/LynxCollectionViewLayoutModel.h",
  "ui/list/lynx_collection/layout/LynxCollectionViewLayoutSectionModel.h",
  "ui/scroll_view/AbsLynxUIScroller.h",
  "ui/scroll_view/LynxBounceView.h",
  "ui/scroll_view/LynxImpressionView.h",
  "ui/scroll_view/LynxScrollView.h",
  "ui/scroll_view/LynxUIScroller.h",
  "ui/scroll_view/UIScrollView+Nested.h",
  "ui/text/LynxTextOverflowLayer.h",
  "ui/text/LynxTextView.h",
  "ui/text/LynxUIText.h",
  "utils/LynxGradientUtils.h",
  "utils/LynxPropertyDiffMap.h",
  "utils/LynxUIKitAPIAdapter.h",
  "utils/LynxUIUnitUtils.h",
  "utils/LynxViewConfigProcessor.h",
]

public_shared_sources += public_shared_extend_sources

# public_shared_sources end

subspec_target("Public") {
  sources = public_shared_sources
  public_header_files = public_shared_sources
}
