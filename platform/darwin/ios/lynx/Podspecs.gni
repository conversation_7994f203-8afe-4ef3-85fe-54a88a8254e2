# Copyright 2024 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/config.gni")

lynx_podspec =
    get_path_info([
                    ":Air",
                    ":Framework",
                    ":JSRuntime",
                    "//lynx/platform/darwin/common/lazy_load:LazyLoad",
                    ":LepusNG",
                    ":NapiBinding",
                    ":Native",
                    ":ReleaseResource",
                    ":Replay",
                    ":Trace",
                    ":UnitTests",
                    ":Worklet",
                    ":Inspector",
                  ],
                  "abspath")

lynx_podspec_metadata = {
  # These variables are reserved to be consistent with the original podspec.
  global_variables = [
    "\$enable_recorder = Integer(ENV[\"LYNX_ENABLE_RECORDER\"] || 0)",
    "\$enable_frozen_mode = Integer(ENV[\"LYNX_ENABLE_FROZEN\"] || 0)",
    "\$enable_e2e_test = Integer(ENV[\"LYNX_ENABLE_E2E_TEST\"] || 0)",
  ]
  if (enable_trace == "perfetto") {
    global_variables += [ "\$enable_trace = 1" ]
  } else {
    global_variables += [ "\$enable_trace = 0" ]
  }
}

lynx_podspec_deps = [
  "//lynx/platform/darwin/common/lynx:Lynx_subspec",
  "//lynx/platform/darwin/common/lynx/public:Public",
  "//lynx/platform/darwin/ios/lynx:lynx",
  "//lynx/platform/darwin/ios/lynx/public:Public",
]

framework_podspec_deps = [
  "//lynx/platform/darwin/common/lazy_load:LazyLoad",
  "//lynx/platform/darwin/common/lynx:Lynx_subspec",
  "//lynx/platform/darwin/common/lynx/public:Public",
  "//lynx/platform/darwin/ios/lynx:Inspector",
  "//lynx/platform/darwin/ios/lynx:JSRuntime",
  "//lynx/platform/darwin/ios/lynx:LepusNG",
  "//lynx/platform/darwin/ios/lynx:NapiBinding",
  "//lynx/platform/darwin/ios/lynx:Native",
  "//lynx/platform/darwin/ios/lynx:Trace",
  "//lynx/platform/darwin/ios/lynx:Worklet",
  "//lynx/platform/darwin/ios/lynx:lynx",
  "//lynx/platform/darwin/ios/lynx/public:Public",
]

native_podspec_deps = [
  "//lynx/platform/darwin/ios/lynx:amoeba",
  "//lynx/platform/darwin/ios/lynx:animation",
  "//lynx/platform/darwin/ios/lynx:animation_utils",
  "//lynx/platform/darwin/ios/lynx:base",
  "//lynx/platform/darwin/ios/lynx:basic_animation",
  "//lynx/platform/darwin/ios/lynx:fml",
  "//lynx/platform/darwin/ios/lynx:lepus",
  "//lynx/platform/darwin/ios/lynx:lynx_basic_animator",
  "//lynx/platform/darwin/ios/lynx:parser",
  "//lynx/platform/darwin/ios/lynx:resource",
  "//lynx/platform/darwin/ios/lynx:starlight",
  "//lynx/platform/darwin/ios/lynx:tasm",
  "//lynx/platform/darwin/ios/lynx:template_bundle",
  "//lynx/platform/darwin/ios/lynx:third_party",
  "//lynx/platform/darwin/ios/lynx:transforms",
]
