# Copyright 2022 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.
import("//${lynx_dir}/build_overrides/darwin.gni")
import("//lynx/devtool/lynx_devtool/devtool.gni")
import("//lynx/tools/gn_tools/podspec_target_template.gni")

assert(is_ios)

# Please put public headers on this _public_headers list.
# These public headers will be added to the public_header_files variable in the podspec
devtool_public_headers = [
  "ConsoleDelegateManager.h",
  "DevToolPlatformDarwinDelegate.h",
  "GlobalDevToolPlatformDarwinDelegate.h",
  "DevToolMonitorView.h",
  "DevtoolWebSocketModule.h",
  "Helper/LynxDeviceInfoHelper.h",
  "Helper/LynxEmulateTouchHelper.h",
  "Helper/TestBenchTraceProfileHelper.h",
  "Helper/TestbenchDumpFileHelper.h",
  "Helper/LynxUITreeHelper.h",
  "Helper/LepusDebugInfoHelper.h",
  "LynxInspectorOwner.h",
  "LynxDevToolNGDarwinDelegate.h",
  "LynxWebSocket.h",
  "LynxDevToolErrorUtils.h",
]

subspec_target("devtool") {
  sources = [
    "ConsoleDelegateManager.mm",
    "DevToolMonitorView.m",
    "DevToolPlatformDarwinDelegate.mm",
    "DevtoolWebSocketModule.m",
    "GlobalDevToolPlatformDarwinDelegate.mm",
    "Helper/LepusDebugInfoHelper.mm",
    "Helper/LynxDeviceInfoHelper.m",
    "Helper/LynxEmulateTouchHelper.mm",
    "Helper/LynxUITreeHelper.mm",
    "Helper/TestBenchTraceProfileHelper.mm",
    "Helper/TestbenchDumpFileHelper.mm",
    "LynxDevToolErrorUtils.mm",
    "LynxDevToolNGDarwinDelegate.mm",
    "LynxInspectorOwner.mm",
    "LynxWebSocket.mm",
  ]
  sources += devtool_public_headers
  sources += devtool_extend_sources
  public_header_files = devtool_public_headers
}

podspec_target("devtool_podspec") {
  output_name = "LynxDevtool.podspec"
  output_path = rebase_path("//")
  global_variables = [
    "\$package_version = ENV[\"version\"]",
    "\$is_debug_version = \$package_version&.end_with?(\"-debug\") || ENV[\"version\"]&.end_with?(\"-debug\")",
    "\$enable_e2e_test = Integer(ENV[\"LYNX_ENABLE_E2E_TEST\"] || 0)",
  ]
  if (enable_trace == "perfetto") {
    global_variables += [ "\$enable_trace = 1" ]
  } else {
    global_variables += [ "\$enable_trace = 0 " ]
  }
  root_specification = {
    name = "LynxDevtool"
    version = "$lynx_version"
    summary = "The framework of LynxDevtool."
    homepage = "https://github.com/lynx-family/lynx"
    license = "Apache 2.0"
    author = "Lynx"
    source = {
      git = "https://github.com/lynx-family/lynx.git"
    }
    requires_arc = true
    compiler_flags = [
      "-Wall",
      "-Wextra",
      "-Wno-unused-parameter",
      "-Wshorten-64-to-32",
      "-fno-rtti",
    ]
    pod_target_xcconfig = {
      GCC_PREPROCESSOR_DEFINITIONS = [
        "OS_IOS=1",
        "HOST_OSTYPE=HOST_IOS",
        "LYNX_DEBUG=0",
        "LYNX_ENABLE_E2E_TEST=#{\$enable_e2e_test}",
        "ENABLE_TRACE_PERFETTO=#{\$enable_trace}",
      ]
      CLANG_CXX_LANGUAGE_STANDARD = "gnu++17"
      OTHER_CPLUSPLUSFLAGS =
          "-fno-aligned-allocation -fno-c++-static-destructors"
    }
    prepare_command = devtool_prepare_command
    ios = {
      deployment_target = "10.0"
    }
  }
  if (use_flatten_deps) {
    deps = [
      ":Framework",
      ":LynxCore",
      ":UnitTests",
    ]
  } else {
    deps = [
      ":DebugResource",
      ":Framework",
      ":LogBoxFramework",
      ":LynxCore",
      ":Native",
      ":ThirdParty",
      ":UnitTests",
    ]
  }
}

subspec_target("DebugResource") {
  resource_bundles = [ ":LynxDebugResources" ]
}

bundle_data("LynxDebugResources") {
  sources = [
    "//lynx/platform/darwin/ios/lynx_devtool/assets/logbox",
    "//lynx/platform/darwin/ios/lynx_devtool/assets/lynx_core_dev.js",
    "//lynx/platform/darwin/ios/lynx_devtool/assets/notification_cancel.png",
    "//lynx/platform/darwin/ios/lynx_devtool/assets/switchPage",
  ]
  outputs = [ "{{bundle_resources_dir}}/{{source_file_part}}" ]
}

subspec_target("Framework") {
  compiler_flags = [
    "-Wno-documentation",
    "-Wno-deprecated",
  ]
  pod_target_xcconfig = {
    GCC_PREPROCESSOR_DEFINITIONS = [
      "RAPIDJSON_HAS_STDSTRING=1",
      "RAPIDJSON_NAMESPACE=lynx::rapidjson",
      "RAPIDJSON_NAMESPACE_BEGIN=\\\"namespace lynx{namespace rapidjson{\\\"",
      "RAPIDJSON_NAMESPACE_END=\\\"}};namespace rapidjson=::lynx::rapidjson;\\\"",
      "JS_ENGINE_TYPE=1",
      "ENABLE_INSPECTOR=1",
      "INSPECTOR_TEST=1",
      "OS_POSIX=1",
    ]
    if (!is_debug) {
      GCC_PREPROCESSOR_DEFINITIONS += [ "NDEBUG=1" ]
    }
  }
  frameworks = [
    "IOKit",
    "UIKit",
    "WebKit",
  ]
  flatten_deps = [
    ":devtool",
    "//lynx/platform/darwin/common/lynx_devtool:devtool",
  ]
  dependency = [ [
        "DebugRouter",
        ">= 5.0.11",
      ] ]
  if (use_flatten_deps) {
    flatten_deps += [
      ":DebugResource",
      ":LogBoxFramework",
      ":Native",
      ":ThirdParty",
    ]
    dependency += lynx_dependency
    dependency += basedevtool_dependency
    dependency += [
      "SocketRocket",
      "LynxDevtool/LynxCore",
    ]
  } else {
    dependency += [
      "Lynx/Framework",
      "Lynx/Native",
      "Lynx/Inspector",
      "Lynx/JSRuntime",
      "Lynx/Trace",
      "LynxDevtool/Native",
      "SocketRocket",
      "BaseDevtool",
    ]
  }
}

subspec_target("LynxCore") {
  pattern_source_files = [
    "//lynx/core/**/*.h",
    "//core/**/*.h",
  ]
  pattern_exclude_files = [
    "//core/platform/**/*",
    "//**/android/**/*",
    "//**/ios/**/*",
    "//**/darwin/**/*",
    "//**/*_android.h",
    "//**/*_darwin.h",
    "//core/build/gen/**/*",
    "//core/runtime/**/v8_*.h",
    "//lynx/core/build/gen/**/*",
    "//lynx/core/runtime/**/v8_*.h",
    "//lynx/core/template_bundle/template_codec/magic_number.h",
    "//lynx/core/runtime/bindings/jsi/interceptor/ios/request_interceptor_darwin.h",
    "//lynx/core/services/replay/lynx_module_binding_testbench.h",
  ]
  header_mappings_dir = "."
}

subspec_target("Native") {
  compiler_flags = [
    "-Wno-documentation",
    "-Wno-deprecated",
  ]
  libraries = [ "stdc++" ]
  pod_target_xcconfig = {
    HEADER_SEARCH_PATHS = Devtool_Native_includes
  }
  deps = [
    "//lynx/devtool/lynx_devtool/agent",
    "//lynx/devtool/lynx_devtool/base",
    "//lynx/devtool/lynx_devtool/common",
    "//lynx/devtool/lynx_devtool/config",
    "//lynx/devtool/lynx_devtool/element",
    "//lynx/devtool/lynx_devtool/recorder",
  ]
  if (!enable_unittests) {
    deps += [ "//lynx/core/renderer/css:css_decoder" ]
  }
  if (use_flatten_deps) {
    flatten_deps = [
      ":Trace",
      ":js_debug",
    ]
  } else {
    deps += [
      ":Trace",
      ":js_debug",
    ]
    dependency = [
      "LynxDevtool/LynxCore",
      "LynxDevtool/ThirdParty",
    ]
    header_mappings_dir = "."
  }
}

subspec_target("Trace") {
  pod_target_xcconfig = {
    HEADER_SEARCH_PATHS = [ "//" ]
  }
  deps = [
    "//lynx/base/trace/native:trace_public_headers",
    "//lynx/devtool/lynx_devtool/tracing",
  ]
  if (!use_flatten_deps) {
    dependency = [ "Lynx/Trace" ]
  }
}

subspec_target("js_debug") {
  pod_target_xcconfig = {
    GCC_PREPROCESSOR_DEFINITIONS = [
      "ENABLE_QUICKJS_DEBUGGER",
      "LYNX_DEV",
      "LYNX_SIMPLIFY=0",
    ]
    HEADER_SEARCH_PATHS = Devtool_Native_includes
  }
  deps = JSDebug_deps
  dependency = js_debug_dependency
  if (!use_flatten_deps) {
    dependency += [ "LynxDevtool/LynxCore" ]
  }
}

subspec_target("LogBoxFramework") {
  pod_target_xcconfig = {
    HEADER_SEARCH_PATHS = [ "//devtool/lynx_devtool" ]
  }
  deps = [ "//lynx/platform/darwin/common/lynx_devtool:logbox" ]
  if (!use_flatten_deps) {
    dependency = [
      "LynxDevtool/LynxCore",
      "LynxDevtool/DebugResource",
    ]
  }
}

subspec_target("ThirdParty") {
  if (!use_flatten_deps) {
    dependency = [
      "BaseDevtool/ThirdParty",
      "LynxDevtool/LynxCore",
    ]
    header_mappings_dir = "."
  }
  deps = [ ":zlib" ]
  libraries = [ "stdc++" ]
  pod_target_xcconfig = {
    HEADER_SEARCH_PATHS = [
      "//devtool/lynx_devtool",
      "//core",
      "//lynx/third_party/jsoncpp/include",
      "//PODS_ROOT/Lynx",
      "//PODS_ROOT/Lynx/lynx",
      "//lynx",
    ]
  }
}

subspec_target("zlib") {
  compiler_flags = [
    "-Wno-conversion",
    "-Wno-comma",
    "-Wno-unused-function",
    "-Wno-sign-compare",
  ]
  deps = [ "//third_party/zlib" ]
  libraries = [ "stdc++" ]
  if (!use_flatten_deps) {
    header_mappings_dir = "third_party"
  }
}

subspec_target("UnitTests") {
  test_subspec = true
  sources = [
    "//lynx/platform/darwin/common/lynx_devtool/LogBox/LynxLogBoxManagerUnitTest.m",
    "//platform/darwin/ios/lynxDevtool/ConsoleDelegateManagerUnitTest.mm",
    "//platform/darwin/ios/lynxDevtool/Helper/LepusDebugInfoHelperUnitTest.m",
    "//platform/darwin/ios/lynxDevtool/Helper/LynxUITreeHelperUnitTest.m",
    "//platform/darwin/ios/lynxDevtool/LynxDevToolErrorUtilsUnitTest.m",
    "//platform/darwin/ios/lynxDevtool/Memory/LynxMemoryControllerUnitTest.m",
  ]
  dependency = [
    "LynxDevtool/Framework",
    "XcodeCoverage",
    "OCMock",
  ]
}
