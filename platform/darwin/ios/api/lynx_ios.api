/*
 * This file is generated, do not edit.
 * @generated
 *
 * @generate-command: python3 tools/api/main.py -u
 *
 */

public struct _LynxBorderUnitValue {
  public CGFloat _LynxBorderUnitValue::val val;
  public LynxBorderValueUnit _LynxBorderUnitValue::unit unit;
}

public struct _LynxRenderBorderSideData {
  public LynxBorderStyle _LynxRenderBorderSideData::style style;
  public CGColorRef _LynxRenderBorderSideData::color color;
  public CGFloat _LynxRenderBorderSideData::length length;
  public CGFloat _LynxRenderBorderSideData::width width;
  public CGFloat _LynxRenderBorderSideData::maxWidth maxWidth;
  public CGPoint _LynxRenderBorderSideData::clipPoints[4] clipPoints;
  public CGPoint _LynxRenderBorderSideData::linePoints[2] linePoints;
  public BOOL _LynxRenderBorderSideData::isLeftOrTop isLeftOrTop;
}

public class AbsLynxUIScroller : <__covariant V UIView> {
  public void AbsLynxUIScroller::setScrollY:requestReset:(BOOL value,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::setScrollX:requestReset:(BOOL value,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::setScrollYReverse:requestReset:(BOOL value,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::setScrollXReverse:requestReset:(BOOL value,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::setScrollBarEnable:requestReset:(BOOL value,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::setUpperThreshold:requestReset:(NSInteger value,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::setLowerThreshold:requestReset:(NSInteger value,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::setScrollTop:requestReset:(int vale,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::setScrollLeft:requestReset:(int vale,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::setScrollToIndex:requestReset:(int vale,[requestReset] BOOL requestReset);
  public void AbsLynxUIScroller::scrollInto:isSmooth:blockType:inlineType:(LynxUI *value,[isSmooth] BOOL isSmooth,[blockType] NSString *blockType,[inlineType] NSString *inlineTyle);
  public void AbsLynxUIScroller::sendScrollEvent:scrollTop:scollleft:scrollHeight:scrollWidth:deltaX:deltaY:(NSString *name,[scrollTop] float top,[scollleft] float left,[scrollHeight] float height,[scrollWidth] float width,[deltaX] float x,[deltaY] float y);
  public BOOL AbsLynxUIScroller::canScroll:(ScrollDirection direction);
  public void AbsLynxUIScroller::scrollByX:(float delta);
  public void AbsLynxUIScroller::scrollByY:(float delta);
  public void AbsLynxUIScroller::flickX:(float velocity);
  public void AbsLynxUIScroller::flickY:(float velocity);
  public void AbsLynxUIScroller::addScrollerDelegate:(id< LynxUIScrollerDelegate > delegate);
  public void AbsLynxUIScroller::removeScrollerDelegate:(id< LynxUIScrollerDelegate > delegate);
}

public class AlignParam : NSObject {
  public CGFloat AlignParam::leftOffset leftOffset;
  public CGFloat AlignParam::topOffset topOffset;
  public void AlignParam::SetAlignOffsetWithLeft:Top:(CGFloat leftOffset,[Top] CGFloat topOffset);
}

public interface CALayer(LynxHeroTransition) {
  public CATransform3D CALayer(LynxHeroTransition)::flatTransformTo:(CALayer *layer);
}

public class CustomizedMessage : NSObject {
  public NSString* CustomizedMessage::type type;
  public NSString* CustomizedMessage::data data;
  public int CustomizedMessage::mark mark;
}

public class DevToolOverlayDelegate : NSObject {
  public instancetype DevToolOverlayDelegate::sharedInstance();
  public NSArray< NSNumber * > * DevToolOverlayDelegate::getAllVisibleOverlaySign();
  public void DevToolOverlayDelegate::initWithService:(id< OverlayService > service);
}

public class IntersectionObserverEntry : NSObject {
  public CGRect IntersectionObserverEntry::relativeRect relativeRect;
  public CGRect IntersectionObserverEntry::boundingClientRect boundingClientRect;
  public CGRect IntersectionObserverEntry::intersectionRect intersectionRect;
  public float IntersectionObserverEntry::intersectionRatio intersectionRatio;
  public BOOL IntersectionObserverEntry::isIntersecting isIntersecting;
  public double IntersectionObserverEntry::time time;
  public NSString* IntersectionObserverEntry::relativeToId relativeToId;
  public void IntersectionObserverEntry::update();
  public NSDictionary * IntersectionObserverEntry::toDictionary();
}

public class JSModule : NSObject {
  public NSString* JSModule::moduleName moduleName;
  public instancetype JSModule::initWithModuleName:(nonnull NSString *moduleName);
  public void JSModule::fire:withParams:(nonnull NSString *methodName,[withParams] NSArray *args);
  public void JSModule()::setJSProxy:(const std::shared_ptr< lynx::shell::JSProxyDarwin > &proxy);
}

public struct LBSPathConsumer {
  public int LBSPathConsumer::type type;
  public void* LBSPathConsumer::ctx ctx;
  public int LBSPathConsumer::error error;
  public void(* LBSPathConsumer::MoveToPoint) (void *ctx, float x, float y) MoveToPoint;
  public void(* LBSPathConsumer::LineToPoint) (void *ctx, float x, float y) LineToPoint;
  public void(* LBSPathConsumer::CubicToPoint) (void *ctx, float cp1x, float cp1y, float cp2x, float cp2y, float x, float y) CubicToPoint;
  public void(* LBSPathConsumer::QuadToPoint) (void *ctx, float cpx, float cpy, float x, float y) QuadToPoint;
  public void(* LBSPathConsumer::EllipticToPoint) (void *ctx, float cpx, float cpy, float rx, float ry, float angle, bool large, bool sweep, float x, float y) EllipticToPoint;
  public void(* LBSPathConsumer::ClosePath) (void *ctx) ClosePath;
}

public class LineSpacingAdaptation : NSObject, <NSLayoutManagerDelegate> {
  public CGFloat LineSpacingAdaptation::calculatedLineSpacing calculatedLineSpacing;
  public BOOL LineSpacingAdaptation::adjustBaseLineOffsetForVerticalAlignCenter adjustBaseLineOffsetForVerticalAlignCenter;
  public CGFloat LineSpacingAdaptation::baseLineOffsetForVerticalAlignCenter baseLineOffsetForVerticalAlignCenter;
  public CGFloat LineSpacingAdaptation::halfLeading halfLeading;
  public CGFloat LineSpacingAdaptation::lineHeight lineHeight;
  public CGFloat LineSpacingAdaptation::maxLineAscender maxLineAscender;
  public CGFloat LineSpacingAdaptation::maxLineDescender maxLineDescender;
  public BOOL LineSpacingAdaptation::enableLayoutRefactor enableLayoutRefactor;
  public LynxVerticalAlign LineSpacingAdaptation::textSingleLineVerticalAlign textSingleLineVerticalAlign;
  public NSMutableAttributedString* LineSpacingAdaptation::attributedString attributedString;
  public CGFloat LineSpacingAdaptation::baseline baseline;
}

public protocol LUIBodyView-p : <NSObject>, <LUIErrorHandling> {
  public BOOL LUIBodyView-p::enableAsyncDisplay enableAsyncDisplay;
  public NSString* LUIBodyView-p::url url;
  public int32_t LUIBodyView-p::instanceId instanceId;
  public BOOL LUIBodyView-p::isChildLynxPage isChildLynxPage;
  public void LUIBodyView-p::sendGlobalEvent:withParams:(nonnull NSString *name,[withParams] nullable NSArray *params);
  public void LUIBodyView-p::setIntrinsicContentSize:(CGSize size);
  public BOOL LUIBodyView-p::enableTextNonContiguousLayout();
  public void LUIBodyView-p::setAttachLynxPageUICallback:(attachLynxPageUI _Nonnull callback);
}

public protocol LUIConfig-p : <NSObject> {
  public bool LUIConfig-p::defaultOverflowVisible();
  public bool LUIConfig-p::enableTextRefactor();
  public bool LUIConfig-p::enableTextOverflow();
  public bool LUIConfig-p::enableNewClipMode();
  public bool LUIConfig-p::globalImplicit();
  public bool LUIConfig-p::enableEventRefactor();
  public bool LUIConfig-p::enableA11yIDMutationObserver();
  public bool LUIConfig-p::enableEventThrough();
  public bool LUIConfig-p::enableBackgroundShapeLayer();
  public bool LUIConfig-p::enableExposureUIMargin();
  public bool LUIConfig-p::enableTextLanguageAlignment();
  public bool LUIConfig-p::enableXTextLayoutReused();
  public bool LUIConfig-p::enableFiberArch();
  public bool LUIConfig-p::enableNewGesture();
  public bool LUIConfig-p::CSSAlignWithLegacyW3C();
  public NSString * LUIConfig-p::targetSdkVersion();
  public bool LUIConfig-p::imageMonitorEnabled();
  public bool LUIConfig-p::devtoolEnabled();
  public bool LUIConfig-p::fixNewImageDownSampling();
  public CGFloat LUIConfig-p::fluencyPageConfigProbability();
  public bool LUIConfig-p::enableTextLayerRenderer();
  public bool LUIConfig-p::enableTextNonContiguousLayout();
  public bool LUIConfig-p::enableImageDownsampling();
  public bool LUIConfig-p::enableNewImage();
  public bool LUIConfig-p::trailUseNewImage();
  public NSUInteger LUIConfig-p::logBoxImageSizeWarningThreshold();
}

public class LUIConfigAdapter : NSObject, <LUIConfig> {
  public instancetype LUIConfigAdapter::initWithConfig:(lynx::tasm::PageConfig *pageConfig);
}

public protocol LUIErrorHandling-p : <NSObject> {
  public void LUIErrorHandling-p::didReceiveResourceError:(NSError *_Nullable error);
  public void LUIErrorHandling-p::didReceiveResourceError:withSource:type:(LynxError *_Nullable error,[withSource] NSString *_Nullable resourceUrl,[type] NSString *_Nullable type);
  public void LUIErrorHandling-p::reportError:(nonnull NSError *error);
  public void LUIErrorHandling-p::reportLynxError:(LynxError *_Nullable error);
}

public class LynxAccessibilityModule : NSObject, <LynxContextModule> {
  public instancetype LynxAccessibilityModule::initWithLynxContext:(LynxContext *context);
}

public class LynxAliasFontInfo : NSObject {
  public UIFont* LynxAliasFontInfo::font font;
  public NSString* LynxAliasFontInfo::name name;
  public bool LynxAliasFontInfo::isEmpty();
}

public struct LynxAnchorPolicies {
  public BOOL LynxAnchorPolicies::anchorPriorityFromBegin anchorPriorityFromBegin;
  public BOOL LynxAnchorPolicies::deleteRegressPolicyToTop deleteRegressPolicyToTop;
  public BOOL LynxAnchorPolicies::insertAnchorModeInside insertAnchorModeInside;
}

public class LynxAnimationInfo : NSObject, <NSCopying> {
  public CompletionBlock LynxAnimationInfo::completeBlock completeBlock;
  public NSTimeInterval LynxAnimationInfo::duration duration;
  public NSTimeInterval LynxAnimationInfo::delay delay;
  public LynxAnimationProp LynxAnimationInfo::prop prop;
  public CAMediaTimingFunction* LynxAnimationInfo::timingFunction timingFunction;
  public NSString* LynxAnimationInfo::name name;
  public CGFloat LynxAnimationInfo::iterationCount iterationCount;
  public LynxAnimationDirectionType LynxAnimationInfo::direction direction;
  public CAMediaTimingFillMode LynxAnimationInfo::fillMode fillMode;
  public LynxAnimationPlayStateType LynxAnimationInfo::playState playState;
  public int LynxAnimationInfo::orderIndex orderIndex;
  public instancetype LynxAnimationInfo::initWithName:(NSString *name);
  public BOOL LynxAnimationInfo::isEqualToKeyframeInfo:(LynxAnimationInfo *info);
  public BOOL LynxAnimationInfo::isOnlyPlayStateChanged:(LynxAnimationInfo *info);
  public BOOL LynxAnimationInfo::isDirectionReverse:(LynxAnimationInfo *info);
  public BOOL LynxAnimationInfo::isDirectionAlternate:(LynxAnimationInfo *info);
  public BOOL LynxAnimationInfo::isFillModeRemoved:(LynxAnimationInfo *info);
  public LynxAnimationInfo * LynxAnimationInfo::copyAnimationInfo:withProp:(LynxAnimationInfo *info,[withProp] LynxAnimationProp prop);
  public void LynxAnimationInfo::removeDuplicateAnimation:withKey:sameToKey:(NSMutableDictionary< NSNumber *, LynxAnimationInfo * > *animationInfos,[withKey] LynxAnimationProp lhsKey,[sameToKey] LynxAnimationProp rhsKey);
  public void LynxAnimationInfo::makePositionAndSizeTimingInfoConsistent:withPositionKey:withSizeKey:(NSMutableDictionary< NSNumber *, LynxAnimationInfo * > *animationInfos,[withPositionKey] LynxAnimationProp positionKey,[withSizeKey] LynxAnimationProp sizeKey);
}

public class LynxAnimationTransformRotation : NSObject {
  public CGFloat LynxAnimationTransformRotation::rotationX rotationX;
  public CGFloat LynxAnimationTransformRotation::rotationY rotationY;
  public CGFloat LynxAnimationTransformRotation::rotationZ rotationZ;
  public BOOL LynxAnimationTransformRotation::isEqualToTransformRotation:(LynxAnimationTransformRotation *transformRotation);
}

public class LynxBackgroundCapInsets : NSObject {
  public UIEdgeInsets LynxBackgroundCapInsets::capInsets capInsets;
  public LynxUI* LynxBackgroundCapInsets::ui ui;
  public instancetype LynxBackgroundCapInsets::initWithParams:(NSString *capInsetsString);
  public void LynxBackgroundCapInsets::reset();
}

public class LynxBackgroundDrawable : NSObject {
  public LynxBackgroundImageType LynxBackgroundDrawable::type type;
  public LynxBackgroundRepeatType LynxBackgroundDrawable::repeatX repeatX;
  public LynxBackgroundRepeatType LynxBackgroundDrawable::repeatY repeatY;
  public LynxBackgroundClipType LynxBackgroundDrawable::clip clip;
  public LynxBackgroundOriginType LynxBackgroundDrawable::origin origin;
  public LynxBackgroundPosition* LynxBackgroundDrawable::posX posX;
  public LynxBackgroundPosition* LynxBackgroundDrawable::posY posY;
  public LynxBackgroundSize* LynxBackgroundDrawable::sizeX sizeX;
  public LynxBackgroundSize* LynxBackgroundDrawable::sizeY sizeY;
  public CGRect LynxBackgroundDrawable::bounds bounds;
  public LynxBorderRadii LynxBackgroundDrawable::borderRadius borderRadius;
  public CGFloat LynxBackgroundDrawable::getImageWidth();
  public CGFloat LynxBackgroundDrawable::getImageHeight();
  public void LynxBackgroundDrawable::drawInContext:borderRect:paddingRect:contentRect:(CGContextRef _Nonnull ctx,[borderRect] CGRect borderRect,[paddingRect] CGRect paddingRect,[contentRect] CGRect contentRect);
  public void LynxBackgroundDrawable::drawTextBackgroundInContext:contentRect:(CGContextRef _Nonnull ctx,[contentRect] CGRect contentRect);
  public CGSize LynxBackgroundDrawable::computeBackgroundSizeWithImageSize:andPaintBoxSize:(const CGSize *_Nonnull imageSize,[andPaintBoxSize] const CGSize *_Nonnull paintBoxSize);
  public void LynxBackgroundDrawable::computeBackgroundPosition:offsetY:paintBox:size:(CGFloat *_Nonnull offsetX,[offsetY] CGFloat *_Nonnull offsetY,[paintBox] const CGRect paintBox,[size] const CGSize size);
}

public class LynxBackgroundGradientDrawable : LynxBackgroundDrawable {
  public LynxGradient* LynxBackgroundGradientDrawable::gradient gradient;
  public CAReplicatorLayer* LynxBackgroundGradientDrawable::horizontalRepeatLayer horizontalRepeatLayer;
  public CAReplicatorLayer* LynxBackgroundGradientDrawable::verticalRepeatLayer verticalRepeatLayer;
  public CAGradientLayer* LynxBackgroundGradientDrawable::gradientLayer gradientLayer;
  public void LynxBackgroundGradientDrawable::prepareGradientWithBorderBox:andPaintBox:andClipRect:(CGRect borderBox,[andPaintBox] CGRect paintBox,[andClipRect] CGRect clipRect);
  public void LynxBackgroundGradientDrawable::onPrepareGradientWithSize:(CGSize gradientSize);
}

public class LynxBackgroundImageDrawable : LynxBackgroundDrawable {
  public NSURL* LynxBackgroundImageDrawable::url url;
  public UIImage* LynxBackgroundImageDrawable::image image;
  public instancetype _Nullable LynxBackgroundImageDrawable::initWithString:(NSString *_Nullable string);
  public instancetype _Nullable LynxBackgroundImageDrawable::initWithURL:(NSURL *_Nullable url);
}

public class LynxBackgroundImageLayerInfo : NSObject {
  public LynxBackgroundDrawable* LynxBackgroundImageLayerInfo::item item;
  public CGRect LynxBackgroundImageLayerInfo::paintingRect paintingRect;
  public CGRect LynxBackgroundImageLayerInfo::clipRect clipRect;
  public CGRect LynxBackgroundImageLayerInfo::contentRect contentRect;
  public CGRect LynxBackgroundImageLayerInfo::borderRect borderRect;
  public CGRect LynxBackgroundImageLayerInfo::paddingRect paddingRect;
  public LynxBackgroundOriginType LynxBackgroundImageLayerInfo::backgroundOrigin backgroundOrigin;
  public LynxBackgroundRepeatType LynxBackgroundImageLayerInfo::repeatXType repeatXType;
  public LynxBackgroundRepeatType LynxBackgroundImageLayerInfo::repeatYType repeatYType;
  public LynxBackgroundSize* LynxBackgroundImageLayerInfo::backgroundSizeX backgroundSizeX;
  public LynxBackgroundSize* LynxBackgroundImageLayerInfo::backgroundSizeY backgroundSizeY;
  public LynxBackgroundPosition* LynxBackgroundImageLayerInfo::backgroundPosX backgroundPosX;
  public LynxBackgroundPosition* LynxBackgroundImageLayerInfo::backgroundPosY backgroundPosY;
  public LynxBackgroundClipType LynxBackgroundImageLayerInfo::backgroundClip backgroundClip;
  public LynxCornerInsets LynxBackgroundImageLayerInfo::cornerInsets cornerInsets;
  public void LynxBackgroundImageLayerInfo::drawInContext:(CGContextRef _Nullable ctx);
  public BOOL LynxBackgroundImageLayerInfo::prepareGradientLayers();
  public CGPathRef LynxBackgroundImageLayerInfo::createClipPath();
}

public class LynxBackgroundInfo : NSObject {
  public LynxPlatformLength* LynxBackgroundInfo::borderRadiusCalc[8] borderRadiusCalc;
  public CGFloat LynxBackgroundInfo::outlineWidth outlineWidth;
  public LynxBorderStyle LynxBackgroundInfo::outlineStyle outlineStyle;
  public UIColor* LynxBackgroundInfo::outlineColor outlineColor;
  public UIColor* LynxBackgroundInfo::borderTopColor borderTopColor;
  public UIColor* LynxBackgroundInfo::borderBottomColor borderBottomColor;
  public UIColor* LynxBackgroundInfo::borderLeftColor borderLeftColor;
  public UIColor* LynxBackgroundInfo::borderRightColor borderRightColor;
  public UIColor* LynxBackgroundInfo::backgroundColor backgroundColor;
  public LynxBorderStyle LynxBackgroundInfo::borderTopStyle borderTopStyle;
  public LynxBorderStyle LynxBackgroundInfo::borderBottomStyle borderBottomStyle;
  public LynxBorderStyle LynxBackgroundInfo::borderLeftStyle borderLeftStyle;
  public LynxBorderStyle LynxBackgroundInfo::borderRightStyle borderRightStyle;
  public LynxBorderRadii LynxBackgroundInfo::borderRadius borderRadius;
  public UIEdgeInsets LynxBackgroundInfo::borderWidth borderWidth;
  public UIEdgeInsets LynxBackgroundInfo::paddingWidth paddingWidth;
  public BOOL LynxBackgroundInfo::BGChangedNoneImage BGChangedNoneImage;
  public BOOL LynxBackgroundInfo::BGChangedImage BGChangedImage;
  public BOOL LynxBackgroundInfo::borderChanged borderChanged;
  public CGRect LynxBackgroundInfo::getPaddingRect:(CGSize size);
  public CGRect LynxBackgroundInfo::getContentRect:(CGRect paddingRect);
  public UIEdgeInsets LynxBackgroundInfo::getPaddingInsets();
  public UIImage * LynxBackgroundInfo::getBorderLayerImageWithSize:(CGSize viewSize);
  public CGPathRef LynxBackgroundInfo::getBorderLayerPathWithSize:(CGSize viewSize);
  public BOOL LynxBackgroundInfo::updateOutlineWidth:(CGFloat outlineWidth);
  public BOOL LynxBackgroundInfo::updateOutlineColor:(UIColor *outlineColor);
  public BOOL LynxBackgroundInfo::updateOutlineStyle:(LynxBorderStyle outlineStyle);
  public void LynxBackgroundInfo::updateBorderColor:value:(LynxBorderPosition position,[value] UIColor *color);
  public BOOL LynxBackgroundInfo::updateBorderStyle:value:(LynxBorderPosition position,[value] LynxBorderStyle style);
  public BOOL LynxBackgroundInfo::hasBorder();
  public BOOL LynxBackgroundInfo::isSimpleBorder();
  public BOOL LynxBackgroundInfo::canUseBorderShapeLayer();
  public BOOL LynxBackgroundInfo::hasDifferentBorderRadius();
  public void LynxBackgroundInfo::makeCssDefaultValueToFitW3c();
}

public class LynxBackgroundLinearGradientDrawable : LynxBackgroundGradientDrawable {
  public instancetype _Nullable LynxBackgroundLinearGradientDrawable::initWithArray:(NSArray *_Nonnull array);
}

public class LynxBackgroundManager : NSObject, <CALayerDelegate> {
  public LynxUI* LynxBackgroundManager::ui ui;
  public LynxBackgroundInfo* LynxBackgroundManager::backgroundInfo backgroundInfo;
  public CGFloat LynxBackgroundManager::opacity opacity;
  public BOOL LynxBackgroundManager::hidden hidden;
  public UIColor* LynxBackgroundManager::backgroundColor backgroundColor;
  public LynxBorderRadii LynxBackgroundManager::borderRadius borderRadius;
  public LynxBorderRadii LynxBackgroundManager::borderRadiusRaw borderRadiusRaw;
  public UIEdgeInsets LynxBackgroundManager::borderWidth borderWidth;
  public UIColor* LynxBackgroundManager::borderTopColor borderTopColor;
  public UIColor* LynxBackgroundManager::borderBottomColor borderBottomColor;
  public UIColor* LynxBackgroundManager::borderLeftColor borderLeftColor;
  public UIColor* LynxBackgroundManager::borderRightColor borderRightColor;
  public NSMutableArray* LynxBackgroundManager::backgroundDrawable backgroundDrawable;
  public NSMutableArray* LynxBackgroundManager::backgroundOrigin backgroundOrigin;
  public NSMutableArray* LynxBackgroundManager::backgroundPosition backgroundPosition;
  public NSMutableArray* LynxBackgroundManager::backgroundRepeat backgroundRepeat;
  public NSMutableArray* LynxBackgroundManager::backgroundClip backgroundClip;
  public NSMutableArray* LynxBackgroundManager::backgroundImageSize backgroundImageSize;
  public LynxBackgroundCapInsets* LynxBackgroundManager::backgroundCapInsets backgroundCapInsets;
  public NSMutableArray* LynxBackgroundManager::maskDrawable maskDrawable;
  public NSMutableArray* LynxBackgroundManager::maskOrigin maskOrigin;
  public NSMutableArray* LynxBackgroundManager::maskPosition maskPosition;
  public NSMutableArray* LynxBackgroundManager::maskRepeat maskRepeat;
  public NSMutableArray* LynxBackgroundManager::maskClip maskClip;
  public NSMutableArray* LynxBackgroundManager::maskSize maskSize;
  public LynxLinearGradient* LynxBackgroundManager::linearGradient linearGradient;
  public BOOL LynxBackgroundManager::implicitAnimation implicitAnimation;
  public CATransform3D LynxBackgroundManager::transform transform;
  public CGPoint LynxBackgroundManager::transformOrigin transformOrigin;
  public LynxBackgroundSubBackgroundLayer* LynxBackgroundManager::backgroundLayer backgroundLayer;
  public LynxBorderLayer* LynxBackgroundManager::borderLayer borderLayer;
  public CALayer* LynxBackgroundManager::outlineLayer outlineLayer;
  public CGPoint LynxBackgroundManager::postTranslate postTranslate;
  public LynxBackgroundSubBackgroundLayer* LynxBackgroundManager::maskLayer maskLayer;
  public UIView* LynxBackgroundManager::opacityView opacityView;
  public NSArray<LynxBoxShadow*>* LynxBackgroundManager::shadowArray shadowArray;
  public int LynxBackgroundManager::animationOptions animationOptions;
  public int LynxBackgroundManager::animationLayerCount animationLayerCount;
  public BOOL LynxBackgroundManager::allowsEdgeAntialiasing allowsEdgeAntialiasing;
  public BOOL LynxBackgroundManager::overlapRendering overlapRendering;
  public LynxBgShapeLayerProp LynxBackgroundManager::uiBackgroundShapeLayerEnabled uiBackgroundShapeLayerEnabled;
  public BOOL LynxBackgroundManager::shouldRasterizeShadow shouldRasterizeShadow;
  public BOOL LynxBackgroundManager::isPixelated isPixelated;
  public instancetype LynxBackgroundManager::initWithUI:(LynxUI *ui);
  public void LynxBackgroundManager::markBackgroundDirty();
  public void LynxBackgroundManager::markMaskDirty();
  public void LynxBackgroundManager::applyEffect();
  public void LynxBackgroundManager::updateShadow();
  public void LynxBackgroundManager::removeAllAnimations();
  public void LynxBackgroundManager::addAnimationToViewAndLayers:forKey:(CAAnimation *anim,[forKey] nullable NSString *key);
  public void LynxBackgroundManager::addAnimation:forKey:(CAAnimation *anim,[forKey] nullable NSString *key);
  public void LynxBackgroundManager::removeAnimationForKey:(NSString *key);
  public void LynxBackgroundManager::setPostTranslate:(CGPoint postTranslate);
  public CATransform3D LynxBackgroundManager::getTransformWithPostTranslate();
  public UIImage * LynxBackgroundManager::getBackgroundImageForContentsAnimation();
  public UIImage * LynxBackgroundManager::getBackgroundImageForContentsAnimationWithSize:(CGSize size);
  public UIImage * LynxBackgroundManager::getBorderImageForContentsAnimationWithSize:(CGSize size);
  public CGPathRef LynxBackgroundManager::getBorderPathForAnimationWithSize:(CGSize size);
  public void LynxBackgroundManager::getBackgroundImageForContentsAnimationAsync:withSize:(void(^ completionBlock)(UIImage *),[withSize] CGSize size);
  public void LynxBackgroundManager::removeAssociateLayers();
  public void LynxBackgroundManager::setFilters:(nullable NSArray *array);
  public BOOL LynxBackgroundManager::hasDifferentBorderRadius();
  public void LynxBackgroundManager::makeCssDefaultValueToFitW3c();
  public BOOL LynxBackgroundManager::hasDifferentBackgroundColor:(UIColor *color);
  public BOOL LynxBackgroundManager::updateOutlineWidth:(CGFloat outlineWidth);
  public BOOL LynxBackgroundManager::updateOutlineColor:(UIColor *outlineColor);
  public BOOL LynxBackgroundManager::updateOutlineStyle:(LynxBorderStyle outlineStyle);
  public void LynxBackgroundManager::updateBorderColor:value:(LynxBorderPosition position,[value] UIColor *color);
  public BOOL LynxBackgroundManager::updateBorderStyle:value:(LynxBorderPosition position,[value] LynxBorderStyle style);
  public CGPathRef LynxBackgroundManager::createBezierPathWithRoundedRect:borderRadii:edgeInsets:(CGRect bounds,[borderRadii] LynxBorderRadii borderRadii,[edgeInsets] UIEdgeInsets edgeInsets);
  public CGPathRef LynxBackgroundManager::createBezierPathWithRoundedRect:borderRadii:(CGRect bounds,[borderRadii] LynxBorderRadii borderRadii);
}

public class LynxBackgroundPosition : NSObject {
  public LynxPlatformLength* _Nullable LynxBackgroundPosition::value value;
  public instancetype _Nullable LynxBackgroundPosition::initWithValue:(LynxPlatformLength *_Nullable value);
  public CGFloat LynxBackgroundPosition::apply:(CGFloat avaiableValue);
}

public class LynxBackgroundRadialGradientDrawable : LynxBackgroundGradientDrawable {
  public instancetype _Nullable LynxBackgroundRadialGradientDrawable::initWithArray:(NSArray *_Nonnull array);
}

public class LynxBackgroundRuntime : NSObject {
  public NSString* LynxBackgroundRuntime::lastScriptUrl lastScriptUrl;
  public instancetype LynxBackgroundRuntime::initWithOptions:(LynxBackgroundRuntimeOptions *options);
  public void LynxBackgroundRuntime::addLifecycleClient:(nonnull id< LynxBackgroundRuntimeLifecycle > lifecycleClient);
  public void LynxBackgroundRuntime::removeLifecycleClient:(nonnull id< LynxBackgroundRuntimeLifecycle > lifecycleClient);
  public void LynxBackgroundRuntime::evaluateJavaScript:withSources:(NSString *url,[withSources] NSString *sources);
  public void LynxBackgroundRuntime::sendGlobalEvent:withParams:(nonnull NSString *name,[withParams] nullable NSArray *params);
  public void LynxBackgroundRuntime::addRuntimeLifecycleListener:(nonnull id< LynxRuntimeLifecycleListener > listener);
  public void LynxBackgroundRuntime::setSessionStorageItem:withTemplateData:(nonnull NSString *key,[withTemplateData] nullable LynxTemplateData *data);
  public void LynxBackgroundRuntime::getSessionStorageItem:withCallback:(nonnull NSString *key,[withCallback] void(^_Nullable callback)(id< NSObject > _Nullable));
  public double LynxBackgroundRuntime::subscribeSessionStorage:withCallback:(nonnull NSString *key,[withCallback] void(^_Nullable callback)(id< NSObject > _Nullable));
  public void LynxBackgroundRuntime::unSubscribeSessionStorage:withId:(nonnull NSString *key,[withId] double callbackId);
  public void LynxBackgroundRuntime::transitionToFullRuntime();
  public std::weak_ptr< lynx::piper::LynxModuleManager > LynxBackgroundRuntime()::moduleManagerPtr();
  public LynxBackgroundRuntimeOptions * LynxBackgroundRuntime()::options();
  public std::shared_ptr< lynx::shell::LynxActor< lynx::runtime::LynxRuntime > > LynxBackgroundRuntime()::runtimeActor();
  public std::shared_ptr< lynx::shell::LynxActor< lynx::tasm::timing::TimingHandler > > LynxBackgroundRuntime()::timingActor();
  public LynxDevtool * LynxBackgroundRuntime()::devtool();
  public BOOL LynxBackgroundRuntime()::attachToLynxView();
  public void LynxBackgroundRuntime()::setRuntimeObserver:(const std::shared_ptr< lynx::piper::InspectorRuntimeObserverNG > &observer);
}

public protocol LynxBackgroundRuntimeLifecycle-p : <NSObject> {
  public void LynxBackgroundRuntimeLifecycle-p::runtime:didRecieveError:(LynxBackgroundRuntime *runtime,[didRecieveError] NSError *error);
}

public class LynxBackgroundRuntimeOptions : NSObject {
  public LynxGroup* LynxBackgroundRuntimeOptions::group group;
  public LynxBackgroundJsRuntimeType LynxBackgroundRuntimeOptions::backgroundJsRuntimeType backgroundJsRuntimeType;
  public LynxTemplateData* LynxBackgroundRuntimeOptions::presetData presetData;
  public BOOL LynxBackgroundRuntimeOptions::enableBytecode enableBytecode;
  public NSString* LynxBackgroundRuntimeOptions::bytecodeUrl bytecodeUrl;
  public id<LynxGenericResourceFetcher> LynxBackgroundRuntimeOptions::genericResourceFetcher genericResourceFetcher;
  public id<LynxMediaResourceFetcher> LynxBackgroundRuntimeOptions::mediaResourceFetcher mediaResourceFetcher;
  public id<LynxTemplateResourceFetcher> LynxBackgroundRuntimeOptions::templateResourceFetcher templateResourceFetcher;
  public BOOL LynxBackgroundRuntimeOptions::pendingCoreJsLoad pendingCoreJsLoad;
  public instancetype LynxBackgroundRuntimeOptions::init();
  public void LynxBackgroundRuntimeOptions::addLynxResourceProvider:provider:(NSString *resType,[provider] id< LynxResourceProvider > provider);
  public void LynxBackgroundRuntimeOptions::registerModule:(Class< LynxModule > module);
  public void LynxBackgroundRuntimeOptions::registerModule:param:(Class< LynxModule > module,[param] nullable id param);
  public NSMutableDictionary< NSString *, id > * LynxBackgroundRuntimeOptions()::moduleWrappers();
  public NSMutableDictionary< NSString *, id< LynxResourceProvider > > * LynxBackgroundRuntimeOptions()::providers();
  public std::string LynxBackgroundRuntimeOptions()::groupThreadName();
  public std::string LynxBackgroundRuntimeOptions()::groupID();
  public BOOL LynxBackgroundRuntimeOptions()::enableJSGroupThread();
  public std::vector< std::string > LynxBackgroundRuntimeOptions()::preloadJSPath();
  public std::string LynxBackgroundRuntimeOptions()::bytecodeUrlString();
  public instancetype LynxBackgroundRuntimeOptions()::initWithOptions:(LynxBackgroundRuntimeOptions *other);
  public void LynxBackgroundRuntimeOptions()::merge:(LynxBackgroundRuntimeOptions *other);
}

public class LynxBackgroundSize : NSObject {
  public LynxPlatformLength* _Nullable LynxBackgroundSize::value value;
  public instancetype _Nullable LynxBackgroundSize::initWithLength:(LynxPlatformLength *_Nullable value);
  public BOOL LynxBackgroundSize::isCover();
  public BOOL LynxBackgroundSize::isContain();
  public BOOL LynxBackgroundSize::isAuto();
  public CGFloat LynxBackgroundSize::apply:currentValue:(CGFloat parentValue,[currentValue] CGFloat currentValue);
}

public class LynxBackgroundSubBackgroundLayer : LynxBackgroundSubLayer {
  public BOOL LynxBackgroundSubBackgroundLayer::isAnimated isAnimated;
  public NSUInteger LynxBackgroundSubBackgroundLayer::frameCount frameCount;
  public NSTimeInterval LynxBackgroundSubBackgroundLayer::animatedImageDuration animatedImageDuration;
  public BOOL LynxBackgroundSubBackgroundLayer::enableAsyncDisplay enableAsyncDisplay;
  public LynxBackgroundClipType LynxBackgroundSubBackgroundLayer::backgroundColorClip backgroundColorClip;
  public UIEdgeInsets LynxBackgroundSubBackgroundLayer::paddingWidth paddingWidth;
  public CGRect LynxBackgroundSubBackgroundLayer::shadowsBounds shadowsBounds;
  public void LynxBackgroundSubBackgroundLayer::markDirtyWithSize:radii:borderInsets:backgroundColor:drawToEdge:capInsets:isGradientOnly:isPixelated:(CGSize viewSize,[radii] LynxBorderRadii cornerRadii,[borderInsets] UIEdgeInsets borderInsets,[backgroundColor] UIColor *backgroundColor,[drawToEdge] BOOL drawToEdge,[capInsets] UIEdgeInsets insets,[isGradientOnly] BOOL isGradientOnly,[isPixelated] BOOL isPixelated);
  public void LynxBackgroundSubBackgroundLayer::setAnimatedPropsWithImage:(UIImage *image);
  public void LynxBackgroundSubBackgroundLayer::detachAllGradientLayers();
}

public class LynxBackgroundSubLayer : CALayer {
  public LynxBgTypes LynxBackgroundSubLayer::type type;
  public NSMutableArray<LynxBackgroundImageLayerInfo*>* LynxBackgroundSubLayer::imageArray imageArray;
}

public class LynxBackgroundUtils : NSObject {
  public CGPathRef LynxBackgroundUtils::createBezierPathWithRoundedRect:borderRadii:(CGRect bounds,[borderRadii] LynxBorderRadii borderRadii);
  public CGPathRef LynxBackgroundUtils::createBezierPathWithRoundedRect:borderRadii:edgeInsets:(CGRect bounds,[borderRadii] LynxBorderRadii borderRadii,[edgeInsets] UIEdgeInsets edgeInsets);
  public void LynxBackgroundUtils::LynxPathAddEllipticArc(CGMutablePathRef path, CGPoint origin, CGFloat width, CGFloat height, CGFloat startAngle, CGFloat endAngle, BOOL clockwise);
  public void LynxBackgroundUtils::LynxPathAddRect(CGMutablePathRef path, CGRect bounds, bool reverse);
  public void LynxBackgroundUtils::LynxPathAddRoundedRect(CGMutablePathRef path, CGRect bounds, LynxCornerInsets ci);
  public CGPathRef LynxBackgroundUtils::LynxPathCreateWithRoundedRect(CGRect bounds, LynxCornerInsets ci);
  public BOOL LynxBackgroundUtils::LynxCornerInsetsAreAboveThreshold(const LynxCornerInsets cornerInsets);
  public UIEdgeInsets LynxBackgroundUtils::LynxGetEdgeInsets(CGRect bounds, UIEdgeInsets edgeInsets, CGFloat mul);
  public LynxCornerInsets LynxBackgroundUtils::LynxGetCornerInsets(CGRect bounds, LynxBorderRadii cornerRadii, UIEdgeInsets edgeInsets);
  public LynxCornerInsets LynxBackgroundUtils::LynxGetCornerInsetsA(CGRect bounds, LynxBorderRadii cornerRadii, UIEdgeInsets edgeInsets, LynxPlatformLength *__strong cornerRadiiCalc[8]);
  public void LynxBackgroundUtils::adjustInsets(UIImage *image, CALayer *layer, UIEdgeInsets insets);
  public CGRect LynxBackgroundUtils::LynxGetRectWithEdgeInsets(CGRect bounds, UIEdgeInsets border);
  public BOOL LynxBackgroundUtils::LynxBorderInsetsNotLargeThan(UIEdgeInsets borderInsets, float val);
  public bool LynxBackgroundUtils::LynxUpdateOutlineLayer(CALayer *layer, CGSize viewSize, LynxBorderStyle style, UIColor *color, float width);
  public void LynxBackgroundUtils::LynxUpdateLayerWithImage(CALayer *layer, UIImage *image);
  public UIImage * LynxBackgroundUtils::LynxGetBorderLayerImage(LynxBorderStyles borderStyles, CGSize viewSize, LynxBorderRadii cornerRadii, UIEdgeInsets borderInsets, LynxBorderColors borderColors, BOOL drawToEdge);
  public UIImage * LynxBackgroundUtils::LynxGetBackgroundImage(CGSize viewSize, LynxBorderRadii cornerRadii, UIEdgeInsets borderInsets, CGColorRef backgroundColor, BOOL drawToEdge, NSArray *bgImageInfoArr, BOOL pixelated);
  public UIImage * LynxBackgroundUtils::LynxGetBackgroundImageWithClip(CGSize viewSize, LynxBorderRadii cornerRadii, UIEdgeInsets borderInsets, CGColorRef backgroundColor, BOOL drawToEdge, NSArray *bgImageInfoArr, LynxBackgroundClipType clip, UIEdgeInsets paddingInsets, BOOL pixelated);
  public CGPathRef LynxBackgroundUtils::LynxCreateBorderCenterPath(CGSize viewSize, LynxBorderRadii cornerRadius, UIEdgeInsets borderWidth);
  public void LynxBackgroundUtils::LynxUpdateBorderLayerWithPath(CAShapeLayer *borderLayer, CGPathRef path, LynxBackgroundInfo *info);
  public BOOL LynxBackgroundUtils::internalHasSameBorderRadius(LynxBorderRadii radii);
  public void LynxBackgroundUtils::LBRGetBorderValueOrLength(NSArray *valArray, int index, LynxBorderUnitValue *value, LynxPlatformLength **length);
}

public class LynxBaseCardManager : NSObject {
  public instancetype LynxBaseCardManager::init:WithCapacity:(id< LynxHolder > lynxHolder,[WithCapacity] NSInteger capacity);
  public void LynxBaseCardManager::registerInitLynxView:(LynxView *LynxView);
  public void LynxBaseCardManager::push:param:(NSString *templateUrl,[param] NSDictionary *param);
  public void LynxBaseCardManager::replace:param:(NSString *templateUrl,[param] NSDictionary *param);
  public void LynxBaseCardManager::pop();
  public BOOL LynxBaseCardManager::onNavigateBack();
  public void LynxBaseCardManager::didEnterForeground();
  public void LynxBaseCardManager::didEnterBackground();
  public void LynxBaseCardManager::clearCaches();
  public LynxView* LynxBaseCardManager()::lynxView lynxView;
  public NSMutableArray* LynxBaseCardManager()::routeStack routeStack;
  public LynxLruCache* LynxBaseCardManager()::lruCache lruCache;
  public id<LynxHolder> LynxBaseCardManager()::holder holder;
}

public class LynxBaseGestureHandler : NSObject {
  public LynxGestureDetectorDarwin* LynxBaseGestureHandler::gestureDetector gestureDetector;
  public id<LynxGestureArenaMember> LynxBaseGestureHandler::gestureMember gestureMember;
  public NSInteger LynxBaseGestureHandler::sign sign;
  public LynxUIContext* LynxBaseGestureHandler::context context;
  public NSDictionary< NSNumber *, LynxBaseGestureHandler * > * LynxBaseGestureHandler::convertToGestureHandler:context:member:gestureDetectors:(NSInteger sign,[context] LynxUIContext *lynxContext,[member] id< LynxGestureArenaMember > member,[gestureDetectors] NSDictionary< NSNumber *, LynxGestureDetectorDarwin * > *gestureDetectors);
  public instancetype LynxBaseGestureHandler::initWithSign:context:member:detector:(NSInteger sign,[context] LynxUIContext *lynxContext,[member] id< LynxGestureArenaMember > member,[detector] LynxGestureDetectorDarwin *detector);
  public BOOL LynxBaseGestureHandler::isGestureTypeMatched:(NSUInteger typeMask);
  public BOOL LynxBaseGestureHandler::canActiveWithCurrentGesture:(CGPoint deltaPoint);
  public BOOL LynxBaseGestureHandler::isCurrentGestureEnd();
  public void LynxBaseGestureHandler::activate();
  public void LynxBaseGestureHandler::reset();
  public void LynxBaseGestureHandler::fail();
  public void LynxBaseGestureHandler::end();
  public void LynxBaseGestureHandler::ignore();
  public void LynxBaseGestureHandler::begin();
  public void LynxBaseGestureHandler::begin:point:touches:event:touchEvent:(NSUInteger typeMask,[point] CGPoint point,[touches] NSSet< UITouch * > *_Nullable touches,[event] UIEvent *_Nullable event,[touchEvent] LynxTouchEvent *_Nullable touchEvent);
  public void LynxBaseGestureHandler::update:point:touches:event:touchEvent:(NSUInteger typeMask,[point] CGPoint point,[touches] NSSet< UITouch * > *_Nullable touches,[event] UIEvent *_Nullable event,[touchEvent] LynxTouchEvent *_Nullable touchEvent);
  public void LynxBaseGestureHandler::end:point:touches:event:touchEvent:(NSUInteger typeMask,[point] CGPoint point,[touches] NSSet< UITouch * > *_Nullable touches,[event] UIEvent *_Nullable event,[touchEvent] LynxTouchEvent *_Nullable touchEvent);
  public void LynxBaseGestureHandler::onBegin:touchEvent:(CGPoint point,[touchEvent] LynxTouchEvent *_Nullable touchEvent);
  public void LynxBaseGestureHandler::onUpdate:touchEvent:(CGPoint point,[touchEvent] LynxTouchEvent *_Nullable touchEvent);
  public void LynxBaseGestureHandler::onStart:touchEvent:(CGPoint point,[touchEvent] LynxTouchEvent *_Nullable touchEvent);
  public void LynxBaseGestureHandler::onEnd:touchEvent:(CGPoint point,[touchEvent] LynxTouchEvent *_Nullable touchEvent);
  public BOOL LynxBaseGestureHandler::onBeginEnabled();
  public BOOL LynxBaseGestureHandler::onUpdateEnabled();
  public BOOL LynxBaseGestureHandler::onStartEnabled();
  public BOOL LynxBaseGestureHandler::onEndEnabled();
  public void LynxBaseGestureHandler::handleUIEvent:touches:event:touchEvent:flingPoint:(NSString *const touchType,[touches] NSSet< UITouch * > *touches,[event] UIEvent *_Nullable event,[touchEvent] LynxTouchEvent *_Nullable touchEvent,[flingPoint] CGPoint flingPoint);
  public void LynxBaseGestureHandler::onHandle:touches:event:touchEvent:flingPoint:(NSString *const touchType,[touches] NSSet< UITouch * > *touches,[event] UIEvent *_Nullable event,[touchEvent] LynxTouchEvent *_Nullable touchEvent,[flingPoint] CGPoint flingPoint);
  public bool LynxBaseGestureHandler::isEnd();
  public bool LynxBaseGestureHandler::isActive();
  public int LynxBaseGestureHandler::status();
  public void LynxBaseGestureHandler::onTouchesDown:(LynxTouchEvent *touchEvent);
  public void LynxBaseGestureHandler::onTouchesMove:(LynxTouchEvent *touchEvent);
  public void LynxBaseGestureHandler::onTouchesUp:(LynxTouchEvent *touchEvent);
  public void LynxBaseGestureHandler::onTouchesCancel:(LynxTouchEvent *touchEvent);
  public void LynxBaseGestureHandler::sendGestureEvent:params:(NSString *eventName,[params] NSDictionary *eventParams);
  public void LynxBaseGestureHandler::handleConfigMap:(nullable NSMutableDictionary *config);
  public NSDictionary * LynxBaseGestureHandler::eventParamsFromTouchEvent:(LynxTouchEvent *touchEvent);
}

public protocol LynxBaseInspectorOwner-p : <NSObject> {
  public nonnull instancetype LynxBaseInspectorOwner-p::initWithLynxView:(nullable LynxView *view);
  public void LynxBaseInspectorOwner-p::setReloadHelper:(nullable LynxPageReloadHelper *reloadHelper);
  public void LynxBaseInspectorOwner-p::onTemplateAssemblerCreated:(intptr_t ptr);
  public void LynxBaseInspectorOwner-p::handleLongPress();
  public void LynxBaseInspectorOwner-p::stopCasting();
  public void LynxBaseInspectorOwner-p::continueCasting();
  public void LynxBaseInspectorOwner-p::pauseCasting();
  public void LynxBaseInspectorOwner-p::setPostUrl:(nullable NSString *postUrl);
  public void LynxBaseInspectorOwner-p::onLoadFinished();
  public void LynxBaseInspectorOwner-p::reloadLynxView:(BOOL ignoreCache);
  public void LynxBaseInspectorOwner-p::navigateLynxView:(nonnull NSString *url);
  public void LynxBaseInspectorOwner-p::emulateTouch:coordinateX:coordinateY:button:deltaX:deltaY:modifiers:clickCount:(nonnull NSString *type,[coordinateX] int x,[coordinateY] int y,[button] nonnull NSString *button,[deltaX] CGFloat dx,[deltaY] CGFloat dy,[modifiers] int modifiers,[clickCount] int clickCount);
  public void LynxBaseInspectorOwner-p::call:withParam:(NSString *_Nonnull function,[withParam] NSString *_Nullable params);
  public void LynxBaseInspectorOwner-p::attach:(nonnull LynxView *lynxView);
  public (deprecated("Deprecated after Lynx2.18")) LynxBaseInspectorOwner-p::__attribute__();
  public void LynxBaseInspectorOwner-p::reloadLynxView:withTemplate:fromFragments:withSize:(BOOL ignoreCache,[withTemplate] nullable NSString *templateBin,[fromFragments] BOOL fromFragments,[withSize] int32_t size);
  public void LynxBaseInspectorOwner-p::invokeCDPFromSDK:withCallback:(NSString *msg,[withCallback] CDPResultCallback callback);
  public void LynxBaseInspectorOwner-p::onReceiveTemplateFragment:withEof:(nullable NSString *data,[withEof] BOOL eof);
  public void LynxBaseInspectorOwner-p::attachDebugBridge:(NSString *url);
  public void LynxBaseInspectorOwner-p::endTestbench:(NSString *_Nonnull filePath);
  public void LynxBaseInspectorOwner-p::onPageUpdate();
  public void LynxBaseInspectorOwner-p::downloadResource:callback:(NSString *_Nonnull url,[callback] LynxResourceLoadBlock _Nonnull callback);
  public void LynxBaseInspectorOwner-p::setLynxInspectorConsoleDelegate:(id _Nonnull delegate);
  public void LynxBaseInspectorOwner-p::getConsoleObject:needStringify:resultHandler:(NSString *_Nonnull objectId,[needStringify] BOOL stringify,[resultHandler] void(^_Nonnull handler)(NSString *_Nonnull detail));
  public void LynxBaseInspectorOwner-p::onPerfMetricsEvent:withData:(NSString *_Nonnull eventName,[withData] NSDictionary< NSString *, NSObject * > *_Nonnull data);
  public void LynxBaseInspectorOwner-p::onReceiveMessageEvent:(NSDictionary *event);
  public void LynxBaseInspectorOwner-p::setDispatchMessageEventBlock:(void(^ block)(NSDictionary *));
  public NSString * LynxBaseInspectorOwner-p::debugInfoUrl();
  public void LynxBaseInspectorOwner-p::onGlobalPropsUpdated:(LynxTemplateData *props);
  public void LynxBaseInspectorOwner-p::showErrorMessageOnConsole:(LynxError *error);
  public void LynxBaseInspectorOwner-p::showMessageOnConsole:withLevel:(NSString *message,[withLevel] int32_t level);
}

public protocol LynxBaseInspectorOwnerNG-p : <LynxBaseInspectorOwner> {
  public void LynxBaseInspectorOwnerNG-p::sendMessage:(nonnull CustomizedMessage *message);
  public void LynxBaseInspectorOwnerNG-p::subscribeMessage:withHandler:(nonnull NSString *type,[withHandler] nonnull id< MessageHandler > handler);
  public void LynxBaseInspectorOwnerNG-p::unsubscribeMessage:(nonnull NSString *type);
  public void LynxBaseInspectorOwnerNG-p::reloadLynxView:withTemplate:fromFragments:withSize:(BOOL ignoreCache,[withTemplate] nullable NSString *templateBin,[fromFragments] BOOL fromFragments,[withSize] int32_t size);
}

public class LynxBaselineShiftLayoutManager : NSLayoutManager {
  public LynxVerticalAlign LynxBaselineShiftLayoutManager::verticalAlign verticalAlign;
  public instancetype LynxBaselineShiftLayoutManager::initWithVerticalAlign:(LynxVerticalAlign verticalAlign);
}

public protocol LynxBaseLogBoxProxy-p : <NSObject> {
  public nonnull instancetype LynxBaseLogBoxProxy-p::initWithLynxView:(nullable LynxView *view);
  public void LynxBaseLogBoxProxy-p::onMovedToWindow();
  public void LynxBaseLogBoxProxy-p::setReloadHelper:(nullable LynxPageReloadHelper *reload_helper);
  public void LynxBaseLogBoxProxy-p::showLogMessage:(LynxError *error);
  public void LynxBaseLogBoxProxy-p::attachLynxView:(nonnull LynxView *lynxView);
  public void LynxBaseLogBoxProxy-p::onLynxViewReload();
  public void LynxBaseLogBoxProxy-p::setRuntimeId:(NSInteger runtimeId);
  public void LynxBaseLogBoxProxy-p::showConsole();
  public void LynxBaseLogBoxProxy-p::destroy();
  public void LynxBaseLogBoxProxy-p::setLynxDevtool:(LynxDevtool *devtool);
}

public class LynxBaseTextShadowNode : LynxShadowNode {
  public LynxTextStyle* LynxBaseTextShadowNode::textStyle textStyle;
  public BOOL LynxBaseTextShadowNode::hasNonVirtualOffspring hasNonVirtualOffspring;
  public BOOL LynxBaseTextShadowNode::enableTextRefactor enableTextRefactor;
  public BOOL LynxBaseTextShadowNode::enableNewClipMode enableNewClipMode;
  public NSString* LynxBaseTextShadowNode::text text;
  public NSAttributedString * LynxBaseTextShadowNode::generateAttributedString:withTextMaxLength:(nullable NSDictionary< NSAttributedStringKey, id > *baseTextAttribute,[withTextMaxLength] NSInteger __deprecated);
  public NSAttributedString * LynxBaseTextShadowNode::generateAttributedString:withTextMaxLength:withDirection:(nullable NSDictionary< NSAttributedStringKey, id > *baseTextAttribute,[withTextMaxLength] NSInteger textMaxLength,[withDirection] NSWritingDirection direction);
  public void LynxBaseTextShadowNode::alignHiddenNativeLayoutNode:alignContext:(NSSet *alignedNodeSignSet,[alignContext] AlignContext *ctx);
  public void LynxBaseTextShadowNode::markStyleDirty();
}

public class LynxBasicShape : NSObject {
  public UIBezierPath * LynxBasicShape::pathWithFrameSize:(CGSize frameSize);
}

public class LynxBlurImageProcessor : NSObject, <LynxImageProcessor> {
  public instancetype LynxBlurImageProcessor::initWithBlurRadius:(CGFloat radius);
}

public struct LynxBorderColors {
  public CGColorRef LynxBorderColors::top top;
  public CGColorRef LynxBorderColors::left left;
  public CGColorRef LynxBorderColors::bottom bottom;
  public CGColorRef LynxBorderColors::right right;
}

public class LynxBorderLayer : CAShapeLayer {
  public LynxBgTypes LynxBorderLayer::type type;
}

public struct LynxBorderRadii {
  public LynxBorderUnitValue LynxBorderRadii::topLeftX topLeftX;
  public LynxBorderUnitValue LynxBorderRadii::topLeftY topLeftY;
  public LynxBorderUnitValue LynxBorderRadii::topRightX topRightX;
  public LynxBorderUnitValue LynxBorderRadii::topRightY topRightY;
  public LynxBorderUnitValue LynxBorderRadii::bottomLeftX bottomLeftX;
  public LynxBorderUnitValue LynxBorderRadii::bottomLeftY bottomLeftY;
  public LynxBorderUnitValue LynxBorderRadii::bottomRightX bottomRightX;
  public LynxBorderUnitValue LynxBorderRadii::bottomRightY bottomRightY;
}

public struct LynxBorderStyles {
  public LynxBorderStyle LynxBorderStyles::top top;
  public LynxBorderStyle LynxBorderStyles::left left;
  public LynxBorderStyle LynxBorderStyles::bottom bottom;
  public LynxBorderStyle LynxBorderStyles::right right;
}

public class LynxBounceView : LynxUIView {
  public LynxBounceViewDirection LynxBounceView::direction direction;
  public float LynxBounceView::space space;
  public CGFloat LynxBounceView::triggerBounceEventDistance triggerBounceEventDistance;
}

public protocol LynxBounceView-p : <NSObject> {
  public void LynxBounceView-p::bdx_updateOverflowText:(nullable NSString *text);
}

public class LynxBoxShadow : NSObject {
  public UIColor* LynxBoxShadow::shadowColor shadowColor;
  public CGFloat LynxBoxShadow::offsetX offsetX;
  public CGFloat LynxBoxShadow::offsetY offsetY;
  public CGFloat LynxBoxShadow::blurRadius blurRadius;
  public CGFloat LynxBoxShadow::spreadRadius spreadRadius;
  public BOOL LynxBoxShadow::inset inset;
  public CALayer* LynxBoxShadow::layer layer;
  public BOOL LynxBoxShadow::isEqualToBoxShadow:(LynxBoxShadow *other);
}

public class LynxBoxShadowLayer : CALayer {
  public UIColor* LynxBoxShadowLayer::customShadowColor customShadowColor;
  public CGFloat LynxBoxShadowLayer::customShadowBlur customShadowBlur;
  public CGSize LynxBoxShadowLayer::customShadowOffset customShadowOffset;
  public CGPathRef LynxBoxShadowLayer::maskPath maskPath;
  public CGPathRef LynxBoxShadowLayer::customShadowPath customShadowPath;
  public LynxUI* LynxBoxShadowLayer::ui ui;
  public BOOL LynxBoxShadowLayer::inset inset;
  public id LynxBoxShadowLayer::initWithUi:(LynxUI *_Nullable ui);
  public void LynxBoxShadowLayer::invalidate();
}

public class LynxCallStackUtil : NSObject {
  public NSString * LynxCallStackUtil::getCallStack();
  public NSString * LynxCallStackUtil::getCallStack:(NSException *e);
}

public class LynxCollectionDataSource : NSObject, <UICollectionViewDataSource> {
  public BOOL LynxCollectionDataSource::ignoreLoadCell ignoreLoadCell;
  public instancetype LynxCollectionDataSource::initWithLynxUICollection:(LynxUICollection *collection);
  public void LynxCollectionDataSource::apply();
  public BOOL LynxCollectionDataSource::applyFirstTime();
}

public class LynxCollectionInvalidationContext : UICollectionViewLayoutInvalidationContext {
  public InvalidationType LynxCollectionInvalidationContext::invalidationType invalidationType;
  public NSArray<NSIndexPath*>* LynxCollectionInvalidationContext::removals removals;
  public NSArray<NSIndexPath*>* LynxCollectionInvalidationContext::insertions insertions;
  public NSArray<NSIndexPath*>* LynxCollectionInvalidationContext::moveFrom moveFrom;
  public NSArray<NSIndexPath*>* LynxCollectionInvalidationContext::moveTo moveTo;
  public NSDictionary<NSIndexPath*, LynxCollectionViewLayoutModel*>* LynxCollectionInvalidationContext::updates updates;
  public NSArray<NSIndexPath*>* LynxCollectionInvalidationContext::fullSpanItems fullSpanItems;
  public NSArray<NSIndexPath*>* LynxCollectionInvalidationContext::stickyTopItems stickyTopItems;
  public NSArray<NSIndexPath*>* LynxCollectionInvalidationContext::stickyBottomItems stickyBottomItems;
  public CGRect LynxCollectionInvalidationContext::bounds bounds;
  public BOOL LynxCollectionInvalidationContext::hasInsetUpdates hasInsetUpdates;
  public UIEdgeInsets LynxCollectionInvalidationContext::insets insets;
  public NSInteger LynxCollectionInvalidationContext::numberOfItems numberOfItems;
  public NSInteger LynxCollectionInvalidationContext::numberOfColumns numberOfColumns;
  public CGFloat LynxCollectionInvalidationContext::mainAxisGap mainAxisGap;
  public CGFloat LynxCollectionInvalidationContext::crossAxisGap crossAxisGap;
  public LynxCollectionViewLayoutType LynxCollectionInvalidationContext::layoutType layoutType;
  public BOOL LynxCollectionInvalidationContext::animated animated;
  public BOOL LynxCollectionInvalidationContext::didSetInitialScrollIndex didSetInitialScrollIndex;
  public BOOL LynxCollectionInvalidationContext::selfSizing selfSizing;
  public CGPoint LynxCollectionInvalidationContext::currentContentOffset currentContentOffset;
  public NSIndexPath* LynxCollectionInvalidationContext::indexPathContainsContentOffset indexPathContainsContentOffset;
  public NSDictionary<NSIndexPath*, NSNumber*>* LynxCollectionInvalidationContext::estimatedHeights estimatedHeights;
  public instancetype LynxCollectionInvalidationContext::initWithNumberOfItemsChanging:fullSpanItems:stickyTopItems:stickyBottomItems:estimatedHeights:(NSInteger number,[fullSpanItems] NSArray< NSIndexPath * > *fullSpanItems,[stickyTopItems] NSArray< NSIndexPath * > *stickyTopItems,[stickyBottomItems] NSArray< NSIndexPath * > *stickyBottomItems,[estimatedHeights] NSDictionary< NSIndexPath *, NSNumber * > *estimatedHeights);
  public instancetype LynxCollectionInvalidationContext::initWithFullSpanItems:stickyTopItems:stickyBottomItems:estimatedHeights:(NSArray< NSIndexPath * > *fullSpanItems,[stickyTopItems] NSArray< NSIndexPath * > *stickyTopItems,[stickyBottomItems] NSArray< NSIndexPath * > *stickyBottomItems,[estimatedHeights] NSDictionary< NSIndexPath *, NSNumber * > *estimatedHeights);
  public instancetype LynxCollectionInvalidationContext::initWithLayoutTypeSwitching:(LynxCollectionViewLayoutType type);
  public instancetype LynxCollectionInvalidationContext::initWithNumberOfColumnsChanging:(NSUInteger numberOfColumns);
  public instancetype LynxCollectionInvalidationContext::initWithMainAxisGapChanging:(CGFloat mainAxisGap);
  public instancetype LynxCollectionInvalidationContext::initWithCrossAxisGapChanging:(CGFloat crossAxisGap);
  public instancetype LynxCollectionInvalidationContext::initWithBoundsChanging:(CGRect bounds);
  public instancetype LynxCollectionInvalidationContext::initWithInsetChanging:(UIEdgeInsets insets);
  public instancetype LynxCollectionInvalidationContext::initWithRemovals:insertions:moveFrom:moveTo:fullSpanItems:stickyTopItems:stickyBottomItems:estimatedHeights:(NSArray< NSIndexPath * > *removals,[insertions] NSArray< NSIndexPath * > *insertions,[moveFrom] NSArray< NSIndexPath * > *moveFrom,[moveTo] NSArray< NSIndexPath * > *moveTo,[fullSpanItems] NSArray< NSIndexPath * > *fullSpanItems,[stickyTopItems] NSArray< NSIndexPath * > *stickyTopItems,[stickyBottomItems] NSArray< NSIndexPath * > *stickyBottomItems,[estimatedHeights] NSDictionary< NSIndexPath *, NSNumber * > *estimatedHeights);
  public instancetype LynxCollectionInvalidationContext::initWithUpdateAtIndexPath:bounds:(NSIndexPath *indexPath,[bounds] CGRect bounds);
  public instancetype LynxCollectionInvalidationContext::initWithResetAnimationTo:(BOOL animated);
  public instancetype LynxCollectionInvalidationContext::initWithSelfSizingCellAtIndexPath:bounds:collectionView:isHorizontal:(NSIndexPath *indexPath,[bounds] CGRect bounds,[collectionView] UICollectionView *collectionView,[isHorizontal] BOOL isHorizontal);
  public instancetype LynxCollectionInvalidationContext::initWithInitialScrollIndexSet();
}

public class LynxCollectionScroll : NSObject {
  public NSInteger LynxCollectionScroll::initialScrollIndex initialScrollIndex;
  public BOOL LynxCollectionScroll::horizontalLayout horizontalLayout;
  public instancetype LynxCollectionScroll::init();
  public void LynxCollectionScroll::scrollCollectionView:position:offset:alignTo:smooth:useScroller:callback:(UICollectionView *collectionView,[position] NSInteger position,[offset] CGFloat offset,[alignTo] NSString *alignTo,[smooth] BOOL smooth,[useScroller] BOOL useScroller,[callback] LynxUIMethodCallbackBlock callback);
  public void LynxCollectionScroll::updateWithInvalidationContext:(LynxCollectionInvalidationContext *context);
  public void LynxCollectionScroll::updateWithCellLoadedAtIndexPath:(NSIndexPath *indexPath);
  public void LynxCollectionScroll::initialScrollCollectionViewIfNeeded:(LynxCollectionViewLayout *layout);
  public void LynxCollectionScroll::updateLastIndexPathWithValidLayoutAttributes:(LynxCollectionInvalidationContext *context);
  public void LynxCollectionScroll::collectionViewDidScroll:(UICollectionView *collectionView);
  public void LynxCollectionScroll::collectionViewDidEndScrollingAnimation:(UICollectionView *collectionView);
  public void LynxCollectionScroll::collectionViewWillBeginDragging:(UICollectionView *collectionView);
}

public class LynxCollectionScroller : NSObject {
  public BOOL LynxCollectionScroller::horizontalLayout horizontalLayout;
  public instancetype LynxCollectionScroller::initWithTargetIndexPath:scrollDown:scrollToInvisibleRect:scrollPosition:offset:sticky:delegate:completion:(NSIndexPath *targetIndexPath,[scrollDown] BOOL willScrollDown,[scrollToInvisibleRect] BOOL willScrollToInvisibleRect,[scrollPosition] UICollectionViewScrollPosition scrollPosition,[offset] CGFloat offset,[sticky] BOOL sticky,[delegate] id< LynxCollectionScrollerHolderDelegate > delegate,[completion] nonnull void(^ completion)(BOOL success));
  public void LynxCollectionScroller::collectionViewDidScroll:(UICollectionView *collectionView);
  public void LynxCollectionScroller::collectionViewDidEndScrollingAnimation:(UICollectionView *collectionView);
  public void LynxCollectionScroller::collectionViewWillBeginDragging:(UICollectionView *collectionView);
  public void LynxCollectionScroller::collectionViewStartScroll:animated:(UICollectionView *collectionView,[animated] BOOL animated);
  public void LynxCollectionScroller::stopScroll();
}

public protocol LynxCollectionScrollerHolderDelegate-p : <NSObject> {
  public void LynxCollectionScrollerHolderDelegate-p::removeScroller();
}

public class LynxCollectionViewCell : UICollectionViewCell {
  public LynxUIComponent* LynxCollectionViewCell::ui ui;
  public NSIndexPath* LynxCollectionViewCell::updateToPath updateToPath;
  public BOOL LynxCollectionViewCell::loading loading;
  public NSString* LynxCollectionViewCell::itemKey itemKey;
  public int64_t LynxCollectionViewCell::operationID operationID;
  public BOOL LynxCollectionViewCell::isPartOnLayout isPartOnLayout;
  public LynxUI * LynxCollectionViewCell::removeLynxUI();
  public void LynxCollectionViewCell::addLynxUI:(LynxUI *ui);
  public void LynxCollectionViewCell::adjustComponentFrame();
  public void LynxCollectionViewCell::restartAnimation();
}

public class LynxCollectionViewLayout : UICollectionViewLayout {
  public LynxCollectionScroll* LynxCollectionViewLayout::scroll scroll;
  public BOOL LynxCollectionViewLayout::indexAsZIndex indexAsZIndex;
  public NSIndexPath* LynxCollectionViewLayout::targetIndexPathAfterBatchUpdate targetIndexPathAfterBatchUpdate;
  public CGFloat LynxCollectionViewLayout::targetOffsetDeltaAfterBatchUpdate targetOffsetDeltaAfterBatchUpdate;
  public BOOL LynxCollectionViewLayout::needsAdjustContentOffsetForSelfSizingCells needsAdjustContentOffsetForSelfSizingCells;
  public BOOL LynxCollectionViewLayout::needUpdateValidLayoutAttributesAfterDiff needUpdateValidLayoutAttributesAfterDiff;
  public BOOL LynxCollectionViewLayout::enableAlignHeight enableAlignHeight;
  public void LynxCollectionViewLayout::setEnableSticky:(BOOL enableSticky);
  public void LynxCollectionViewLayout::setHorizontalLayout:(BOOL useHorizontalLayout);
  public void LynxCollectionViewLayout::setStickyOffset:(CGFloat stickOffset);
  public void LynxCollectionViewLayout::prepareForCellLayoutUpdate();
  public void LynxCollectionViewLayout::setEnableAlignHeight:(BOOL enableAlignHeight);
  public void LynxCollectionViewLayout::setFixSelfSizingOffsetFromStart:(BOOL fromStart);
  public void LynxCollectionViewLayout::setUseOldSticky:(BOOL useOldSticky);
  public void LynxCollectionViewLayout::setStickyWithBounces:(BOOL stickyWithBounces);
  public BOOL LynxCollectionViewLayout::isStickyItem:(NSIndexPath *indexPath);
  public UICollectionViewLayoutAttributes * LynxCollectionViewLayout::layoutAttributesForStickItemAtIndexPath:(NSIndexPath *indexPath);
}

public class LynxCollectionViewLayoutModel : NSObject, <NSCopying> {
  public CGRect LynxCollectionViewLayoutModel::frame frame;
  public instancetype LynxCollectionViewLayoutModel::modelWithBounds:(CGRect bound);
  public instancetype LynxCollectionViewLayoutModel::modelWithHeight:(CGFloat height);
  public instancetype LynxCollectionViewLayoutModel::modelWithWidth:(CGFloat width);
  public instancetype LynxCollectionViewLayoutModel::modelWithDefaultSize();
  public CGFloat LynxCollectionViewLayoutModel::defaultHeight();
  public CGFloat LynxCollectionViewLayoutModel::defaultWidth();
}

public class LynxCollectionViewLayoutSectionModel : NSObject {
  public BOOL LynxCollectionViewLayoutSectionModel::needAlignHeight needAlignHeight;
  public BOOL LynxCollectionViewLayoutSectionModel::useOldSticky useOldSticky;
  public BOOL LynxCollectionViewLayoutSectionModel::horizontalLayout horizontalLayout;
  public BOOL LynxCollectionViewLayoutSectionModel::fixSelfSizingOffsetFromStart fixSelfSizingOffsetFromStart;
  public BOOL LynxCollectionViewLayoutSectionModel::enableSticky enableSticky;
  public CGFloat LynxCollectionViewLayoutSectionModel::stickyOffset stickyOffset;
  public BOOL LynxCollectionViewLayoutSectionModel::indexAsZIndex indexAsZIndex;
  public BOOL LynxCollectionViewLayoutSectionModel::stickyWithBounces stickyWithBounces;
  public instancetype LynxCollectionViewLayoutSectionModel::initWithItemCount:(NSUInteger length);
  public void LynxCollectionViewLayoutSectionModel::updateWithInvalidationContext:collectionView:(LynxCollectionInvalidationContext *context,[collectionView] UICollectionView *collectionView);
  public void LynxCollectionViewLayoutSectionModel::layoutIfNeededForUICollectionView:(UICollectionView *collectionView);
  public NSArray< __kindof UICollectionViewLayoutAttributes * > * LynxCollectionViewLayoutSectionModel::layoutAttributesForElementsInRect:(CGRect rect);
  public __kindof UICollectionViewLayoutAttributes * LynxCollectionViewLayoutSectionModel::layoutAttributeForElementAtIndexPath:(NSIndexPath *indexPath);
  public CGSize LynxCollectionViewLayoutSectionModel::contentSize();
  public void LynxCollectionViewLayoutSectionModel::prepareForCollectionViewUpdates:(NSArray< UICollectionViewUpdateItem * > *updateItems);
  public __kindof UICollectionViewLayoutAttributes * LynxCollectionViewLayoutSectionModel::initialLayoutAttributesForAppearingItemAtIndexPath:defaultLayoutAttributes:inCollectionView:(NSIndexPath *itemIndexPath,[defaultLayoutAttributes] UICollectionViewLayoutAttributes *defaultAttributes,[inCollectionView] UICollectionView *__nullable collectionView);
  public __kindof UICollectionViewLayoutAttributes * LynxCollectionViewLayoutSectionModel::finalLayoutAttributesForDisappearingItemAtIndexPath:(NSIndexPath *itemIndexPath);
  public void LynxCollectionViewLayoutSectionModel::finalizeCollectionViewUpdates();
  public void LynxCollectionViewLayoutSectionModel::setBounds:(CGRect newBounds);
  public void LynxCollectionViewLayoutSectionModel::adjustCollectionViewContentOffsetForSelfSizingCellInvaldationIfNeeded:(UICollectionView *collectionView);
  public void LynxCollectionViewLayoutSectionModel::prepareForCellLayoutUpdate();
  public UICollectionViewLayoutAttributes * LynxCollectionViewLayoutSectionModel::layoutAttributesFromCacheAtRow:(NSInteger row);
  public BOOL LynxCollectionViewLayoutSectionModel::isStickyItem:(NSInteger item);
}

public class LynxColorUtils : NSObject {
  public nullable COLOR_CLASS * LynxColorUtils::convertNSStringToUIColor:(NSString *value);
}

public class LynxComponent : <__covariant D> {
  public NSMutableArray<D>* LynxComponent::children children;
  public void LynxComponent::insertChild:atIndex:(D _Nonnull child,[atIndex] NSInteger index);
  public void LynxComponent::removeChild:atIndex:(D _Nonnull child,[atIndex] NSInteger index);
  public void LynxComponent::didAddSubComponent:(nonnull D subComponent);
  public void LynxComponent::willRemoveComponent:(nonnull D subComponent);
  public void LynxComponent::willMoveToSuperComponent:(nullable D newSuperComponent);
  public void LynxComponent::didMoveToSuperComponet();
  public void LynxComponent::propsDidUpdate();
  public void LynxComponent::animationPropsDidUpdate();
  public void LynxComponent::transformPropsDidUpdate();
  public void LynxComponent::onNodeReady();
  public void LynxComponent::onNodeRemoved();
  public void LynxComponent::onNodeReload();
}

public class LynxComponentRegistry : NSObject {
  public void LynxComponentRegistry::registerUI:nameAs:(Class componentClass,[nameAs] NSString *name);
  public void LynxComponentRegistry::registerNode:nameAs:(Class componentClass,[nameAs] NSString *name);
  public void LynxComponentRegistry::registerUI:withName:(Class componentClass,[withName] NSString *name);
  public void LynxComponentRegistry::registerShadowNode:withName:(Class componentClass,[withName] NSString *name);
  public Class LynxComponentRegistry::shadowNodeClassWithName:accessible:(NSString *name,[accessible] BOOL *legal);
  public Class LynxComponentRegistry::uiClassWithName:accessible:(NSString *name,[accessible] BOOL *legal);
  public NSSet< NSString * > * LynxComponentRegistry::lynxUIClasses();
}

public class LynxComponentScopeRegistry : NSObject {
  public NSSet<NSString*>* LynxComponentScopeRegistry::allRegisteredComponent allRegisteredComponent;
  public void LynxComponentScopeRegistry::registerUI:withName:(Class componentClass,[withName] NSString *name);
  public void LynxComponentScopeRegistry::registerShadowNode:withName:(Class componentClass,[withName] NSString *name);
  public Class LynxComponentScopeRegistry::shadowNodeClassWithName:accessible:(NSString *name,[accessible] BOOL *legal);
  public Class LynxComponentScopeRegistry::uiClassWithName:accessible:(NSString *name,[accessible] BOOL *legal);
  public void LynxComponentScopeRegistry::makeIntoGloabl();
}

public class LynxConfig : NSObject {
  public id<LynxTemplateProvider> LynxConfig::templateProvider templateProvider;
  public LynxComponentScopeRegistry* LynxConfig::componentRegistry componentRegistry;
  public NSMutableDictionary* LynxConfig::contextDict contextDict;
  public (deprecated("Use [LynxEnv sharedInstance].config instead.")) LynxConfig::__attribute__();
  public void LynxConfig::prepareGlobalConfig:((deprecated("Use [[LynxEnv sharedInstance] prepareConfig:config] instead.")) __attribute__);
  public instancetype LynxConfig::NS_UNAVAILABLE();
  public instancetype LynxConfig::initWithProvider:(nullable id< LynxTemplateProvider > provider);
  public void LynxConfig::registerModule:(Class< LynxModule > module);
  public void LynxConfig::registerModule:param:(Class< LynxModule > module,[param] nullable id param);
  public void LynxConfig::registerUI:withName:(Class ui,[withName] NSString *name);
  public void LynxConfig::registerShadowNode:withName:(Class node,[withName] NSString *name);
  public void LynxConfig::registerMethodAuth:(LynxMethodBlock authBlock);
  public void LynxConfig::registerContext:sessionInfo:(NSDictionary *ctxDict,[sessionInfo] LynxMethodSessionBlock sessionInfo);
  public std::shared_ptr< lynx::piper::ModuleFactoryDarwin > LynxConfig()::moduleFactoryPtr();
}

public class LynxConfigInfo : NSObject {
  public NSString* LynxConfigInfo::pageVersion pageVersion;
  public NSString* LynxConfigInfo::pageType pageType;
  public NSString* LynxConfigInfo::cliVersion cliVersion;
  public NSString* LynxConfigInfo::customData customData;
  public NSString* LynxConfigInfo::templateUrl templateUrl;
  public NSString* LynxConfigInfo::targetSdkVersion targetSdkVersion;
  public NSString* LynxConfigInfo::lepusVersion lepusVersion;
  public LynxThreadStrategyForRender LynxConfigInfo::threadStrategyForRendering threadStrategyForRendering;
  public BOOL LynxConfigInfo::enableLepusNG enableLepusNG;
  public NSString* LynxConfigInfo::radonMode radonMode;
  public NSString* LynxConfigInfo::reactVersion reactVersion;
  public NSSet<NSString*>* LynxConfigInfo::registeredComponent registeredComponent;
  public NSData* LynxConfigInfo::json json;
  public BOOL LynxConfigInfo::cssAlignWithLegacyW3c cssAlignWithLegacyW3c;
  public BOOL LynxConfigInfo::enableCSSParser enableCSSParser;
}

public class LynxConfigInfoBuilder : NSObject {
  public NSString* LynxConfigInfoBuilder::pageVersion pageVersion;
  public NSString* LynxConfigInfoBuilder::pageType pageType;
  public NSString* LynxConfigInfoBuilder::cliVersion cliVersion;
  public NSString* LynxConfigInfoBuilder::customData customData;
  public NSString* LynxConfigInfoBuilder::templateUrl templateUrl;
  public NSString* LynxConfigInfoBuilder::targetSdkVersion targetSdkVersion;
  public NSString* LynxConfigInfoBuilder::lepusVersion lepusVersion;
  public LynxThreadStrategyForRender LynxConfigInfoBuilder::threadStrategyForRendering threadStrategyForRendering;
  public BOOL LynxConfigInfoBuilder::enableLepusNG enableLepusNG;
  public NSString* LynxConfigInfoBuilder::radonMode radonMode;
  public NSSet<NSString*>* LynxConfigInfoBuilder::registeredComponent registeredComponent;
  public BOOL LynxConfigInfoBuilder::cssAlignWithLegacyW3c cssAlignWithLegacyW3c;
  public BOOL LynxConfigInfoBuilder::enableCSSParser enableCSSParser;
  public NSString* LynxConfigInfoBuilder::reactVersion reactVersion;
  public instancetype LynxConfigInfoBuilder::init();
  public LynxConfigInfo * LynxConfigInfoBuilder::build();
}

public class LynxContext : NSObject {
  public void LynxContext::sendGlobalEvent:withParams:(nonnull NSString *name,[withParams] nullable NSArray *params);
  public nullable JSModule * LynxContext::getJSModule:(nonnull NSString *name);
  public nullable NSNumber * LynxContext::getLynxRuntimeId();
  public void LynxContext::reportModuleCustomError:(NSString *message);
  public nullable LynxView * LynxContext::getLynxView();
  public void LynxContext::runOnTasmThread:(dispatch_block_t task);
  public void LynxContext::runOnJSThread:(dispatch_block_t task);
  public void LynxContext::setExtensionModule:forKey:(nonnull id< LynxExtensionModule > extensionModule,[forKey] nonnull NSString *key);
  public nonnull id< LynxExtensionModule > LynxContext::getExtensionModuleByKey:(nonnull NSString *key);
  public nonnull instancetype LynxContext()::initWithLynxView:(LynxView *_Nullable lynxView);
  public void LynxContext()::setJSProxy:(const std::shared_ptr< lynx::shell::JSProxyDarwin > &proxy);
  public nullable NSDictionary * LynxContext()::extentionModules();
  public std::shared_ptr<lynx::shell::JSProxyDarwin> LynxContext()::proxy_ proxy_;
  public LynxUIOwner* _Nullable LynxContext()::uiOwner uiOwner;
  public LynxUIIntersectionObserverManager* _Nullable LynxContext()::intersectionManager intersectionManager;
  public LynxView* _Nullable LynxContext()::lynxView lynxView;
  public int32_t LynxContext()::instanceId instanceId;
}

public protocol LynxContextModule-p : <LynxModule> {
  public instancetype LynxContextModule-p::initWithLynxContext:(LynxContext *context);
  public instancetype LynxContextModule-p::initWithLynxContext:WithParam:(LynxContext *context,[WithParam] id param);
  public void LynxContextModule-p::destroy();
}

public class LynxConverter : NSObject {
  public CGFloat LynxConverter::toCGFloat:(id value);
  public NSInteger LynxConverter::toNSInteger:(id value);
  public int LynxConverter::toint:(id value);
  public NSUInteger LynxConverter::toNSUInteger:(id value);
  public NSString * LynxConverter::toNSString:(id value);
  public CGColorRef LynxConverter::toCGColorRef:(id value);
  public BOOL LynxConverter::toBOOL:(id value);
  public NSTimeInterval LynxConverter::toNSTimeInterval:(id value);
  public NSNumber * LynxConverter::toNSNumber:(id value);
  public id LynxConverter::toid:(id value);
  public CAMediaTimingFunction * LynxConverter(CAMediaTimingFunction)::toCAMediaTimingFunction:(id __nullable value);
  public LynxAnimationInfo * LynxConverter(LynxAnimationInfo)::toKeyframeAnimationInfo:(id value);
  public LynxAnimationInfo * LynxConverter(LynxAnimationInfo)::toTransitionAnimationInfo:(id value);
  public LynxAnimationProp LynxConverter(LynxAnimationPropType)::toLynxAnimationProp:(id value);
  public NSString * LynxConverter(LynxAnimationPropType)::toLynxPropName:(LynxAnimationProp prop);
  public LynxBorderStyle LynxConverter(LynxBorderStyle)::toLynxBorderStyle:(id value);
  public NSArray< LynxBoxShadow * > * LynxConverter(LynxBoxShadow)::toLynxBoxShadow:(id value);
  public NSShadow * LynxConverter(NSShadow)::toNSShadow:(NSArray< LynxBoxShadow * > *shadowArr);
  public CATransform3D LynxConverter(Transform)::toCATransform3D:rotationType:rotationX:rotationY:rotationZ:ui:(NSArray< LynxTransformRaw * > *value, [rotationType] char *rotationType, [rotationX] CGFloat *currentRotationX, [rotationY] CGFloat *currentRotationY, [rotationZ] CGFloat *currentRotationZ, [ui] LynxUI *ui);
  public CATransform3D LynxConverter(Transform)::toCATransform3D:ui:newFrame:transformWithoutRotate:transformWithoutRotateXY:rotationType:rotationX:rotationY:rotationZ:(NSArray< LynxTransformRaw * > *value, [ui] LynxUI *ui, [newFrame] CGRect frame, [transformWithoutRotate] CATransform3D *transformWithoutRotate, [transformWithoutRotateXY] CATransform3D *transformWithoutRotateXY, [rotationType] char *rotationType, [rotationX] CGFloat *rotationX, [rotationY] CGFloat *rotationY, [rotationZ] CGFloat *rotationZ);
  public CATransform3D LynxConverter(Transform)::toCATransform3D:ui:newFrame:rotationType:rotationX:rotationY:rotationZ:(NSArray< LynxTransformRaw * > *value, [ui] LynxUI *ui, [newFrame] CGRect frame, [rotationType] char *rotationType, [rotationX] CGFloat *currentRotationX, [rotationY] CGFloat *currentRotationY, [rotationZ] CGFloat *currentRotationZ);
  public CATransform3D LynxConverter(Transform)::toCATransform3D:ui:(NSArray< LynxTransformRaw * > *value, [ui] LynxUI *ui);
  public BOOL LynxConverter(Transform)::isDefaultTransformOrigin:(LynxTransformOriginRaw *transformOrigin);
  public COLOR_CLASS * LynxConverter(UI)::toUIColor:(id value);
}

public interface LynxConverter(CAMediaTimingFunction) {
  public CAMediaTimingFunction * LynxConverter(CAMediaTimingFunction)::toCAMediaTimingFunction:(id __nullable value);
}

public interface LynxConverter(LynxAnimationInfo) {
  public LynxAnimationInfo * LynxConverter(LynxAnimationInfo)::toKeyframeAnimationInfo:(id value);
  public LynxAnimationInfo * LynxConverter(LynxAnimationInfo)::toTransitionAnimationInfo:(id value);
}

public interface LynxConverter(LynxAnimationPropType) {
  public LynxAnimationProp LynxConverter(LynxAnimationPropType)::toLynxAnimationProp:(id value);
  public NSString * LynxConverter(LynxAnimationPropType)::toLynxPropName:(LynxAnimationProp prop);
}

public interface LynxConverter(LynxBorderStyle) {
  public LynxBorderStyle LynxConverter(LynxBorderStyle)::toLynxBorderStyle:(id value);
}

public interface LynxConverter(LynxBoxShadow) {
  public NSArray< LynxBoxShadow * > * LynxConverter(LynxBoxShadow)::toLynxBoxShadow:(id value);
}

public interface LynxConverter(NSShadow) {
  public NSShadow * LynxConverter(NSShadow)::toNSShadow:(NSArray< LynxBoxShadow * > *shadowArr);
}

public interface LynxConverter(Transform) {
  public CATransform3D LynxConverter(Transform)::toCATransform3D:rotationType:rotationX:rotationY:rotationZ:ui:(NSArray< LynxTransformRaw * > *value,[rotationType] char *rotationType,[rotationX] CGFloat *currentRotationX,[rotationY] CGFloat *currentRotationY,[rotationZ] CGFloat *currentRotationZ,[ui] LynxUI *ui);
  public CATransform3D LynxConverter(Transform)::toCATransform3D:ui:newFrame:transformWithoutRotate:transformWithoutRotateXY:rotationType:rotationX:rotationY:rotationZ:(NSArray< LynxTransformRaw * > *value,[ui] LynxUI *ui,[newFrame] CGRect frame,[transformWithoutRotate] CATransform3D *transformWithoutRotate,[transformWithoutRotateXY] CATransform3D *transformWithoutRotateXY,[rotationType] char *rotationType,[rotationX] CGFloat *rotationX,[rotationY] CGFloat *rotationY,[rotationZ] CGFloat *rotationZ);
  public CATransform3D LynxConverter(Transform)::toCATransform3D:ui:newFrame:rotationType:rotationX:rotationY:rotationZ:(NSArray< LynxTransformRaw * > *value,[ui] LynxUI *ui,[newFrame] CGRect frame,[rotationType] char *rotationType,[rotationX] CGFloat *currentRotationX,[rotationY] CGFloat *currentRotationY,[rotationZ] CGFloat *currentRotationZ);
  public CATransform3D LynxConverter(Transform)::toCATransform3D:ui:(NSArray< LynxTransformRaw * > *value,[ui] LynxUI *ui);
  public BOOL LynxConverter(Transform)::isDefaultTransformOrigin:(LynxTransformOriginRaw *transformOrigin);
}

public interface LynxConverter(UI) {
  public COLOR_CLASS * LynxConverter(UI)::toUIColor:(id value);
}

public class LynxConvertUtils : NSObject {
  public NSString * LynxConvertUtils::convertToJsonData:(NSDictionary *dict);
}

public struct LynxCornerInsetPoints {
  public CGPoint LynxCornerInsetPoints::topLeft topLeft;
  public CGPoint LynxCornerInsetPoints::topRight topRight;
  public CGPoint LynxCornerInsetPoints::bottomLeft bottomLeft;
  public CGPoint LynxCornerInsetPoints::bottomRight bottomRight;
}

public struct LynxCornerInsets {
  public CGSize LynxCornerInsets::topLeft topLeft;
  public CGSize LynxCornerInsets::topRight topRight;
  public CGSize LynxCornerInsets::bottomLeft bottomLeft;
  public CGSize LynxCornerInsets::bottomRight bottomRight;
}

public class LynxCustomEvent : LynxEvent {
  public NSMutableDictionary* LynxCustomEvent::params params;
  public instancetype LynxCustomEvent::initWithName:targetSign:(NSString *name,[targetSign] NSInteger target);
  public instancetype LynxCustomEvent::initWithName:targetSign:params:(NSString *name,[targetSign] NSInteger target,[params] nullable NSDictionary *params);
  public instancetype LynxCustomEvent::initWithName:targetSign:currentTargetSign:params:(NSString *name,[targetSign] NSInteger target,[currentTargetSign] NSInteger currentTarget,[params] nullable NSDictionary *params);
  public void LynxCustomEvent::addDetailKey:value:(NSString *key,[value] NSObject *value);
  public NSString * LynxCustomEvent::paramsName();
}

public protocol LynxCustomMeasureDelegate-p : <NSObjectNSObject> {
  public MeasureResult LynxCustomMeasureDelegate-p::measureWithMeasureParam:MeasureContext:(MeasureParam *param,[MeasureContext] nullable MeasureContext *context);
  public void LynxCustomMeasureDelegate-p::alignWithAlignParam:AlignContext:(AlignParam *param,[AlignContext] AlignContext *context);
}

public class LynxCustomMeasureShadowNode : LynxShadowNode {
  public BOOL LynxCustomMeasureShadowNode::hasCustomLayout hasCustomLayout;
  public MeasureResult LynxCustomMeasureShadowNode::measureNativeLayoutNode:measureContext:(nonnull MeasureParam *param,[measureContext] nullable MeasureContext *context);
  public void LynxCustomMeasureShadowNode::alignNativeLayoutNode:alignContext:(nonnull AlignParam *param,[alignContext] nonnull AlignContext *context);
  public MeasureResult LynxCustomMeasureShadowNode::customMeasureLayoutNode:measureContext:(nonnull MeasureParam *param,[measureContext] nullable MeasureContext *context);
  public void LynxCustomMeasureShadowNode::customAlignLayoutNode:alignContext:(nonnull AlignParam *param,[alignContext] nonnull AlignContext *context);
  public CGFloat LynxCustomMeasureShadowNode::toPtWithUnitValue:fontSize:(NSString *unitValue,[fontSize] CGFloat fontSize);
}

public protocol LynxCustomScrollDelegate-p :  {
  public void LynxCustomScrollDelegate-p::autoScrollStop();
}

public class LynxDebugger : NSObject {
  public BOOL LynxDebugger::enable:withOptions:(NSURL *schema,[withOptions] NSDictionary *options);
  public void LynxDebugger::setOpenCardCallback:((deprecated("Use addOpenCardCallback instead after lynx 2.6")) __attribute__);
  public void LynxDebugger::addOpenCardCallback:(LynxOpenCardCallback callback);
  public BOOL LynxDebugger::hasSetOpenCardCallback();
  public BOOL LynxDebugger::openDebugSettingPanel();
  public void LynxDebugger::onPerfMetricsEvent:withData:instanceId:(NSString *_Nonnull eventName,[withData] NSDictionary< NSString *, NSObject * > *_Nonnull data,[instanceId] int32_t instanceId);
}

public protocol LynxDebuggerProtocol-p : <NSObject> {
  public instancetype LynxDebuggerProtocol-p::singleton();
  public BOOL LynxDebuggerProtocol-p::enable:withOptions:(NSURL *url,[withOptions] NSDictionary *options);
  public void LynxDebuggerProtocol-p::setOpenCardCallback:(LynxOpenCardCallback callback);
}

public class LynxDetailEvent : LynxCustomEvent {
  public instancetype LynxDetailEvent::initWithName:targetSign:detail:(NSString *name,[targetSign] NSInteger target,[detail] nullable NSDictionary *detail);
  public instancetype LynxDetailEvent::initWithName:targetSign:currentTargetSign:detail:(NSString *name,[targetSign] NSInteger target,[currentTargetSign] NSInteger currentTarget,[detail] nullable NSDictionary *detail);
}

public class LynxDevtool : NSObject {
  public id<LynxBaseInspectorOwner> LynxDevtool::owner owner;
  public nonnull instancetype LynxDevtool::initWithLynxView:debuggable:(LynxView *view,[debuggable] BOOL debuggable);
  public void LynxDevtool::registerModule:(LynxTemplateRender *render);
  public void LynxDevtool::onLoadFromLocalFile:withURL:initData:(NSData *tem,[withURL] NSString *url,[initData] LynxTemplateData *data);
  public void LynxDevtool::onLoadFromURL:initData:postURL:(NSString *url,[initData] LynxTemplateData *data,[postURL] NSString *postURL);
  public void LynxDevtool::attachDebugBridge:(NSString *url);
  public void LynxDevtool::onLoadFromBundle:withURL:initData:(LynxTemplateBundle *bundle,[withURL] NSString *url,[initData] LynxTemplateData *data);
  public void LynxDevtool::onStandaloneRuntimeLoadFromURL:(NSString *url);
  public void LynxDevtool::onTemplateAssemblerCreated:(intptr_t ptr);
  public void LynxDevtool::onEnterForeground();
  public void LynxDevtool::onEnterBackground();
  public void LynxDevtool::onLoadFinished();
  public void LynxDevtool::handleLongPress();
  public void LynxDevtool::showErrorMessage:(LynxError *error);
  public void LynxDevtool::attachLynxView:(LynxView *lynxView);
  public void LynxDevtool::onMovedToWindow();
  public void LynxDevtool::onPageUpdate();
  public void LynxDevtool::downloadResource:callback:(NSString *_Nonnull url,[callback] LynxResourceLoadBlock _Nonnull callback);
  public void LynxDevtool::onPerfMetricsEvent:withData:(NSString *_Nonnull eventName,[withData] NSDictionary< NSString *, NSObject * > *_Nonnull data);
  public NSString * LynxDevtool::debugInfoUrl();
  public void LynxDevtool::onReceiveMessageEvent:(NSDictionary *event);
  public void LynxDevtool::setDispatchMessageEventBlock:(void(^ block)(NSDictionary *));
  public void LynxDevtool()::onTemplateLoadSuccess:(nullable NSData *tem);
  public void LynxDevtool()::onGlobalPropsUpdated:(LynxTemplateData *props);
}

public class LynxDevToolUtils : NSObject {
  public void LynxDevToolUtils::setDevtoolEnv:forKey:(BOOL value,[forKey] NSString *key);
  public BOOL LynxDevToolUtils::getDevtoolEnv:withDefaultValue:(NSString *key,[withDefaultValue] BOOL value);
  public void LynxDevToolUtils::setDevtoolEnv:forGroup:(NSSet *newGroupValues,[forGroup] NSString *groupKey);
  public NSSet * LynxDevToolUtils::getDevtoolEnvWithGroupName:(NSString *groupKey);
}

public protocol LynxDynamicComponentFetcher-p : <NSObject> {
  public void LynxDynamicComponentFetcher-p::loadDynamicComponent:withLoadedBlock:(NSString *url,[withLoadedBlock] onComponentLoaded block);
}

public class LynxEngineProxy : NSObject {
  public void LynxEngineProxy::dispatchTaskToLynxEngine:(dispatch_block_t task);
  public void LynxEngineProxy::invokeLepusFunc:callbackID:(NSDictionary *data,[callbackID] int32_t callbackID);
  public void LynxEngineProxy::sendSyncTouchEvent:(LynxTouchEvent *event);
  public void LynxEngineProxy::sendSyncMultiTouchEvent:(LynxTouchEvent *event);
  public void LynxEngineProxy::sendGestureEvent:event:(int gestureId,[event] LynxCustomEvent *event);
  public void LynxEngineProxy::sendCustomEvent:(LynxCustomEvent *event);
  public void LynxEngineProxy::onPseudoStatusChanged:fromPreStatus:toCurrentStatus:(int32_t tag,[fromPreStatus] int32_t preStatus,[toCurrentStatus] int32_t currentStatus);
  public void LynxEngineProxy::startEventGenerate:(LynxEvent *event);
  public void LynxEngineProxy::startEventCapture:(int64_t eventID);
  public void LynxEngineProxy::startEventBubble:(int64_t eventID);
  public void LynxEngineProxy::startEventFire:withEventID:(BOOL isStop,[withEventID] int64_t eventID);
  public void LynxEngineProxy()::setNativeEngineProxy:(std::shared_ptr< lynx::shell::LynxEngineProxyDarwin > proxy);
}

public class LynxEnv : NSObject {
  public LynxConfig* LynxEnv::config config;
  public NSString* LynxEnv::locale locale;
  public LynxLifecycleDispatcher* LynxEnv::lifecycleDispatcher lifecycleDispatcher;
  public NSMutableDictionary<NSString *, id<LynxResourceProvider> >* LynxEnv::resoureProviders resoureProviders;
  public BOOL LynxEnv::lynxDebugEnabled lynxDebugEnabled;
  public BOOL LynxEnv::devtoolComponentAttach devtoolComponentAttach;
  public BOOL LynxEnv::devtoolEnabled devtoolEnabled;
  public BOOL LynxEnv::devtoolEnabledForDebuggableView devtoolEnabledForDebuggableView;
  public BOOL LynxEnv::logBoxEnabled logBoxEnabled;
  public BOOL LynxEnv::highlightTouchEnabled highlightTouchEnabled;
  public BOOL LynxEnv::automationEnabled automationEnabled;
  public BOOL LynxEnv::layoutOnlyEnabled layoutOnlyEnabled;
  public BOOL LynxEnv::autoResumeAnimation autoResumeAnimation;
  public BOOL LynxEnv::enableNewTransformOrigin enableNewTransformOrigin;
  public BOOL LynxEnv::recordEnable recordEnable;
  public BOOL LynxEnv::launchRecordEnabled launchRecordEnabled;
  public void* LynxEnv::cronetEngine cronetEngine;
  public void* LynxEnv::cronetServerConfig cronetServerConfig;
  public BOOL LynxEnv::switchRunloopThread switchRunloopThread;
  public BOOL redBoxEnabled LynxEnv::__attribute__((deprecated("Please use logBoxEnabled to instead")));
  public BOOL enableDevMenu LynxEnv::__attribute__((deprecated("Use unified flag enableDevtoolDebug")));
  public BOOL enableJSDebug LynxEnv::__attribute__((deprecated("Use unified flag enableDevtoolDebug")));
  public BOOL enableDevtoolDebug LynxEnv::__attribute__((deprecated("Use devtoolEnabled")));
  public BOOL enableLogBox LynxEnv::__attribute__((deprecated("Use logBoxEnabled")));
  public void LynxEnv::prepareConfig:(LynxConfig *config);
  public void LynxEnv::reportModuleCustomError:(NSString *error);
  public void LynxEnv::onPiperInvoked:method:paramStr:url:sessionID:(NSString *module,[method] NSString *method,[paramStr] NSString *paramStr,[url] NSString *url,[sessionID] NSString *sessionID);
  public void LynxEnv::onPiperResponsed:method:url:response:sessionID:(NSString *module,[method] NSString *method,[url] NSString *url,[response] NSDictionary *response,[sessionID] NSString *sessionID);
  public void LynxEnv::updateSettings:(NSDictionary *settings);
  public void LynxEnv::addResoureProvider:provider:(NSString *key,[provider] id< LynxResourceProvider > provider);
  public BOOL LynxEnv::boolFromExternalEnv:defaultValue:(LynxEnvKey key,[defaultValue] BOOL defaultValue);
  public NSString * LynxEnv::stringFromExternalEnv:(LynxEnvKey key);
  public void LynxEnv::setLocalEnv:forKey:(NSString *value,[forKey] NSString *key);
  public void LynxEnv::setDevtoolEnv:forKey:(BOOL value,[forKey] NSString *key);
  public BOOL LynxEnv::getDevtoolEnv:withDefaultValue:(NSString *key,[withDefaultValue] BOOL value);
  public void LynxEnv::setDevtoolEnv:forGroup:(NSSet *newGroupValues,[forGroup] NSString *groupKey);
  public NSSet * LynxEnv::getDevtoolEnvWithGroupName:(NSString *groupKey);
  public void LynxEnv::setEnableRadonCompatible:((deprecated("Radon diff mode can't be close after lynx 2.3.")) __attribute__);
  public (deprecated("Radon diff mode can't be close after lynx 2.3.")) LynxEnv::__attribute__();
  public void LynxEnv::setEnableLayoutOnly:(BOOL value);
  public BOOL LynxEnv::getEnableLayoutOnly();
  public void LynxEnv::setPiperMonitorState:(BOOL state);
  public void LynxEnv::initLayoutConfig:(CGSize screenSize);
  public void LynxEnv::setAutoResumeAnimation:(BOOL value);
  public BOOL LynxEnv::getAutoResumeAnimation();
  public void LynxEnv::setEnableNewTransformOrigin:(BOOL value);
  public BOOL LynxEnv::getEnableNewTransformOrigin();
  public void LynxEnv::setCronetEngine:(void *engine);
  public void LynxEnv::setCronetServerConfig:(void *config);
  public void LynxEnv::enableFluencyTracer:(BOOL value);
  public BOOL LynxEnv::enableComponentStatisticReport();
  public BOOL LynxEnv::enableImageEventReport();
  public BOOL LynxEnv::enableGenericResourceFetcher();
  public BOOL LynxEnv::enableTextContainerOpt();
  public BOOL LynxEnv::enableTextStorageDeallocFix();
  public NSDictionary< NSString *, NSString * > * LynxEnv::cppEnvDebugDescription();
  public NSDictionary< NSString *, NSString * > * LynxEnv::platformEnvDebugDescription();
  public BOOL LynxEnv()::enableCreateUIAsync();
  public BOOL LynxEnv()::enableAnimationSyncTimeOpt();
  public NSString * LynxEnv()::_stringFromExternalEnv:(NSString *key);
  public void LynxEnv()::updateExternalEnvCacheForKey:withValue:(NSString *key, [withValue] NSString *value);
  public instancetype LynxEnv::sharedInstance();
  public NSString *_Nonnull LynxEnv::getSSRApiVersion();
  public void LynxEnv::clearBytecode:(nonnull NSString *bytecodeSourceUrl);
  public NSString * LynxEnv()::_keyStringFromType:(LynxEnvKey key);
  public BOOL LynxEnv()::stringValueToBool:defaultValue:(NSString *value, [defaultValue] BOOL defaultValue);
}

public class LynxError : NSError {
  public BOOL LynxError::isFatal isFatal;
  public BOOL LynxError::isLogBoxOnly isLogBoxOnly;
  public NSInteger LynxError::errorCode errorCode;
  public NSString* LynxError::summaryMessage summaryMessage;
  public NSString* LynxError::templateUrl templateUrl;
  public NSString* LynxError::cardVersion cardVersion;
  public NSString* LynxError::level level;
  public NSString* LynxError::fixSuggestion fixSuggestion;
  public NSString* LynxError::callStack callStack;
  public NSString* LynxError::rootCause rootCause;
  public NSMutableDictionary* LynxError::customInfo customInfo;
  public instancetype LynxError::lynxErrorWithCode:message:(NSInteger code,[message] NSString *errorMsg);
  public instancetype LynxError::lynxErrorWithCode:message:fixSuggestion:level:(NSInteger code,[message] NSString *errorMsg,[fixSuggestion] NSString *suggestion,[level] NSString *level);
  public instancetype LynxError::lynxErrorWithCode:message:fixSuggestion:level:customInfo:(NSInteger code,[message] NSString *errorMsg,[fixSuggestion] NSString *suggestion,[level] NSString *level,[customInfo] NSDictionary *_Nullable customInfo);
  public instancetype LynxError::lynxErrorWithCode:message:fixSuggestion:level:customInfo:isLogBoxOnly:(NSInteger code,[message] NSString *errorMsg,[fixSuggestion] NSString *suggestion,[level] NSString *level,[customInfo] NSDictionary *_Nullable customInfo,[isLogBoxOnly] BOOL isLogBoxOnly);
  public instancetype LynxError::lynxErrorWithCode:userInfo:(NSInteger code,[userInfo] NSDictionary *userInfo);
  public instancetype LynxError::lynxErrorWithCode:description:(NSInteger code,[description] nonnull NSString *message);
  public instancetype LynxError::lynxErrorWithCode:sourceError:(NSInteger code,[sourceError](deprecated("Use lynxErrorWithCode:message:fixSuggestion:level")) __attribute__);
  public instancetype LynxError::errorWithDomain:code:userInfo:(NSErrorDomain domain,[code] NSInteger code,[userInfo](unavailable("Use lynxErrorWithCode:message:fixSuggestion:level")) __attribute__);
  public NSInteger LynxError::getSubCode();
  public BOOL LynxError::isValid();
  public BOOL LynxError::isJSError();
  public BOOL LynxError::isLepusError();
  public void LynxError::addCustomInfo:forKey:(NSString *value,[forKey] NSString *key);
  public void LynxError::setCustomInfo:(NSDictionary *customInfo);
  public NSDictionary * LynxError::getContextInfo();
  public (unavailable("Use lynxErrorWithCode:message:fixSuggestion:level")) LynxError::__attribute__();
  public (unavailable("Use lynxErrorWithCode:message:fixSuggestion:level")) LynxError::__attribute__();
  public instancetype LynxError::initWithDomain:code:userInfo:(NSErrorDomain domain,[code] NSInteger code,[userInfo](unavailable("Use lynxErrorWithCode:message:fixSuggestion:level")) __attribute__);
}

public protocol LynxErrorReceiverProtocol-p : <NSObject> {
  public void LynxErrorReceiverProtocol-p::onErrorOccurred:(LynxError *error);
}

public class LynxEvent : NSObject {
  public NSInteger LynxEvent::targetSign targetSign;
  public NSInteger LynxEvent::currentTargetSign currentTargetSign;
  public id<LynxEventTargetBase> LynxEvent::eventTarget eventTarget;
  public NSString* LynxEvent::eventName eventName;
  public LynxEventType LynxEvent::eventType eventType;
  public NSTimeInterval LynxEvent::timestamp timestamp;
  public int64_t LynxEvent::eventID eventID;
  public instancetype LynxEvent::initWithName:type:(NSString *name,[type] LynxEventType type);
  public instancetype LynxEvent::initWithName:type:targetSign:(NSString *name,[type] LynxEventType type,[targetSign] NSInteger target);
  public instancetype LynxEvent::initWithName:type:targetSign:currentTargetSign:(NSString *name,[type] LynxEventType type,[targetSign] NSInteger target,[currentTargetSign] NSInteger currentTarget);
  public BOOL LynxEvent::canCoalesce();
  public NSMutableDictionary * LynxEvent::generateEventBody();
  public NSMutableArray * LynxEvent::getEventParams();
}

public class LynxEventDetail : NSObject {
  public LynxEvent* LynxEventDetail::event event;
  public id<LynxEventTargetBase> LynxEventDetail::eventTarget eventTarget;
  public LynxView* LynxEventDetail::lynxView lynxView;
  public UIEvent* LynxEventDetail::uiEvent uiEvent;
  public NSSet<UITouch*>* LynxEventDetail::touches touches;
  public nullable instancetype LynxEventDetail::initWithEvent:target:lynxView:(nonnull LynxEvent *event,[target] nullable id< LynxEventTargetBase > target,[lynxView] nullable LynxView *lynxView);
  public nullable NSString * LynxEventDetail::eventName();
  public enum EVENT_TYPE LynxEventDetail::eventType();
  public CGPoint LynxEventDetail::targetPoint();
  public BOOL LynxEventDetail::isMultiTouch();
  public nullable NSDictionary * LynxEventDetail::targetPointMap();
  public nullable NSDictionary * LynxEventDetail::params();
}

public class LynxEventEmitter : NSObject {
  public instancetype LynxEventEmitter::initWithLynxEngineProxy:(LynxEngineProxy *engineProxy);
  public void LynxEventEmitter::setEventReporterBlock:(onLynxEvent eventReporter);
  public void LynxEventEmitter::setIntersectionObserverBlock:(dispatch_block_t intersectionObserver);
  public BOOL LynxEventEmitter::dispatchTouchEvent:(LynxTouchEvent *event);
  public void LynxEventEmitter::dispatchMultiTouchEvent:(LynxTouchEvent *event);
  public void LynxEventEmitter::dispatchCustomEvent:(LynxCustomEvent *event);
  public void LynxEventEmitter::sendCustomEvent:(LynxCustomEvent *event);
  public BOOL LynxEventEmitter::onLynxEvent:(LynxEvent *detail);
  public void LynxEventEmitter::dispatchGestureEvent:event:(int gestureId,[event] LynxCustomEvent *event);
  public void LynxEventEmitter::onPseudoStatusChanged:fromPreStatus:toCurrentStatus:(int32_t tag,[fromPreStatus] int32_t preStatus,[toCurrentStatus] int32_t currentStatus);
  public void LynxEventEmitter::dispatchLayoutEvent();
  public void LynxEventEmitter::addObserver:(id< LynxEventObserver > observer);
  public void LynxEventEmitter::removeObserver:(id< LynxEventObserver > observer);
  public void LynxEventEmitter::notifyIntersectionObserver();
  public void LynxEventEmitter::startEventGenerate:(LynxEvent *event);
  public void LynxEventEmitter::setEventID:(int64_t eventID);
  public void LynxEventEmitter::startEventCapture:(int64_t eventID);
  public void LynxEventEmitter::startEventBubble:(int64_t eventID);
  public void LynxEventEmitter::startEventFire:withEventID:(BOOL isStop,[withEventID] int64_t eventID);
}

public class LynxEventEmitterUnitTestHelper : LynxEventEmitter {
  public LynxCustomEvent* LynxEventEmitterUnitTestHelper::event event;
  public NSMutableArray<LynxCustomEvent *>* LynxEventEmitterUnitTestHelper::cachedEvents cachedEvents;
  public void LynxEventEmitterUnitTestHelper::clearEvent();
}

public class LynxEventHandler : NSObject {
  public UIView* LynxEventHandler::rootView rootView;
  public LynxEventEmitter* LynxEventHandler::eventEmitter eventEmitter;
  public LynxTouchHandler* LynxEventHandler::touchRecognizer touchRecognizer;
  public UIGestureRecognizer* LynxEventHandler::tapRecognizer tapRecognizer;
  public UIGestureRecognizer* LynxEventHandler::longPressRecognizer longPressRecognizer;
  public LynxGestureArenaManager* _Nullable LynxEventHandler::gestureArenaManager gestureArenaManager;
  public BOOL LynxEventHandler::enableSimultaneousTap enableSimultaneousTap;
  public instancetype LynxEventHandler::initWithRootView:(UIView *rootView);
  public instancetype LynxEventHandler::initWithRootView:withRootUI:(UIView *rootView,[withRootUI] nullable LynxUI *rootUI);
  public void LynxEventHandler::attachLynxView:(UIView *rootView);
  public void LynxEventHandler::updateUiOwner:eventEmitter:(nullable LynxUIOwner *owner,[eventEmitter] LynxEventEmitter *eventEmitter);
  public id< LynxEventTarget > LynxEventHandler::hitTest:withEvent:(CGPoint point,[withEvent] nullable UIEvent *event);
  public void LynxEventHandler::onGestureRecognized();
  public void LynxEventHandler::onGestureRecognizedByEventTarget:(id< LynxEventTarget > ui);
  public void LynxEventHandler::onPropsChangedByEventTarget:(id< LynxEventTarget > ui);
  public void LynxEventHandler::resetEventEnv();
  public NSInteger LynxEventHandler::canRespondTapOrClickEvent:(id< LynxEventTarget > ui);
  public NSInteger LynxEventHandler::canRespondTapOrClickWhenUISlideByProps:(id< LynxEventTarget > ui);
  public void LynxEventHandler::dispatchTapEvent:(UITapGestureRecognizer *sender);
  public void LynxEventHandler::dispatchLongPressEvent:(UILongPressGestureRecognizer *sender);
  public NSInteger LynxEventHandler::setGestureArenaManagerAndGetIndex:(nullable LynxGestureArenaManager *manager);
  public void LynxEventHandler::removeGestureArenaManager:(NSInteger index);
  public id< LynxEventTarget > LynxEventHandler::touchTarget();
  public LynxUIOwner * LynxEventHandler::uiOwner();
  public void LynxEventHandler()::setEnableViewReceiveTouch:(BOOL enable);
  public void LynxEventHandler()::setEnableSimultaneousTap:(BOOL enable);
  public void LynxEventHandler()::setDisableLongpressAfterScroll:(bool value);
  public void LynxEventHandler()::setTapSlop:(NSString *tapSlop);
  public void LynxEventHandler()::setLongPressDuration:(int32_t value);
  public void LynxEventHandler()::dispatchPanEvent:(UIPanGestureRecognizer *sender);
  public void LynxEventHandler()::needCheckConsumeSlideEvent();
  public void LynxEventHandler()::handleFocus:onView:withContainer:andPoint:andEvent:(id< LynxEventTarget > target, [onView] UIView *view, [withContainer] UIView *container, [andPoint] CGPoint point, [andEvent] UIEvent *event);
  public id< LynxEventTarget > LynxEventHandler()::hitTestInner:withEvent:(CGPoint point, [withEvent] nullable UIEvent *event);
  public NSInteger LynxEventHandler()::checkCanRespondTapOrClick:withSet:(id< LynxEventTarget > ui, [withSet] NSSet *set);
  public void LynxEventHandler()::removeEventGestures();
}

public protocol LynxEventObserver-p :  {
  public void LynxEventObserver-p::onLynxEvent:event:(LynxInnerEventType type,[event] LynxEvent *event);
}

public class LynxEventReporter : NSObject {
  public void LynxEventReporter::onEvent:instanceId:props:(nonnull NSString *eventName,[instanceId] int32_t instanceId,[props] nullable NSDictionary< NSString *, NSObject * > *props);
  public void LynxEventReporter::onEvent:instanceId:propsBuilder:(nonnull NSString *eventName,[instanceId] int32_t instanceId,[propsBuilder] NSDictionary< NSString *, NSObject * > *(^ propsBuilder)(void));
  public void LynxEventReporter::updateGenericInfo:key:instanceId:(nonnull NSObject *value,[key] nonnull NSString *key,[instanceId] int32_t instanceId);
  public void LynxEventReporter::removeGenericInfo:(int32_t instanceId);
  public void LynxEventReporter::putExtraParams:forInstanceId:(NSDictionary< NSString *, NSObject * > *params,[forInstanceId] int32_t instanceId);
  public void LynxEventReporter::moveExtraParams:toInstanceId:(int32_t originInstanceId,[toInstanceId] int32_t targetInstanceId);
  public void LynxEventReporter::clearCacheForInstanceId:(int32_t instanceId);
  public void LynxEventReporter::addEventReportObserver:(id< LynxEventReportObserverProtocol > observer);
  public void LynxEventReporter::removeEventReportObserver:(id< LynxEventReportObserverProtocol > observer);
}

public class LynxEventReporterUtils : NSObject {
  public NSString * LynxEventReporterUtils::relativePathForURL:(NSString *urlStr);
}

public protocol LynxEventReportObserverProtocol-p : <NSObject> {
  public void LynxEventReportObserverProtocol-p::onReportEvent:instanceId:props:extraData:(NSString *eventName,[instanceId] NSInteger instanceId,[props] NSDictionary *_Nullable props,[extraData] NSDictionary *_Nullable extraData);
}

public class LynxEventSpec : NSObject {
  public NSString* LynxEventSpec::name name;
  public BOOL LynxEventSpec::shouldCaptureBubble shouldCaptureBubble;
  public BOOL LynxEventSpec::shouldCaptureCatch shouldCaptureCatch;
  public BOOL LynxEventSpec::interestedInBubble interestedInBubble;
  public BOOL LynxEventSpec::interestedInCatch interestedInCatch;
  public BOOL LynxEventSpec::shouldLepusCaptureBubble shouldLepusCaptureBubble;
  public BOOL LynxEventSpec::shouldLepusCaptureCatch shouldLepusCaptureCatch;
  public BOOL LynxEventSpec::interestedInLepusBubble interestedInLepusBubble;
  public BOOL LynxEventSpec::interestedInLepusCatch interestedInLepusCatch;
  public instancetype LynxEventSpec::initWithRawEvent:withJSEvent:(NSString *event,[withJSEvent] BOOL isJSEvent);
  public void LynxEventSpec::composeWithOtherSpec:(LynxEventSpec *spec);
  public nullable NSDictionary< NSString *, LynxEventSpec * > * LynxEventSpec::convertRawEvents:andRwaLepusEvents:(nullable NSSet< NSString * > *set,[andRwaLepusEvents] nullable NSSet< NSString * > *lepusSet);
}

public protocol LynxEventTarget-p : <LynxEventTargetBase> {
  public NSInteger LynxEventTarget-p::signature();
  public int32_t LynxEventTarget-p::pseudoStatus();
  public nullable id< LynxEventTarget > LynxEventTarget-p::parentTarget();
  public id< LynxEventTarget > LynxEventTarget-p::hitTest:withEvent:(CGPoint point,[withEvent] nullable UIEvent *event);
  public BOOL LynxEventTarget-p::containsPoint:(CGPoint point);
  public nullable NSDictionary< NSString *, LynxEventSpec * > * LynxEventTarget-p::eventSet();
  public nullable NSDictionary< NSNumber *, LynxGestureDetectorDarwin * > * LynxEventTarget-p::gestureMap();
  public BOOL LynxEventTarget-p::shouldHitTest:withEvent:(CGPoint point,[withEvent] nullable UIEvent *event);
  public BOOL LynxEventTarget-p::ignoreFocus();
  public BOOL LynxEventTarget-p::consumeSlideEvent:(CGFloat angle);
  public BOOL LynxEventTarget-p::blockNativeEvent:(UIGestureRecognizer *gestureRecognizer);
  public BOOL LynxEventTarget-p::eventThrough();
  public BOOL LynxEventTarget-p::enableTouchPseudoPropagation();
  public void LynxEventTarget-p::onPseudoStatusFrom:changedTo:(int32_t preStatus,[changedTo] int32_t currentStatus);
  public BOOL LynxEventTarget-p::dispatchTouch:touches:withEvent:(NSString *const touchType,[touches] NSSet< UITouch * > *touches,[withEvent] UIEvent *event);
  public BOOL LynxEventTarget-p::dispatchEvent:(LynxEventDetail *event);
  public void LynxEventTarget-p::onResponseChain();
  public void LynxEventTarget-p::offResponseChain();
  public BOOL LynxEventTarget-p::isOnResponseChain();
  public NSInteger LynxEventTarget-p::getGestureArenaMemberId();
  public id< LynxEventTarget > LynxEventTarget-p::parentLynxPageUI();
  public void LynxEventTarget-p::setParentLynxPageUI:(id< LynxEventTarget > ui);
  public NSMutableDictionary * LynxEventTarget-p::childrenLynxPageUI();
  public void LynxEventTarget-p::setChildrenLynxPageUI:(NSMutableDictionary *dict);
  public id< LynxEventTarget > LynxEventTarget-p::rootLynxPageUI();
  public void LynxEventTarget-p::setEventID:(int64_t eventID);
  public void LynxEventTarget-p::startEventCapture:(int64_t eventID);
  public void LynxEventTarget-p::onEventCapture:withEventID:(BOOL isCatch,[withEventID] int64_t eventID);
  public void LynxEventTarget-p::startEventBubble:(int64_t eventID);
  public void LynxEventTarget-p::onEventBubble:withEventID:(BOOL isCatch,[withEventID] int64_t eventID);
  public void LynxEventTarget-p::startEventFire:withEventID:(BOOL isStop,[withEventID] int64_t eventID);
  public void LynxEventTarget-p::onEventFire:withEventID:(BOOL isStop,[withEventID] int64_t eventID);
}

public protocol LynxEventTargetBase-p : <NSObject> {
  public nullable id< LynxEventTargetBase > LynxEventTargetBase-p::parentResponder();
  public nullable NSDictionary * LynxEventTargetBase-p::getDataset();
}

public class LynxEventTargetSpan : NSObject, <LynxEventTarget> {
  public instancetype LynxEventTargetSpan::initWithShadowNode:frame:(LynxShadowNode *node,[frame] CGRect frame);
  public void LynxEventTargetSpan::setParentEventTarget:(id< LynxEventTarget > parent);
}

public class LynxExposureModule : NSObject, <LynxContextModule> {
  typedef void(^ LynxExposureModule()::LynxExposureBlock) (LynxExposureModule *)
  public LynxUIExposure * LynxExposureModule()::exposure();
  public void LynxExposureModule()::stopExposure:(NSDictionary *options);
  public void LynxExposureModule()::resumeExposure();
  public void LynxExposureModule()::runOnUIThreadSafely:(LynxExposureBlock block);
}

public protocol LynxExtensionModule-p : <NSObject> {
  public NSString * LynxExtensionModule-p::name();
  public instancetype LynxExtensionModule-p::initWithLynxContext:group:(LynxContext *context,[group] LynxGroup *group);
  public void * LynxExtensionModule-p::getExtensionDelegate();
  public void LynxExtensionModule-p::setUp();
}

public class LynxExternalResourceFetcherWrapper : NSObject {
  public instancetype LynxExternalResourceFetcherWrapper::initWithDynamicComponentFetcher:(id< LynxDynamicComponentFetcher > fetcher);
  public void LynxExternalResourceFetcherWrapper::fetchResource:withLoadedBlock:(NSString *url,[withLoadedBlock] LoadedBlock block);
}

public class LynxExtraTiming : NSObject {
  public uint64_t LynxExtraTiming::openTime openTime;
  public uint64_t LynxExtraTiming::containerInitStart containerInitStart;
  public uint64_t LynxExtraTiming::containerInitEnd containerInitEnd;
  public uint64_t LynxExtraTiming::prepareTemplateStart prepareTemplateStart;
  public uint64_t LynxExtraTiming::prepareTemplateEnd prepareTemplateEnd;
  public NSDictionary * LynxExtraTiming::toDictionary();
}

public class LynxFatImageProcessor : NSObject, <LynxImageProcessor> {
  public instancetype LynxFatImageProcessor::initWithSize:padding:border:contentMode:(CGSize size,[padding] UIEdgeInsets padding,[border] UIEdgeInsets border,[contentMode] UIViewContentMode contentMode);
}

public class LynxFeatureCounter : NSObject {
  public void LynxFeatureCounter::count:instanceId:(LynxFeature feature,[instanceId] int32_t instanceId);
}

public class LynxFilterUtil : NSObject {
  public id LynxFilterUtil::getFilterWithType:filterAmount:(LynxFilterType type,[filterAmount] CGFloat filter_amount);
}

public class LynxFluencyMonitor : NSObject {
  public BOOL LynxFluencyMonitor::shouldSendAllScrollEvent shouldSendAllScrollEvent;
  public void LynxFluencyMonitor::startWithScrollInfo:(LynxScrollInfo *info);
  public void LynxFluencyMonitor::stopWithScrollInfo:(LynxScrollInfo *info);
  public void LynxFluencyMonitor::setEnabledBySampling:(LynxBooleanOption enabledBySampling);
  public void LynxFluencyMonitor::setPageConfigProbability:(CGFloat probability);
}

public class LynxFontFace : NSObject {
  public instancetype LynxFontFace::initWithFamilyName:andSrc:withLynxContext:(NSString *familyName,[andSrc] NSString *src,[withLynxContext] LynxContext *context);
  public NSUInteger LynxFontFace::srcCount();
  public LynxFontSrcItem * LynxFontFace::srcAtIndex:(NSUInteger index);
}

public class LynxFontFaceContext : NSObject {
  public id<LynxResourceFetcher> LynxFontFaceContext::resourceFetcher resourceFetcher;
  public id<LynxResourceProvider> LynxFontFaceContext::resourceProvider resourceProvider;
  public id<LynxGenericResourceFetcher> LynxFontFaceContext::genericResourceServiceFetcher genericResourceServiceFetcher;
  public UIView<LUIBodyView>* LynxFontFaceContext::rootView rootView;
  public NSDictionary* LynxFontFaceContext::builderRegistedAliasFontMap builderRegistedAliasFontMap;
  public void LynxFontFaceContext::addFontFace:(LynxFontFace *fontFace);
  public nullable LynxFontFace * LynxFontFaceContext::getFontFaceWithFamilyName:(NSString *familyName);
}

public class LynxFontFaceManager : NSObject {
  public LynxFontFaceManager * LynxFontFaceManager::sharedManager();
  public UIFont * LynxFontFaceManager::generateFontWithSize:weight:style:fontFamilyName:fontFaceContext:fontFaceObserver:(CGFloat fontSize,[weight] CGFloat fontWeight,[style] LynxFontStyleType fontStyle,[fontFamilyName] NSString *fontFamilyName,[fontFaceContext] LynxFontFaceContext *fontFaceContext,[fontFaceObserver] id< LynxFontFaceObserver > observer);
  public void LynxFontFaceManager::registerFont:forName:(UIFont *font,[forName] NSString *name);
  public void LynxFontFaceManager::registerFamilyName:withAliasName:(NSString *fontFamilyName,[withAliasName] NSString *aliasName);
  public UIFont * LynxFontFaceManager::getRegisteredUIFont:fontSize:(NSString *familyName,[fontSize] CGFloat fontSize);
}

public protocol LynxFontFaceObserver-p : <NSObjectNSObject> {
  public void LynxFontFaceObserver-p::onFontFaceLoad();
}

public class LynxFontSrcItem : NSObject {
  public LynxFontSrcType LynxFontSrcItem::type type;
  public NSString* LynxFontSrcItem::src src;
  public NSString* LynxFontSrcItem::dataFontName dataFontName;
  public NSPointerArray* LynxFontSrcItem::notifierArray notifierArray;
}

public protocol LynxForegroundProtocol-p : <NSObject> {
  public void LynxForegroundProtocol-p::onEnterForeground();
  public void LynxForegroundProtocol-p::onEnterBackground();
}

public struct LynxFPSDerivedMetrics {
  public double LynxFPSDerivedMetrics::fps fps;
  public double LynxFPSDerivedMetrics::drop1PerSecond drop1PerSecond;
  public double LynxFPSDerivedMetrics::drop3PerSecond drop3PerSecond;
  public double LynxFPSDerivedMetrics::drop7PerSecond drop7PerSecond;
  public double LynxFPSDerivedMetrics::drop25PerSecond drop25PerSecond;
  public double LynxFPSDerivedMetrics::hitchRatio hitchRatio;
  public double LynxFPSDerivedMetrics::drop1Ratio drop1Ratio;
  public double LynxFPSDerivedMetrics::drop3Ratio drop3Ratio;
  public double LynxFPSDerivedMetrics::drop7Ratio drop7Ratio;
  public double LynxFPSDerivedMetrics::drop25Ratio drop25Ratio;
}

public class LynxFPSMonitor : NSObject {
  public BOOL LynxFPSMonitor::supportsDynamicFrameRate supportsDynamicFrameRate;
  public BOOL LynxFPSMonitor::active active;
  public instancetype LynxFPSMonitor::sharedInstance();
  public LynxFPSRecord * LynxFPSMonitor::beginWithKey:(id< NSCopying > key);
  public nullable LynxFPSRecord * LynxFPSMonitor::pauseWithKey:(id< NSCopying > key);
  public nullable LynxFPSRecord * LynxFPSMonitor::endWithKey:(id< NSCopying > NS_WARN_UNUSED_RESULT);
  public nullable LynxFPSRecord * LynxFPSMonitor::recordWithKey:(id< NSCopying > NS_WARN_UNUSED_RESULT);
}

public struct LynxFPSRawMetrics {
  public UInt32 LynxFPSRawMetrics::frames frames;
  public NSTimeInterval LynxFPSRawMetrics::duration duration;
  public UInt32 LynxFPSRawMetrics::drop1Count drop1Count;
  public NSTimeInterval LynxFPSRawMetrics::drop1Duration drop1Duration;
  public UInt32 LynxFPSRawMetrics::drop3Count drop3Count;
  public NSTimeInterval LynxFPSRawMetrics::drop3Duration drop3Duration;
  public UInt32 LynxFPSRawMetrics::drop7Count drop7Count;
  public NSTimeInterval LynxFPSRawMetrics::drop7Duration drop7Duration;
  public UInt32 LynxFPSRawMetrics::drop25Count drop25Count;
  public NSTimeInterval LynxFPSRawMetrics::drop25Duration drop25Duration;
  public NSTimeInterval LynxFPSRawMetrics::hitchDuration hitchDuration;
}

public class LynxFPSRecord : NSObject, <NSCopying> {
  public id<NSCopying> LynxFPSRecord::key key;
  public NSString* LynxFPSRecord::name name;
  public NSUInteger LynxFPSRecord::frames frames;
  public NSTimeInterval LynxFPSRecord::duration duration;
  public double LynxFPSRecord::framesPerSecond framesPerSecond;
  public NSInteger LynxFPSRecord::maximumFramesPerSecond maximumFramesPerSecond;
  public LynxFPSRawMetrics LynxFPSRecord::metrics metrics;
  public LynxFPSDerivedMetrics LynxFPSRecord::derivedMetrics derivedMetrics;
  public LynxFPSRecordState LynxFPSRecord()::state state;
  public instancetype LynxFPSRecord::initWithKey:(id< NSCopying > key);
  public void LynxFPSRecord::setTimeout:completion:(NSTimeInterval timeout,[completion] void(^_Nullable completion)(LynxFPSRecord *));
  public void LynxFPSRecord::reset();
}

public protocol LynxGenericResourceFetcher-p : <NSObject> {
  public dispatch_block_t LynxGenericResourceFetcher-p::fetchResource:onComplete:(nonnull LynxResourceRequest *request,[onComplete] LynxGenericResourceCompletionBlock _Nonnull callback);
  public dispatch_block_t LynxGenericResourceFetcher-p::fetchResourcePath:onComplete:(nonnull LynxResourceRequest *request,[onComplete] LynxGenericResourcePathCompletionBlock _Nonnull callback);
  public dispatch_block_t LynxGenericResourceFetcher-p::fetchStream:withStream:(nonnull LynxResourceRequest *request,[withStream] nonnull id< LynxResourceStreamLoadDelegate > delegate);
}

public class LynxGestureArenaManager : NSObject {
  public LynxGestureHandlerTrigger* LynxGestureArenaManager::gestureHandlerTrigger gestureHandlerTrigger;
  public NSArray< id< LynxGestureArenaMember > > * LynxGestureArenaManager::getCompetitionChainCandidates();
  public void LynxGestureArenaManager::dispatchTouchToArena:touches:event:touchEvent:(NSString *const touchType,[touches] NSSet< UITouch * > *touches,[event] UIEvent *event,[touchEvent] LynxTouchEvent *_Nullable touchEvent);
  public void LynxGestureArenaManager::dispatchBubble:touchEvent:(NSString *const touchType,[touchEvent] LynxTouchEvent *_Nullable touchEvent);
  public void LynxGestureArenaManager::setActiveUIToArena:(id< LynxEventTarget > target);
  public NSInteger LynxGestureArenaManager::addMember:(id< LynxGestureArenaMember > member);
  public BOOL LynxGestureArenaManager::isMemberExist:(NSInteger memberId);
  public id< LynxGestureArenaMember > LynxGestureArenaManager::getMemberById:(NSInteger memberId);
  public void LynxGestureArenaManager::removeMember:detectorMap:(id< LynxGestureArenaMember > member,[detectorMap] NSDictionary< NSNumber *, LynxGestureDetectorDarwin * > *detectorMap);
  public void LynxGestureArenaManager::registerGestureDetectors:detectorMap:(NSInteger memberId,[detectorMap] NSDictionary< NSNumber *, LynxGestureDetectorDarwin * > *gestureDetectors);
  public void LynxGestureArenaManager::unregisterGestureDetectors:detectorMap:(NSInteger memberId,[detectorMap] NSDictionary< NSNumber *, LynxGestureDetectorDarwin * > *gestureDetectors);
  public void LynxGestureArenaManager::setGestureDetectorState:memberId:state:(NSInteger gestureId,[memberId] NSInteger memberId,[state] LynxGestureState state);
}

public protocol LynxGestureArenaMember-p : <LynxEventTarget> {
  public void LynxGestureArenaMember-p::onGestureScrollBy:(CGPoint delta);
  public BOOL LynxGestureArenaMember-p::canConsumeGesture:(CGPoint delta);
  public BOOL LynxGestureArenaMember-p::getGestureBorder:(BOOL start);
  public NSInteger LynxGestureArenaMember-p::getGestureArenaMemberId();
  public CGFloat LynxGestureArenaMember-p::getMemberScrollX();
  public CGFloat LynxGestureArenaMember-p::getMemberScrollY();
  public NSDictionary< NSNumber *, LynxGestureDetectorDarwin * > * LynxGestureArenaMember-p::getGestureDetectorMap();
  public NSDictionary< NSNumber *, LynxBaseGestureHandler * > * LynxGestureArenaMember-p::getGestureHandlers();
}

public class LynxGestureConsumer : NSObject {
  public BOOL LynxGestureConsumer::adjustingScrollOffset adjustingScrollOffset;
  public LynxGestureConsumeStatus LynxGestureConsumer::gestureConsumeStatus gestureConsumeStatus;
  public CGPoint LynxGestureConsumer::previousScrollOffset previousScrollOffset;
  public void LynxGestureConsumer::consumeGesture:(BOOL consume);
}

public class LynxGestureDetectorDarwin : NSObject {
  public uint32_t LynxGestureDetectorDarwin::gestureID gestureID;
  public LynxGestureTypeDarwin LynxGestureDetectorDarwin::gestureType gestureType;
  public NSArray<NSString *>* LynxGestureDetectorDarwin::gestureCallbackNames gestureCallbackNames;
  public NSDictionary<NSString *, NSArray<NSNumber *> *>* LynxGestureDetectorDarwin::relationMap relationMap;
  public NSMutableDictionary* LynxGestureDetectorDarwin::configMap configMap;
  public instancetype LynxGestureDetectorDarwin::initWithGestureID:gestureType:gestureCallbackNames:relationMap:(uint32_t gestureID,[gestureType] LynxGestureTypeDarwin gestureType,[gestureCallbackNames] NSArray< NSString * > *gestureCallbackNames,[relationMap] NSDictionary< NSString *, NSArray< NSNumber * > * > *relationMap);
  public instancetype LynxGestureDetectorDarwin::initWithGestureID:gestureType:gestureCallbackNames:relationMap:configMap:(uint32_t gestureID,[gestureType] LynxGestureTypeDarwin gestureType,[gestureCallbackNames] NSArray< NSString * > *gestureCallbackNames,[relationMap] NSDictionary< NSString *, NSArray< NSNumber * > * > *relationMap,[configMap] NSMutableDictionary *configMap);
}

public class LynxGestureDetectorManager : NSObject {
  public instancetype LynxGestureDetectorManager::initWithArenaManager:(LynxGestureArenaManager *manager);
  public NSMutableArray< id< LynxGestureArenaMember > > * LynxGestureDetectorManager::convertResponseChainToCompeteChain:(NSArray< id< LynxGestureArenaMember > > *responseList);
  public nullable NSDictionary< NSString *, NSSet * > * LynxGestureDetectorManager::handleSimultaneousWinner:(id< LynxGestureArenaMember > current);
  public void LynxGestureDetectorManager::registerGestureDetector:detector:(NSInteger memberId,[detector] LynxGestureDetectorDarwin *gestureDetector);
  public void LynxGestureDetectorManager::unregisterGestureDetector:detector:(NSInteger memberId,[detector] LynxGestureDetectorDarwin *gestureDetector);
}

public class LynxGestureFlingTrigger : NSObject {
  public LynxGestureFlingTriggerState LynxGestureFlingTrigger::state state;
  public CGPoint LynxGestureFlingTrigger::velocity velocity;
  public CGPoint LynxGestureFlingTrigger::distance distance;
  public CGPoint LynxGestureFlingTrigger::lastDistance lastDistance;
  public instancetype LynxGestureFlingTrigger::initWithTarget:action:(nullable id target,[action] nullable SEL action);
  public BOOL LynxGestureFlingTrigger::startWithVelocity:(CGPoint velocity);
  public void LynxGestureFlingTrigger::stop();
  public void LynxGestureFlingTrigger::reset();
  public BOOL LynxGestureFlingTrigger::isFinished();
}

public class LynxGestureHandlerTrigger : NSObject {
  public instancetype LynxGestureHandlerTrigger::initWithDetectorManager:arenaManager:(LynxGestureDetectorManager *detectorManager,[arenaManager] LynxGestureArenaManager *arenaManager);
  public void LynxGestureHandlerTrigger::setCurrentWinnerWhenDown:(id< LynxGestureArenaMember > winner);
  public void LynxGestureHandlerTrigger::resolveTouchEvent:touches:event:touchEvent:completionChainCandidates:bubbleCandidates:(NSString *const touchType,[touches] NSSet< UITouch * > *touches,[event] UIEvent *event,[touchEvent] LynxTouchEvent *touchEvent,[completionChainCandidates] NSArray< id< LynxGestureArenaMember > > *completionChainCandidate,[bubbleCandidates] NSArray< id< LynxGestureArenaMember > > *bubbleCandidates);
  public void LynxGestureHandlerTrigger::dispatchBubble:touchEvent:bubbleCandidate:winner:(NSString *const touchType,[touchEvent] LynxTouchEvent *touchEvent,[bubbleCandidate] NSArray< id< LynxGestureArenaMember > > *bubbleCandidate,[winner] id< LynxGestureArenaMember > winner);
  public void LynxGestureHandlerTrigger::handleGestureDetectorState:gestureId:state:(id< LynxGestureArenaMember > member,[gestureId] NSInteger gestureId,[state] LynxGestureState state);
  public NSInteger LynxGestureHandlerTrigger::addVelocityTracker:(LynxGestureVelocityTracker *velocityTracker);
  public NSInteger LynxGestureHandlerTrigger::addEventHandler:(LynxEventHandler *eventHandler);
  public void LynxGestureHandlerTrigger::removeVelocityTracker:(NSInteger index);
  public void LynxGestureHandlerTrigger::removeEventHandler:(NSInteger index);
}

public class LynxGestureVelocityTracker : NSObject {
  public UIPanGestureRecognizer* LynxGestureVelocityTracker::tracker tracker;
  public instancetype LynxGestureVelocityTracker::initWithRootView:(UIView *rootView);
  public CGPoint LynxGestureVelocityTracker::velocityInView:(nullable UIView *view);
}

public class LynxGlobalObserver : NSObject {
  public void LynxGlobalObserver::notifyAnimationStart();
  public void LynxGlobalObserver::notifyAnimationEnd();
  public void LynxGlobalObserver::notifyLayout:(NSDictionary *options);
  public void LynxGlobalObserver::notifyScroll:(NSDictionary *options);
  public void LynxGlobalObserver::notifyProperty:(NSDictionary *options);
  public void LynxGlobalObserver()::setObserverFrameRate:(int32_t rate);
  public void LynxGlobalObserver()::addAnimationObserver:(callback callback);
  public void LynxGlobalObserver()::removeAnimationObserver:(callback callback);
  public void LynxGlobalObserver()::addLayoutObserver:(callback callback);
  public void LynxGlobalObserver()::removeLayoutObserver:(callback callback);
  public void LynxGlobalObserver()::addScrollObserver:(callback callback);
  public void LynxGlobalObserver()::removeScrollObserver:(callback callback);
  public void LynxGlobalObserver()::addPropertyObserver:(callback callback);
  public void LynxGlobalObserver()::removePropertyObserver:(callback callback);
}

public class LynxGradient : NSObject {
  public NSMutableArray* LynxGradient::colors colors;
  public CGFloat* LynxGradient::positions positions;
  public NSUInteger LynxGradient::positionCount positionCount;
  public instancetype LynxGradient::initWithColors:stops:(NSArray< NSNumber * > *colors,[stops] NSArray< NSNumber * > *stops);
  public void LynxGradient::draw:withPath:(CGContextRef context,[withPath] CGPathRef path);
  public void LynxGradient::draw:withRect:(CGContextRef context,[withRect] CGRect pathRect);
  public BOOL LynxGradient::isEqualTo:(LynxGradient *rhs);
}

public class LynxGradientUtils : NSObject {
  public CGPoint LynxGradientUtils::getRadialRadiusWithShape:shapeSize:centerX:centerY:sizeX:sizeY:(LynxRadialGradientShapeType shape,[shapeSize] LynxRadialGradientSizeType shapeSize,[centerX] CGFloat cx,[centerY] CGFloat cy,[sizeX] CGFloat sx,[sizeY] CGFloat sy);
  public NSArray *_Nullable LynxGradientUtils::getGradientArrayFromString:withLengthContext:(NSString *_Nonnull gradientDef,[withLengthContext] struct LynxLengthContext context);
}

public class LynxGroup : NSObject {
  public NSString* LynxGroup::groupName groupName;
  public NSString* LynxGroup::identification identification;
  public NSArray* LynxGroup::preloadJSPaths preloadJSPaths;
  public std::shared_ptr<lynx::tasm::WhiteBoard> LynxGroup()::whiteBoard whiteBoard;
  public nonnull NSString * LynxGroup::singleGroupTag();
  public NSString * LynxGroup()::groupNameForLynxGroupOrDefault:(LynxGroup *group);
  public NSString * LynxGroup()::jsThreadNameForLynxGroupOrDefault:(LynxGroup *group);
  public nonnull instancetype LynxGroup::initWithName:(nonnull NSString *name);
  public nonnull instancetype LynxGroup::initWithName:withPreloadScript:(nonnull NSString *name,[withPreloadScript] nullable NSArray *extraJSPaths);
  public nonnull instancetype LynxGroup::initWithName:withLynxGroupOption:(nonnull NSString *name,[withLynxGroupOption] nullable LynxGroupOption *option);
  public void LynxGroup::addLynxView:(nonnull LynxView *view);
  public bool LynxGroup::enableJSGroupThread();
  public nullable NSString * LynxGroup::getStringConfig:(nonnull NSString *key);
  public BOOL LynxGroup::getBoolConfig:(nonnull NSString *key);
}

public class LynxGroupOption : NSObject {
  public NSArray* LynxGroupOption::preloadJSPaths preloadJSPaths;
  public bool LynxGroupOption::enableJSGroupThread enableJSGroupThread;
  public void LynxGroupOption::setStringConfig:forKey:(nonnull NSString *value,[forKey] nonnull NSString *key);
  public void LynxGroupOption::setBoolConfig:forKey:(BOOL value,[forKey] nonnull NSString *key);
}

public class LynxHeroAnimator : NSObject {
  public id<LynxHeroAnimatorDelegate> LynxHeroAnimator::delegate delegate;
  public NSTimeInterval LynxHeroAnimator::timePassed timePassed;
  public NSTimeInterval LynxHeroAnimator::totalTime totalTime;
  public BOOL LynxHeroAnimator::isReversed isReversed;
  public void LynxHeroAnimator::startWithTimePassed:totalTime:isReversed:(NSTimeInterval timePassed,[totalTime] NSTimeInterval totalTime,[isReversed] BOOL isReversed);
  public void LynxHeroAnimator::stop();
}

public protocol LynxHeroAnimatorDelegate-p : <NSObject> {
  public void LynxHeroAnimatorDelegate-p::updateProgress:(double progress);
  public void LynxHeroAnimatorDelegate-p::complete:(BOOL finished);
}

public class LynxHeroModifiers : NSObject {
  public NSTimeInterval LynxHeroModifiers::duration duration;
  public NSTimeInterval LynxHeroModifiers::delay delay;
  public float LynxHeroModifiers::opacity opacity;
  public CATransform3D LynxHeroModifiers::transform transform;
  public CGSize LynxHeroModifiers::size size;
  public CAMediaTimingFunction* LynxHeroModifiers::timingFunction timingFunction;
  public instancetype LynxHeroModifiers::rotateX:y:z:(CGFloat x,[y] CGFloat y,[z] CGFloat z);
  public instancetype LynxHeroModifiers::translateX:y:z:(CGFloat x,[y] CGFloat y,[z] CGFloat z);
  public instancetype LynxHeroModifiers::scaleX:y:z:(CGFloat x,[y] CGFloat y,[z] CGFloat z);
}

public class LynxHeroTransition : NSObject, <UIViewControllerTransitioningDelegate>, <UITabBarControllerDelegate>, <UINavigationControllerDelegate>, <UIViewControllerInteractiveTransitioning>, <UIViewControllerAnimatedTransitioning>, <LynxHeroAnimatorDelegate> {
  public UIViewController* LynxHeroTransition::toViewController toViewController;
  public UIViewController* LynxHeroTransition::fromViewController fromViewController;
  public BOOL LynxHeroTransition::isInNavigationController isInNavigationController;
  public BOOL LynxHeroTransition::isPresenting isPresenting;
  public instancetype LynxHeroTransition::sharedInstance();
  public void LynxHeroTransition::executeEnterTransition:(LynxView *lynxView);
  public void LynxHeroTransition::executeExitTransition:finishCallback:(LynxView *lynxView,[finishCallback] LynxViewAnimFinishCallback callback);
  public void LynxHeroTransition::executePauseTransition:(LynxView *lynxView);
  public void LynxHeroTransition::executeResumeTransition:(LynxView *lynxView);
  public void LynxHeroTransition::registerEnterTransition:anim:(LynxUI *lynxUI,[anim] LynxAnimationInfo *anim);
  public void LynxHeroTransition::registerExitTransition:anim:(LynxUI *lynxUI,[anim] LynxAnimationInfo *anim);
  public void LynxHeroTransition::registerPauseTransition:anim:(LynxUI *lynxUI,[anim] LynxAnimationInfo *anim);
  public void LynxHeroTransition::registerResumeTransition:anim:(LynxUI *lynxUI,[anim] LynxAnimationInfo *anim);
  public void LynxHeroTransition::registerSharedElementsUI:shareTag:(LynxUI *lynxUI,[shareTag] NSString *tag);
  public void LynxHeroTransition::registerSharedElements:shareTag:(UIView *view,[shareTag] NSString *tag);
  public void LynxHeroTransition::unregisterLynxView:(LynxView *lynxView);
}

public class LynxHeroViewConfig : NSObject {
  public NSString* LynxHeroViewConfig::sharedElementName sharedElementName;
  public BOOL LynxHeroViewConfig::crossPage crossPage;
  public LynxHeroModifiers* LynxHeroViewConfig::sharedElementModifiers sharedElementModifiers;
  public LynxAnimationInfo* LynxHeroViewConfig::enterTransitionName enterTransitionName;
  public LynxAnimationInfo* LynxHeroViewConfig::exitTransitionName exitTransitionName;
  public LynxAnimationInfo* LynxHeroViewConfig::pauseTransiitonName pauseTransiitonName;
  public LynxAnimationInfo* LynxHeroViewConfig::resumeTransitionName resumeTransitionName;
  public LynxHeroModifiers* LynxHeroViewConfig::enterTransitionModifiers enterTransitionModifiers;
  public LynxHeroModifiers* LynxHeroViewConfig::exitTransitionModifiers exitTransitionModifiers;
  public BOOL LynxHeroViewConfig::snapshot snapshot;
  public BOOL LynxHeroViewConfig::merge merge;
  public LynxUI* LynxHeroViewConfig::lynxUI lynxUI;
  public UIView* LynxHeroViewConfig::view view;
  public instancetype LynxHeroViewConfig::initWithView:(UIView *view);
}

public class LynxHeroViewControllerConfig : NSObject {
  public id<UINavigationControllerDelegate> LynxHeroViewControllerConfig::previousNavigationDelegate previousNavigationDelegate;
  public id<UITabBarControllerDelegate> LynxHeroViewControllerConfig::previousTabBarDelegate previousTabBarDelegate;
  public BOOL LynxHeroViewControllerConfig::enableHeroTransition enableHeroTransition;
  public UIViewController* LynxHeroViewControllerConfig::vc vc;
  public instancetype LynxHeroViewControllerConfig::initWithVC:(UIViewController *vc);
}

public protocol LynxHolder-p : <NSObject> {
  public LynxView * LynxHolder-p::createLynxView:(LynxRoute *route);
  public void LynxHolder-p::showLynxView:name:(nonnull LynxView *lynxView,[name] nonnull NSString *name);
  public void LynxHolder-p::hideLynxView:(nonnull LynxView *lynxView);
}

public protocol LynxHttpInterceptor-p :  {
  public LynxHttpResponse * LynxHttpInterceptor-p::interceptRequest:(LynxHttpRequest *request);
  public void LynxHttpInterceptor-p::onRequest:(LynxHttpRequest *request);
  public void LynxHttpInterceptor-p::onResponse:withRequest:(LynxHttpResponse *response,[withRequest] LynxHttpRequest *request);
}

public class LynxHttpRequest : NSObject {
  public NSString* LynxHttpRequest::httpMethod httpMethod;
  public NSString* LynxHttpRequest::url url;
  public NSString* LynxHttpRequest::originUrl originUrl;
  public NSData* LynxHttpRequest::httpBody httpBody;
  public NSDictionary<NSString *, NSString *>* LynxHttpRequest::httpHeaders httpHeaders;
  public NSDictionary<NSString *, NSObject *>* LynxHttpRequest::customConfig customConfig;
}

public class LynxHttpResponse : NSObject {
  public NSInteger LynxHttpResponse::statusCode statusCode;
  public NSString* LynxHttpResponse::statusText statusText;
  public NSDictionary* LynxHttpResponse::httpHeaders httpHeaders;
  public NSString* LynxHttpResponse::url url;
  public NSData* LynxHttpResponse::httpBody httpBody;
  public NSDictionary<NSString *, NSObject *>* LynxHttpResponse::customInfo customInfo;
}

public class LynxImageBlurUtils : NSObject {
  public UIImage * LynxImageBlurUtils::blurImage:withRadius:(UIImage *inputImage,[withRadius] CGFloat radius);
}

public protocol LynxImageFetcher-p : <NSObject> {
  public dispatch_block_t LynxImageFetcher-p::loadImageWithURL:size:contextInfo:completion:(NSURL *url,[size] CGSize targetSize,[contextInfo] nullable NSDictionary *contextInfo,[completion] LynxImageLoadCompletionBlock completionBlock);
  public dispatch_block_t LynxImageFetcher-p::loadImageWithURL:processors:size:contextInfo:completion:(NSURL *url,[processors] NSArray< id< LynxImageProcessor > > *processors,[size] CGSize targetSize,[contextInfo] NSDictionary *contextInfo,[completion] LynxImageLoadCompletionBlock completionBlock);
  public void LynxImageFetcher-p::loadImageWithURL:size:completion:(NSURL *url,[size] CGSize targetSize,[completion](deprecated("Use loadImageWithURL:size:contextInfo:completion: instead.")) __attribute__);
  public dispatch_block_t LynxImageFetcher-p::cancelableLoadImageWithURL:size:completion:(NSURL *url,[size] CGSize targetSize,[completion](deprecated("Use loadImageWithURL:size:contextInfo:completion: instead.")) __attribute__);
}

public class LynxImageLoader : NSObject {
  public nonnull instancetype LynxImageLoader::sharedInstance();
  public id< LynxServiceImageProtocol > LynxImageLoader::imageService();
  public dispatch_block_t LynxImageLoader::loadImageFromLynxURL:size:contextInfo:processors:imageFetcher:LynxUIImage:enableGenericFetcher:completed:(LynxURL *requestUrl,[size] CGSize targetSize,[contextInfo] NSDictionary *contextInfo,[processors] nullable NSArray *processors,[imageFetcher] nullable id< LynxImageFetcher > imageFetcher,[LynxUIImage] nullable LynxUIImage *lynxUIImage,[enableGenericFetcher] BOOL enableGenericFetcher,[completed] LynxImageLoadCompletionBlock completed);
  public dispatch_block_t LynxImageLoader::loadImageFromURL:size:contextInfo:processors:imageFetcher:completed:(NSURL *url,[size] CGSize targetSize,[contextInfo] NSDictionary *contextInfo,[processors] NSArray *processors,[imageFetcher] id< LynxImageFetcher > imageFetcher,[completed] LynxImageLoadCompletionBlock completed);
}

public protocol LynxImageProcessor-p : <NSObject> {
  public UIImage * LynxImageProcessor-p::processImage:(UIImage *image);
  public NSString * LynxImageProcessor-p::cacheKey();
}

public protocol LynxImpressionParentView-p : <NSObject> {
  public BOOL LynxImpressionParentView-p::shouldManualExposure();
}

public class LynxInitBackgroundRuntimeEntry : LynxPerformanceEntry {
  public NSNumber* LynxInitBackgroundRuntimeEntry::loadCoreStart loadCoreStart;
  public NSNumber* LynxInitBackgroundRuntimeEntry::loadCoreEnd loadCoreEnd;
  public instancetype LynxInitBackgroundRuntimeEntry::initWithDictionary:(NSDictionary *dictionary);
}

public class LynxInitContainerEntry : LynxPerformanceEntry {
  public NSNumber* LynxInitContainerEntry::openTime openTime;
  public NSNumber* LynxInitContainerEntry::containerInitStart containerInitStart;
  public NSNumber* LynxInitContainerEntry::containerInitEnd containerInitEnd;
  public NSNumber* LynxInitContainerEntry::prepareTemplateStart prepareTemplateStart;
  public NSNumber* LynxInitContainerEntry::prepareTemplateEnd prepareTemplateEnd;
  public NSDictionary* LynxInitContainerEntry::extraTiming extraTiming;
  public instancetype LynxInitContainerEntry::initWithDictionary:(NSDictionary *dictionary);
}

public class LynxInitLynxviewEntry : LynxPerformanceEntry {
  public NSNumber* LynxInitLynxviewEntry::createLynxStart createLynxStart;
  public NSNumber* LynxInitLynxviewEntry::createLynxEnd createLynxEnd;
  public instancetype LynxInitLynxviewEntry::initWithDictionary:(NSDictionary *dictionary);
}

public class LynxInnerImpressionView : UIView {
  public BOOL LynxInnerImpressionView::onScreen onScreen;
  public float LynxInnerImpressionView::impressionPercent impressionPercent;
  public void LynxInnerImpressionView::impression();
  public void LynxInnerImpressionView::exit();
}

public protocol LynxJSBTimingListener-p : <NSObject> {
  public void LynxJSBTimingListener-p::lynxView:onCallJSBFinished:(LynxView *lynxView,[onCallJSBFinished] NSDictionary *info);
  public void LynxJSBTimingListener-p::lynxView:onJSBInvoked:(LynxView *lynxView,[onJSBInvoked] NSDictionary *jsbInfo);
}

public class LynxKeyboardEventDispatcher : NSObject {
  public instancetype LynxKeyboardEventDispatcher::initWithContext:(LynxContext *context);
}

public class LynxKeyframeAnimator : NSObject typedef NS_ENUMNSUInteger, LynxKFAnimatorState {
  public LynxUI* LynxKeyframeAnimator::ui ui;
  public NSString* LynxKeyframeAnimator::kTransformStr kTransformStr;
  public NSString* LynxKeyframeAnimator::kOpacityStr kOpacityStr;
  public NSString* LynxKeyframeAnimator::kBackgroundColorStr kBackgroundColorStr;
  public NSMutableDictionary* LynxKeyframeAnimator::propertyOriginValue propertyOriginValue;
  public BOOL LynxKeyframeAnimator::autoResumeAnimation autoResumeAnimation;
  public instancetype LynxKeyframeAnimator::initWithUI:(LynxUI *ui);
  public void LynxKeyframeAnimator::apply:(LynxAnimationInfo *info);
  public void LynxKeyframeAnimator::destroy();
  public void LynxKeyframeAnimator::cancel();
  public void LynxKeyframeAnimator::notifyBGLayerAdded();
  public void LynxKeyframeAnimator::notifyPropertyUpdated:value:(NSString *name,[value] id value);
  public BOOL LynxKeyframeAnimator::isRunning();
  public BOOL LynxKeyframeAnimator::shouldReInitTransform();
  public void LynxKeyframeAnimator::tryToResumeAnimationOnNextFrame();
  public void LynxKeyframeAnimator::detachFromUI();
  public void LynxKeyframeAnimator::attachToUI:(LynxUI *ui);
}

public class LynxKeyframeManager : NSObject {
  public LynxUI* LynxKeyframeManager::ui ui;
  public BOOL LynxKeyframeManager::autoResumeAnimation autoResumeAnimation;
  public instancetype LynxKeyframeManager::initWithUI:(LynxUI *ui);
  public void LynxKeyframeManager::setAnimations:(NSArray< LynxAnimationInfo * > *infos);
  public void LynxKeyframeManager::setAnimation:(LynxAnimationInfo *info);
  public void LynxKeyframeManager::notifyAnimationUpdated();
  public void LynxKeyframeManager::notifyBGLayerAdded();
  public void LynxKeyframeManager::notifyPropertyUpdated:value:(NSString *name,[value] id value);
  public void LynxKeyframeManager::endAllAnimation();
  public BOOL LynxKeyframeManager::hasAnimationRunning();
  public void LynxKeyframeManager::resetAnimation();
  public void LynxKeyframeManager::restartAnimation();
  public void LynxKeyframeManager::resumeAnimation();
  public void LynxKeyframeManager::detachFromUI();
  public void LynxKeyframeManager::attachToUI:(LynxUI *ui);
}

public class LynxKeyframeParsedData : NSObject {
  public NSMutableDictionary<NSString*, NSMutableArray*>* LynxKeyframeParsedData::keyframeValues keyframeValues;
  public NSMutableDictionary<NSString*, NSMutableArray<NSNumber*>*>* LynxKeyframeParsedData::keyframeTimes keyframeTimes;
  public NSMutableDictionary<NSString*, id>* LynxKeyframeParsedData::beginStyles beginStyles;
  public NSMutableDictionary<NSString*, id>* LynxKeyframeParsedData::endStyles endStyles;
  public BOOL LynxKeyframeParsedData::isPercentTransform isPercentTransform;
  public instancetype LynxKeyframeParsedData::init();
}

public class LynxKeyframes : NSObject {
  public NSDictionary<NSString*, NSDictionary<NSString*, id>*>* LynxKeyframes::styles styles;
}

public class LynxLayoutAnimationManager : NSObject {
  public LynxAnimationInfo* LynxLayoutAnimationManager::createConfig createConfig;
  public LynxAnimationInfo* LynxLayoutAnimationManager::updateConfig updateConfig;
  public LynxAnimationInfo* LynxLayoutAnimationManager::deleteConfig deleteConfig;
  public instancetype LynxLayoutAnimationManager::initWithLynxUI:(LynxUI *ui);
  public void LynxLayoutAnimationManager::removeAllLayoutAnimation();
  public BOOL LynxLayoutAnimationManager::maybeUpdateFrameWithLayoutAnimation:withPadding:border:margin:(CGRect newFrame,[withPadding] UIEdgeInsets padding,[border] UIEdgeInsets border,[margin] UIEdgeInsets margin);
}

public class LynxLayoutNode : <__covariant V> {
  public NSString* LynxLayoutNode::tagName tagName;
  public CGRect LynxLayoutNode::frame frame;
  public UIEdgeInsets LynxLayoutNode::padding padding;
  public UIEdgeInsets LynxLayoutNode::margin margin;
  public UIEdgeInsets LynxLayoutNode::border border;
  public LynxLayoutStyle* LynxLayoutNode::style style;
  public id<LynxMeasureDelegate> LynxLayoutNode::measureDelegate measureDelegate;
  public id<LynxCustomMeasureDelegate> LynxLayoutNode::customMeasureDelegate customMeasureDelegate;
  public LynxLayoutNodeManager* LynxLayoutNode::layoutNodeManager layoutNodeManager;
  public instancetype LynxLayoutNode::initWithSign:tagName:(NSInteger sign,[tagName] NSString *tagName);
  public void LynxLayoutNode::adoptNativeLayoutNode:(int64_t ptr);
  public void LynxLayoutNode::updateLayoutWithFrame:(CGRect frame);
  public MeasureResult LynxLayoutNode::measureWithWidth:widthMode:height:heightMode:finalMeasure:(float width,[widthMode] LynxMeasureMode widthMode,[height] float height,[heightMode] LynxMeasureMode heightMode,[finalMeasure] bool finalMeasure);
  public void LynxLayoutNode::align();
  public void LynxLayoutNode::setNeedsLayout();
  public void LynxLayoutNode::internalSetNeedsLayoutForce();
  public BOOL LynxLayoutNode::needsLayout();
  public void LynxLayoutNode::layoutDidStart();
  public void LynxLayoutNode::layoutDidUpdate();
  public BOOL LynxLayoutNode::hasCustomLayout();
}

public class LynxLayoutNodeManager : NSObject {
  public instancetype LynxLayoutNodeManager::initWithNativePtr:(void *nativePtr);
  public LynxFlexDirection LynxLayoutNodeManager::getFlexDirection:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getMarginLeft:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getMarginRight:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getMarginTop:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getMarginBottom:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getPaddingLeft:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getPaddingRight:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getPaddingTop:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getPaddingBottom:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getWidth:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getHeight:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getMinWidth:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getMaxWidth:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getMinHeight:(NSInteger sign);
  public CGFloat LynxLayoutNodeManager::getMaxHeigh:(NSInteger sign);
  public void LynxLayoutNodeManager::setMeasureFuncWithSign:LayoutNode:(NSInteger sign,[LayoutNode] LynxLayoutNode *layoutNode);
  public void LynxLayoutNodeManager::markDirtyAndRequestLayout:(NSInteger sign);
  public void LynxLayoutNodeManager::markDirtyAndForceLayout:(NSInteger sign);
  public bool LynxLayoutNodeManager::isDirty:(NSInteger sign);
  public MeasureResult LynxLayoutNodeManager::measureWithSign:MeasureParam:MeasureContext:(NSInteger sign,[MeasureParam] MeasureParam *param,[MeasureContext] MeasureContext *context);
  public void LynxLayoutNodeManager::alignWithSign:AlignParam:AlignContext:(NSInteger sign,[AlignParam] AlignParam *param,[AlignContext] AlignContext *context);
}

public class LynxLayoutSpec : NSObject {
  public CGFloat LynxLayoutSpec::width width;
  public CGFloat LynxLayoutSpec::height height;
  public LynxMeasureMode LynxLayoutSpec::widthMode widthMode;
  public LynxMeasureMode LynxLayoutSpec::heightMode heightMode;
  public LynxTextOverflowType LynxLayoutSpec::textOverflow textOverflow;
  public LynxOverflow LynxLayoutSpec::overflow overflow;
  public LynxWhiteSpaceType LynxLayoutSpec::whiteSpace whiteSpace;
  public NSInteger LynxLayoutSpec::maxLineNum maxLineNum;
  public NSInteger LynxLayoutSpec::maxTextLength maxTextLength;
  public LynxTextStyle* LynxLayoutSpec::textStyle textStyle;
  public id<NSLayoutManagerDelegate> LynxLayoutSpec::layoutManagerDelegate layoutManagerDelegate;
  public LynxVerticalAlign LynxLayoutSpec::verticalAlign verticalAlign;
  public BOOL LynxLayoutSpec::enableTailColorConvert enableTailColorConvert;
  public BOOL LynxLayoutSpec::enableTextNonContiguousLayout enableTextNonContiguousLayout;
  public BOOL LynxLayoutSpec::enableTextRefactor enableTextRefactor;
  public BOOL LynxLayoutSpec::enableNewClipMode enableNewClipMode;
  public LynxVerticalAlign LynxLayoutSpec::textSingleLineVerticalAlign textSingleLineVerticalAlign;
  public BOOL LynxLayoutSpec::widthUndifined widthUndifined;
  public BOOL LynxLayoutSpec::heightUndifined heightUndifined;
  public instancetype LynxLayoutSpec::initWithWidth:height:widthMode:heightMode:textOverflow:overflow:whiteSpace:maxLineNum:maxTextLength:textStyle:enableTailColorConvert:(CGFloat width,[height] CGFloat height,[widthMode] LynxMeasureMode widthMode,[heightMode] LynxMeasureMode heightMode,[textOverflow] LynxTextOverflowType textOverflow,[overflow] LynxOverflow overflow,[whiteSpace] LynxWhiteSpaceType whiteSpace,[maxLineNum] NSInteger maxLineNum,[maxTextLength] NSInteger maxTextLength,[textStyle] LynxTextStyle *textStyle,[enableTailColorConvert] BOOL enableTailColorConvert);
  public BOOL LynxLayoutSpec::isEqualToSpec:(LynxLayoutSpec *spec);
}

public class LynxLayoutStyle : NSObject {
  public NSInteger LynxLayoutStyle::sign sign;
  public instancetype LynxLayoutStyle::initWithSign:layoutNodeManager:(NSInteger sign,[layoutNodeManager] LynxLayoutNodeManager *layoutNodeManager);
  public LynxFlexDirection LynxLayoutStyle::flexDirection();
  public CGFloat LynxLayoutStyle::computedMarginLeft();
  public CGFloat LynxLayoutStyle::computedMarginRight();
  public CGFloat LynxLayoutStyle::computedMarginTop();
  public CGFloat LynxLayoutStyle::computedMarginBottom();
  public CGFloat LynxLayoutStyle::computedPaddingLeft();
  public CGFloat LynxLayoutStyle::computedPaddingRight();
  public CGFloat LynxLayoutStyle::computedPaddingTop();
  public CGFloat LynxLayoutStyle::computedPaddingBottom();
  public CGFloat LynxLayoutStyle::computedWidth();
  public CGFloat LynxLayoutStyle::computedHeight();
  public CGFloat LynxLayoutStyle::computedMinWidth();
  public CGFloat LynxLayoutStyle::computedMaxWidth();
  public CGFloat LynxLayoutStyle::computedMinHeight();
  public CGFloat LynxLayoutStyle::computedMaxHeight();
}

public class LynxLayoutTick : NSObject {
  public nonnull instancetype LynxLayoutTick::initWithBlock:(nonnull LynxOnLayoutBlock block);
  public void LynxLayoutTick::requestLayout();
  public void LynxLayoutTick::triggerLayout();
  public void LynxLayoutTick::cancelLayoutRequest();
}

public struct LynxLengthContext {
  public float LynxLengthContext::screen_width screen_width;
  public float LynxLengthContext::layouts_unit_per_px layouts_unit_per_px;
  public float LynxLengthContext::physical_pixels_per_layout_unit physical_pixels_per_layout_unit;
  public float LynxLengthContext::root_node_font_size root_node_font_size;
  public float LynxLengthContext::cur_node_font_size cur_node_font_size;
  public float LynxLengthContext::font_scale font_scale;
  public float LynxLengthContext::viewport_width viewport_width;
  public float LynxLengthContext::viewport_height viewport_height;
  public bool LynxLengthContext::font_scale_sp_only font_scale_sp_only;
}

public class LynxLifecycleDispatcher : NSObject, <LynxViewLifecycle>, <LynxViewLifecycleV2> {
  public NSArray<id<LynxViewBaseLifecycle> >* LynxLifecycleDispatcher::lifecycleClients lifecycleClients;
  public int32_t LynxLifecycleDispatcher::instanceId instanceId;
  public void LynxLifecycleDispatcher::addLifecycleClient:(id< LynxViewBaseLifecycle > lifecycleClient);
  public void LynxLifecycleDispatcher::removeLifecycleClient:(id< LynxViewBaseLifecycle > lifecycleClient);
}

public class LynxLinearGradient : LynxGradient {
  public double LynxLinearGradient::angle angle;
  public LynxLinearGradientDirection LynxLinearGradient::directionType directionType;
  public instancetype LynxLinearGradient::initWithArray:(NSArray *arr);
  public void LynxLinearGradient::computeStartPoint:andEndPoint:withSize:(CGPoint *_Nonnull startPoint,[andEndPoint] CGPoint *_Nonnull endPoint,[withSize] const CGSize *_Nonnull size);
}

public class LynxListAnchorManager : NSObject {
  public BOOL LynxListAnchorManager::isVerticalLayout isVerticalLayout;
  public NSInteger LynxListAnchorManager::numberOfColumns numberOfColumns;
  public NSInteger LynxListAnchorManager::findAnchorCell:anchorPolicies:layoutInfo:(LynxListCachedCellManager *cachedCells,[anchorPolicies] LynxAnchorPolicies anchorPolicies,[layoutInfo] NSMutableArray< NSMutableArray< NSNumber * > * > *layoutColumnInfo);
  public NSInteger LynxListAnchorManager::findAnchorCellForRemoval:anchorPolicies:layoutInfo:deleteIndexes:(LynxListCachedCellManager *cachedCells,[anchorPolicies] LynxAnchorPolicies anchorPolicies,[layoutInfo] NSMutableArray< NSMutableArray< NSNumber * > * > *layoutColumnInfo,[deleteIndexes] NSArray< NSNumber * > *deleteIndexes);
  public NSInteger LynxListAnchorManager::closestAttributesToUpperVisibleBound:inColumn:(NSInteger index,[inColumn] NSArray< NSNumber * > *columnInfo);
  public NSInteger LynxListAnchorManager::closestAttributesToLowerVisibleBound:inColumn:(NSInteger index,[inColumn] NSArray< NSNumber * > *columnInfo);
}

public class LynxListAppearEventEmitter : NSObject {
  public instancetype LynxListAppearEventEmitter::initWithEmitter:(LynxEventEmitter *emitter);
  public void LynxListAppearEventEmitter::onCellAppear:atIndexPath:(LynxUIComponent *ui,[atIndexPath] NSIndexPath *indexPath);
  public void LynxListAppearEventEmitter::onCellDisappear:atIndexPath:(LynxUIComponent *ui,[atIndexPath] NSIndexPath *indexPath);
  public void LynxListAppearEventEmitter::invalidate();
  public void LynxListAppearEventEmitter::setListUI:(LynxUICollection *ui);
}

public class LynxListCachedCellManager : NSObject {
  public NSMutableArray<id<LynxListCell> >* LynxListCachedCellManager::displayingCells displayingCells;
  public NSMutableArray<id<LynxListCell> >* LynxListCachedCellManager::upperCachedCells upperCachedCells;
  public NSMutableArray<id<LynxListCell> >* LynxListCachedCellManager::lowerCachedCells lowerCachedCells;
  public NSArray<id<LynxListCell> >* LynxListCachedCellManager::allCachedCells allCachedCells;
  public id<LynxListCell> LynxListCachedCellManager::lastVisibleCell lastVisibleCell;
  public id<LynxListCell> LynxListCachedCellManager::firstVisibleCell firstVisibleCell;
  public NSInteger LynxListCachedCellManager::numberOfColumns numberOfColumns;
  public BOOL LynxListCachedCellManager::isVerticalLayout isVerticalLayout;
  public instancetype LynxListCachedCellManager::initWithColumnCount:uiContext:(NSInteger numberOfColumns,[uiContext] LynxUIContext *context);
  public void LynxListCachedCellManager::addCell:inArray:(id< LynxListCell > cell,[inArray] NSMutableArray< id< LynxListCell > > *cacheArray);
  public id< LynxListCell > _Nullable LynxListCachedCellManager::findCellAtIndex:inArray:(NSInteger index,[inArray] NSMutableArray< id< LynxListCell > > *cacheArray);
  public BOOL LynxListCachedCellManager::markRemoveCellAtIndex:(NSInteger index);
  public void LynxListCachedCellManager::markCellInfoDirty();
  public id< LynxListCell > LynxListCachedCellManager::removeCellAtIndex:(NSInteger index);
  public id< LynxListCell > _Nullable LynxListCachedCellManager::cellAtIndex:(NSInteger index);
  public NSMutableDictionary< NSNumber *, id< LynxListCell > > * LynxListCachedCellManager::topCells();
  public NSMutableDictionary< NSNumber *, id< LynxListCell > > * LynxListCachedCellManager::bottomCells();
  public NSInteger LynxListCachedCellManager::lastIndexInPathOrder();
  public NSInteger LynxListCachedCellManager::firstIndexInPathOrder();
  public BOOL LynxListCachedCellManager::isEmpty();
}

public protocol LynxListCell-p : <NSObject> {
  public NSInteger LynxListCell-p::updateToPath updateToPath;
  public NSString* LynxListCell-p::itemKey itemKey;
  public BOOL LynxListCell-p::removed removed;
  public NSInteger LynxListCell-p::columnIndex columnIndex;
  public LynxLayoutModelType LynxListCell-p::layoutType layoutType;
  public NSString* LynxListCell-p::reuseIdentifier reuseIdentifier;
  public CGRect LynxListCell-p::frame frame;
  public int64_t LynxListCell-p::operationID operationID;
  public BOOL LynxListCell-p::isInStickyStatus isInStickyStatus;
  public CGFloat LynxListCell-p::stickyPosition stickyPosition;
  public UIView* LynxListCell-p::contentView contentView;
  public void LynxListCell-p::applyLayoutModel:(LynxListLayoutModelLight *model);
}

public protocol LynxListCellContentProducer-p : <NSObject> {
  public id< LynxListCell > LynxListCellContentProducer-p::cell:forKey:(id< LynxListCell > cell,[forKey] NSString *itemKey);
}

public protocol LynxListEventsProtocol-p : <NSObject> {
  public NSInteger LynxListEventsProtocol-p::totalItemsCount();
  public NSArray< id< LynxListCell > > * LynxListEventsProtocol-p::attachedCells();
}

public class LynxListLayoutManager : NSObject, <LynxListLayoutProtocol> {
  public NSMutableArray<LynxListLayoutModelLight *>* LynxListLayoutManager::models models;
  public NSInteger LynxListLayoutManager::firstInvalidIndex firstInvalidIndex;
  public NSUInteger LynxListLayoutManager::numberOfColumns numberOfColumns;
  public CGFloat LynxListLayoutManager::mainAxisGap mainAxisGap;
  public CGFloat LynxListLayoutManager::crossAxisGap crossAxisGap;
  public UIEdgeInsets LynxListLayoutManager::insets insets;
  public CGRect LynxListLayoutManager::bounds bounds;
  public NSArray<NSNumber *>* LynxListLayoutManager::fullSpanItems fullSpanItems;
  public LynxListLayoutType LynxListLayoutManager::layoutType layoutType;
  public BOOL LynxListLayoutManager::needAlignHeight needAlignHeight;
  public NSInteger LynxListLayoutManager::lastValidModel lastValidModel;
  public NSMutableArray<NSNumber *>* LynxListLayoutManager::mainSizes mainSizes;
  public NSMutableArray<NSArray<NSNumber *> *>* LynxListLayoutManager::mainSizesCache mainSizesCache;
  public NSMutableArray<NSMutableArray<NSNumber *> *>* LynxListLayoutManager::layoutColumnInfo layoutColumnInfo;
  public NSDictionary<NSNumber *, NSNumber *>* LynxListLayoutManager::estimatedHeights estimatedHeights;
  public void LynxListLayoutManager::retrieveMainSizeFromCacheAtInvalidIndex:(NSInteger invalidIndex);
  public CGFloat LynxListLayoutManager::largestSizeInMainSizes:(NSArray< NSNumber * > *mainSizes);
  public NSUInteger LynxListLayoutManager::findNearestFullSpanItem:(NSUInteger index);
  public CGFloat LynxListLayoutManager::largestMainSizeInPreviousRowAtIndex:withNearestFullSpanIndex:(NSUInteger index,[withNearestFullSpanIndex] NSUInteger nearestFullSpanIndex);
  public void LynxListLayoutManager::resetMainSizesWithNumberOfColumns:(NSUInteger numberOfColumns);
  public CGFloat LynxListLayoutManager::layoutOffsetForFullSpanItems:crossSize:paddingStart:paddingEnd:(CGFloat itemSize,[crossSize] CGFloat collectionSize,[paddingStart] CGFloat paddingStart,[paddingEnd] CGFloat paddingEnd);
  public CGFloat LynxListLayoutManager::largestMainSize();
  public CGFloat LynxListLayoutManager::adjustOffsetAtIndex:originalOffset:nearestFullSpan:(NSUInteger index,[originalOffset] CGFloat Offset,[nearestFullSpan] NSUInteger nearestFullSpanIndex);
  public NSUInteger LynxListLayoutManager::shortestColumn();
  public CGFloat LynxListLayoutManager::shortestMainSize();
}

public class LynxListLayoutModelLight : NSObject {
  public CGRect LynxListLayoutModelLight::frame frame;
  public LynxLayoutModelType LynxListLayoutModelLight::type type;
  public NSInteger LynxListLayoutModelLight::zIndex zIndex;
  public NSInteger LynxListLayoutModelLight::columnIndex columnIndex;
  public instancetype LynxListLayoutModelLight::initWithFrame:(CGRect frame);
}

public protocol LynxListLayoutProtocol-p : <NSObject> {
  public NSInteger LynxListLayoutProtocol-p::firstInvalidIndex firstInvalidIndex;
  public NSMutableArray<NSMutableArray<NSNumber *> *>* LynxListLayoutProtocol-p::layoutColumnInfo layoutColumnInfo;
  public NSInteger LynxListLayoutProtocol-p::lastValidModel lastValidModel;
  public NSUInteger LynxListLayoutProtocol-p::numberOfColumns numberOfColumns;
  public CGFloat LynxListLayoutProtocol-p::mainAxisGap mainAxisGap;
  public CGFloat LynxListLayoutProtocol-p::crossAxisGap crossAxisGap;
  public UIEdgeInsets LynxListLayoutProtocol-p::insets insets;
  public NSInteger LynxListLayoutProtocol-p::getCount();
  public CGSize LynxListLayoutProtocol-p::getContentSize();
  public BOOL LynxListLayoutProtocol-p::isVerticalLayout();
  public void LynxListLayoutProtocol-p::updateModelsWithRemovals:(NSArray< NSNumber * > *removals);
  public void LynxListLayoutProtocol-p::updateModelsWithInsertions:(NSArray< NSNumber * > *insertions);
  public void LynxListLayoutProtocol-p::updateModels:(NSDictionary< NSNumber *, NSValue * > *updates);
  public NSDictionary< NSNumber *, NSNumber * > * LynxListLayoutProtocol-p::findWhichItemToDisplayOnTop();
  public void LynxListLayoutProtocol-p::updateBasicInvalidationContext:bounds:(LynxUIListInvalidationContext *context,[bounds] CGRect bounds);
  public void LynxListLayoutProtocol-p::layoutFrom:to:(NSInteger startIndex,[to] NSInteger endIndex);
  public LynxListLayoutModelLight * LynxListLayoutProtocol-p::attributesFromIndex:(NSInteger index);
  public BOOL LynxListLayoutProtocol-p::layoutModelVisibleInIndex:(NSInteger index);
}

public class LynxListReusePool : NSObject {
  public NSMutableDictionary<NSString *, NSMutableArray<id<LynxListCell> > *>* LynxListReusePool::pool pool;
  public NSMutableDictionary<NSString *, Class>* LynxListReusePool::reuseIdentifierMap reuseIdentifierMap;
  public void LynxListReusePool::enqueueReusableCell:(id< LynxListCell > cell);
  public id< LynxListCell > LynxListReusePool::dequeueReusableCellInIndex:withReuseIdentifier:(NSInteger index,[withReuseIdentifier] NSString *reuseIdentifier);
  public void LynxListReusePool::registerClass:forCellReuseIdentifier:(Class cellClass,[forCellReuseIdentifier] nonnull NSString *identifier);
}

public class LynxListScrollEventEmitter : NSObject, <UIScrollViewDelegate> {
  public id<LynxListScrollEventEmitterDelegate> LynxListScrollEventEmitter::delegate delegate;
  public BOOL LynxListScrollEventEmitter::enableScrollEvent enableScrollEvent;
  public BOOL LynxListScrollEventEmitter::enableScrollToLowerEvent enableScrollToLowerEvent;
  public BOOL LynxListScrollEventEmitter::enableScrollToUpperEvent enableScrollToUpperEvent;
  public CGFloat LynxListScrollEventEmitter::scrollEventThrottle scrollEventThrottle;
  public CGFloat LynxListScrollEventEmitter::scrollUpperThreshold scrollUpperThreshold;
  public CGFloat LynxListScrollEventEmitter::scrollLowerThreshold scrollLowerThreshold;
  public BOOL LynxListScrollEventEmitter::horizontalLayout horizontalLayout;
  public LynxListScrollEventEmitterHelper* LynxListScrollEventEmitter::helper helper;
  public instancetype LynxListScrollEventEmitter::init();
  public instancetype LynxListScrollEventEmitter::initWithLynxUI:(LynxUI *lynxUI);
  public void LynxListScrollEventEmitter::attachToLynxUI:(LynxUI *lynxUI);
  public void LynxListScrollEventEmitter::scrollViewDidEndDecelerating:(UIScrollView *scrollView);
  public void LynxListScrollEventEmitter::scrollViewDidEndDragging:willDecelerate:(UIScrollView *scrollView,[willDecelerate] BOOL decelerate);
  public void LynxListScrollEventEmitter::scrollViewDidScroll:(UIScrollView *scrollView);
  public void LynxListScrollEventEmitter::scrollViewWillBeginDragging:(UIScrollView *scrollView);
  public void LynxListScrollEventEmitter::helperSendScrollEvent:(UIScrollView *scrollView);
}

public protocol LynxListScrollEventEmitterDelegate-p : <NSObject> {
  public BOOL LynxListScrollEventEmitterDelegate-p::shouldForceSendLowerThresholdEvent();
  public BOOL LynxListScrollEventEmitterDelegate-p::shouldForceSendUpperThresholdEvent();
  public NSArray * LynxListScrollEventEmitterDelegate-p::attachedCellsArray();
}

public class LynxListScrollEventEmitterHelper : NSObject {
  public LynxListScrollPosition LynxListScrollEventEmitterHelper::scrollPosition scrollPosition;
  public LynxListScrollState LynxListScrollEventEmitterHelper::scrollState scrollState;
  public BOOL LynxListScrollEventEmitterHelper::horizontalLayout horizontalLayout;
  public instancetype LynxListScrollEventEmitterHelper::initWithEmitter:(LynxListScrollEventEmitter *emitter);
  public NSString * LynxListScrollEventEmitterHelper::fetchScrollEvent:(UIScrollView *scrollView);
}

public class LynxListViewCellLight : UIView, <LynxListCell> {
  public UIView* LynxListViewCellLight::contentView contentView;
  public NSString* LynxListViewCellLight::reuseIdentifier reuseIdentifier;
  public NSInteger LynxListViewCellLight::updateToPath updateToPath;
  public NSString* LynxListViewCellLight::itemKey itemKey;
  public BOOL LynxListViewCellLight::removed removed;
  public NSInteger LynxListViewCellLight::columnIndex columnIndex;
  public LynxLayoutModelType LynxListViewCellLight::layoutType layoutType;
  public BOOL LynxListViewCellLight::isInStickyStatus isInStickyStatus;
  public CGFloat LynxListViewCellLight::stickyPosition stickyPosition;
  public int64_t LynxListViewCellLight::operationID operationID;
  public void LynxListViewCellLight::applyLayoutModel:(LynxListLayoutModelLight *model);
}

public class LynxListViewCellLightLynxUI : LynxListViewCellLight {
  public LynxUIComponent* LynxListViewCellLightLynxUI::ui ui;
  public void LynxListViewCellLightLynxUI::addLynxUI:(LynxUI *ui);
  public LynxUI * LynxListViewCellLightLynxUI::removeLynxUI();
}

public class LynxListViewLight : UIScrollView, <LynxListEventsProtocol> {
  public NSInteger LynxListViewLight::numberOfColumns numberOfColumns;
  public BOOL LynxListViewLight::verticalOrientation verticalOrientation;
  public NSInteger LynxListViewLight::preloadBufferCount preloadBufferCount;
  public BOOL LynxListViewLight::anchorPriorityFromBegin anchorPriorityFromBegin;
  public BOOL LynxListViewLight::deleteRegressPolicyToTop deleteRegressPolicyToTop;
  public BOOL LynxListViewLight::insertAnchorModeInside insertAnchorModeInside;
  public LynxAnchorVisibility LynxListViewLight::anchorVisibility anchorVisibility;
  public BOOL LynxListViewLight::anchorAlignToBottom anchorAlignToBottom;
  public BOOL LynxListViewLight::isAsync isAsync;
  public BOOL LynxListViewLight::enableFadeInAnimation enableFadeInAnimation;
  public CGFloat LynxListViewLight::updateAnimationFadeInDuration updateAnimationFadeInDuration;
  public void LynxListViewLight::setLayout:(LynxListLayoutManager *_Nullable layout);
  public void LynxListViewLight::setDataSource:(LynxUIListDataSource *dataSource);
  public void LynxListViewLight::setSign:(NSInteger sign);
  public void LynxListViewLight::setUIContext:(LynxUIContext *context);
  public void LynxListViewLight::setEventEmitter:(LynxEventEmitter *eventEmitter);
  public void LynxListViewLight::updateFrame:withPadding:border:margin:withLayoutAnimation:(CGRect frame,[withPadding] UIEdgeInsets padding,[border] UIEdgeInsets border,[margin] UIEdgeInsets margin,[withLayoutAnimation] BOOL with);
  public void LynxListViewLight::updateScrollThresholds:(LynxUIListScrollThresholds *scrollThreSholds);
  public void LynxListViewLight::dispatchInvalidationContext:(LynxUIListInvalidationContext *context);
  public NSArray< id< LynxListCell > > * LynxListViewLight::visibleCells();
  public void LynxListViewLight::updateReuseIdentifiers:(NSArray< NSString * > *reuseIdentifiers);
  public void LynxListViewLight::registerCellClass:reuseIdentifiers:(Class cellClass,[reuseIdentifiers] NSArray< NSString * > *reuseIdentifiers);
  public void LynxListViewLight::invalidLayoutFromIndex:(NSInteger index);
  public id< LynxListCell > LynxListViewLight::dequeueReusableCellForIndex:(NSInteger index);
  public void LynxListViewLight::onAsyncComponentLayoutUpdated:operationID:(nonnull LynxUIComponent *component,[operationID] int64_t operationID);
  public void LynxListViewLight::onComponentLayoutUpdated:(LynxUIComponent *component);
  public id< LynxListCell > LynxListViewLight::visibleCellAtPoint:(CGPoint point);
  public id< LynxEventTarget > LynxListViewLight::findHitTestTarget:withEvent:(CGPoint point,[withEvent] UIEvent *event);
}

public class LynxLoadBundleEntry : LynxPipelineEntry {
  public NSNumber* LynxLoadBundleEntry::loadBundleStart loadBundleStart;
  public NSNumber* LynxLoadBundleEntry::loadBundleEnd loadBundleEnd;
  public NSNumber* LynxLoadBundleEntry::parseStart parseStart;
  public NSNumber* LynxLoadBundleEntry::parseEnd parseEnd;
  public NSNumber* LynxLoadBundleEntry::loadBackgroundStart loadBackgroundStart;
  public NSNumber* LynxLoadBundleEntry::loadBackgroundEnd loadBackgroundEnd;
  public instancetype LynxLoadBundleEntry::initWithDictionary:(NSDictionary *dictionary);
}

public class LynxLoadMeta : NSObject {
  public NSString* LynxLoadMeta::url url;
  public LynxLoadMode LynxLoadMeta::loadMode loadMode;
  public LynxLoadOption LynxLoadMeta::loadOption loadOption;
  public NSData* LynxLoadMeta::binaryData binaryData;
  public LynxTemplateData* LynxLoadMeta::initialData initialData;
  public LynxTemplateData* LynxLoadMeta::globalProps globalProps;
  public LynxTemplateBundle* LynxLoadMeta::templateBundle templateBundle;
  public NSMutableDictionary<NSString*, id>* LynxLoadMeta::lynxViewConfig lynxViewConfig;
}

public class LynxLogObserver : NSObject {
  public LynxLogFunction LynxLogObserver::logFunction logFunction;
  public LynxLogLevel LynxLogObserver::minLogLevel minLogLevel;
  public BOOL LynxLogObserver::shouldFormatMessage shouldFormatMessage;
  public LynxLogSource LynxLogObserver::acceptSource acceptSource;
  public NSInteger LynxLogObserver::acceptRuntimeId acceptRuntimeId;
  public instancetype LynxLogObserver::initWithLogFunction:minLogLevel:(LynxLogFunction logFunction,[minLogLevel] LynxLogLevel minLogLevel);
}

public class LynxLruCache : NSObject {
  public NSUInteger LynxLruCache::capacity capacity;
  public instancetype LynxLruCache::initWithCapacity:recreate:viewEvicted:(NSUInteger capacity,[recreate] LynxViewReCreateBlock createBlock,[viewEvicted] LynxViewEvictedBlock evictedBlock);
  public void LynxLruCache::setObject:forKey:(id object,[forKey] id key);
  public id LynxLruCache::objectForKey:(id key);
  public id LynxLruCache::removeObjectForKey:(id key);
}

public class LynxLruCacheNode : NSObject, <NSCoding> {
  public id LynxLruCacheNode::value value;
  public id<NSCopying> LynxLruCacheNode::key key;
  public LynxLruCacheNode* LynxLruCacheNode::next next;
  public LynxLruCacheNode* LynxLruCacheNode::prev prev;
  public instancetype LynxLruCacheNode::nodeWithValue:key:(id value,[key] id< NSCopying > key);
  public instancetype LynxLruCacheNode::initWithValue:key:(id value,[key] id< NSCopying > key);
}

public protocol LynxMeasureDelegate-p : <NSObject> {
  public CGSize LynxMeasureDelegate-p::measureNode:withWidth:widthMode:height:heightMode:(LynxLayoutNode *node,[withWidth] CGFloat width,[widthMode] LynxMeasureMode widthMode,[height] CGFloat height,[heightMode] LynxMeasureMode heightMode);
}

public class lynx::tasm::LynxMeasureFuncDarwin : MeasureFunc {
  public lynx::tasm::LynxMeasureFuncDarwin::LynxMeasureFuncDarwin(LynxLayoutNode *layoutNode);
  public lynx::tasm::LynxMeasureFuncDarwin::~LynxMeasureFuncDarwin() override=default;
  public LayoutResult lynx::tasm::LynxMeasureFuncDarwin::Measure(float width, int width_mode, float height, int height_mode, bool final_measure) override;
  public void lynx::tasm::LynxMeasureFuncDarwin::Alignment() override;
}

public protocol LynxMediaResourceFetcher-p : <NSObject> {
  public NSString *_Nonnull LynxMediaResourceFetcher-p::shouldRedirectUrl:(LynxResourceRequest *_Nonnull request);
  public LynxResourceOptionalBool LynxMediaResourceFetcher-p::isLocalResource:(NSURL *_Nonnull url);
  public dispatch_block_t LynxMediaResourceFetcher-p::fetchUIImage:onComplete:(LynxResourceRequest *_Nonnull request,[onComplete] LynxMediaResourceCompletionBlock _Nonnull response);
}

public class LynxMemoryListener : NSObject {
  public NSMutableArray<id<LynxMemoryReporter> >* LynxMemoryListener::memoryReporters memoryReporters;
  public instancetype LynxMemoryListener::shareInstance();
  public void LynxMemoryListener::uploadImageInfo:(NSDictionary *data);
  public void LynxMemoryListener::addMemoryReporter:(id< LynxMemoryReporter > report);
  public void LynxMemoryListener::removeMemoryReporter:(id< LynxMemoryReporter > report);
}

public protocol LynxMemoryReporter-p : <NSObject> {
  public void LynxMemoryReporter-p::uploadImageInfo:(NSDictionary *data);
}

public class LynxMetricActualFmpEntry : LynxPerformanceEntry {
  public LynxPerformanceMetric* LynxMetricActualFmpEntry::actualFmp actualFmp;
  public LynxPerformanceMetric* LynxMetricActualFmpEntry::lynxActualFmp lynxActualFmp;
  public LynxPerformanceMetric* LynxMetricActualFmpEntry::totalActualFmp totalActualFmp;
  public instancetype LynxMetricActualFmpEntry::initWithDictionary:(NSDictionary *dictionary);
}

public class LynxMetricFcpEntry : LynxPerformanceEntry {
  public LynxPerformanceMetric* LynxMetricFcpEntry::fcp fcp;
  public LynxPerformanceMetric* LynxMetricFcpEntry::lynxFcp lynxFcp;
  public LynxPerformanceMetric* LynxMetricFcpEntry::totalFcp totalFcp;
  public instancetype LynxMetricFcpEntry::initWithDictionary:(NSDictionary *dictionary);
}

public class LynxMetricTtiEntry : LynxPerformanceEntry {
  public LynxPerformanceMetric* LynxMetricTtiEntry::tti tti;
  public LynxPerformanceMetric* LynxMetricTtiEntry::lynxTti lynxTti;
  public LynxPerformanceMetric* LynxMetricTtiEntry::totalTti totalTti;
  public instancetype LynxMetricTtiEntry::initWithDictionary:(NSDictionary *dictionary);
}

public protocol LynxModule-p : <NSObject> {
  public NSDictionary<NSString *, NSString *>* LynxModule-p::methodLookup methodLookup;
  public NSDictionary* LynxModule-p::attributeLookup attributeLookup;
  public id LynxModule-p::extraData extraData;
  public instancetype LynxModule-p::init();
  public instancetype LynxModule-p::initWithParam:(id param);
  public void LynxModule-p::destroy();
  public NSString* LynxModule-p::name name;
}

public class LynxNativeLayoutNode : LynxShadowNode {
  public NSString* LynxNativeLayoutNode::idSelector idSelector;
  public MeasureResult LynxNativeLayoutNode::measureWithMeasureParam:MeasureContext:(MeasureParam *param,[MeasureContext] MeasureContext *context);
  public void LynxNativeLayoutNode::alignWithAlignParam:AlignContext:(AlignParam *param,[AlignContext] AlignContext *context);
}

public class LynxNavigator : NSObject {
  public NSInteger LynxNavigator::capacity capacity;
  public id<LynxSchemaInterceptor> LynxNavigator::interceptor interceptor;
  public instancetype LynxNavigator::sharedInstance();
  public void LynxNavigator::setCapacity:(NSInteger capacity);
  public void LynxNavigator::setSchemaInterceptor:(id< LynxSchemaInterceptor > interceptor);
  public void LynxNavigator::registerLynxHolder:(id< LynxHolder > lynxHolder);
  public void LynxNavigator::registerLynxHolder:initLynxView:(id< LynxHolder > lynxHolder,[initLynxView] nullable LynxView *lynxView);
  public void LynxNavigator::unregisterLynxHolder:(id< LynxHolder > lynxHolder);
  public void LynxNavigator::navigate:withParam:(nonnull NSString *name,[withParam] nonnull NSDictionary *param);
  public void LynxNavigator::replace:withParam:(nonnull NSString *name,[withParam] nonnull NSDictionary *param);
  public void LynxNavigator::goBack();
  public BOOL LynxNavigator::onNavigateBack();
  public void LynxNavigator::didEnterForeground:(id< LynxHolder > lynxHolder);
  public void LynxNavigator::didEnterBackground:(id< LynxHolder > lynxHolder);
}

public protocol LynxNewGestureDelegate-p : <NSObject> {
  public void LynxNewGestureDelegate-p::setGestureDetectorState:state:(NSInteger gestureId,[state] LynxGestureState state);
  public void LynxNewGestureDelegate-p::consumeGesture:params:(NSInteger gestureId,[params] NSDictionary *params);
  public NSArray< NSNumber * > * LynxNewGestureDelegate-p::scrollBy:deltaY:(CGFloat deltaX,[deltaY] CGFloat deltaY);
}

public class LynxNinePatchImageProcessor : NSObject, <LynxImageProcessor> {
  public instancetype LynxNinePatchImageProcessor::initWithCapInsets:(UIEdgeInsets capInsets);
  public instancetype LynxNinePatchImageProcessor::initWithCapInsets:capInsetsScale:(UIEdgeInsets capInsets,[capInsetsScale] CGFloat capInsetsScale);
}

public class LynxOffsetCalculator : NSObject {
  public CGPoint LynxOffsetCalculator::pointAtProgress:onPath:withTangent:(CGFloat progress,[onPath] CGPathRef path,[withTangent] nullable CGFloat *tangent);
}

public class LynxPageReloadHelper : NSObject, <LynxPageReloadHelperProto> {
  public void LynxPageReloadHelper()::onTemplateLoadSuccess:(nullable NSData *tem);
  public NSString *_Nullable LynxPageReloadHelper()::getTemplateJsInfo:withSize:(uint32_t offset, [withSize] uint32_t size);
}

public protocol LynxPageReloadHelperProto-p : <NSObject> {
  public nonnull instancetype LynxPageReloadHelperProto-p::initWithLynxView:(LynxView *view);
  public void LynxPageReloadHelperProto-p::loadFromLocalFile:withURL:initData:(NSData *tem,[withURL] NSString *url,[initData] LynxTemplateData *data);
  public void LynxPageReloadHelperProto-p::loadFromURL:initData:(NSString *url,[initData] LynxTemplateData *data);
  public void LynxPageReloadHelperProto-p::loadFromBundle:withURL:initData:(LynxTemplateBundle *bundle,[withURL] NSString *url,[initData] LynxTemplateData *data);
  public nonnull NSString * LynxPageReloadHelperProto-p::getURL();
  public LynxTemplateData * LynxPageReloadHelperProto-p::getTemplateData();
  public void LynxPageReloadHelperProto-p::reloadLynxView:(BOOL ignoreCache);
  public void LynxPageReloadHelperProto-p::reloadLynxView:withTemplate:fromFragments:withSize:(BOOL ignoreCache,[withTemplate] NSString *templateBin,[fromFragments] BOOL fromFragments,[withSize] int32_t size);
  public void LynxPageReloadHelperProto-p::onReceiveTemplateFragment:withEof:(NSString *fragment,[withEof] BOOL eof);
  public void LynxPageReloadHelperProto-p::navigateLynxView:(nonnull NSString *url);
  public void LynxPageReloadHelperProto-p::setTextLable:(NSInteger label);
  public void LynxPageReloadHelperProto-p::removeTextLabel();
  public void LynxPageReloadHelperProto-p::popTextLabel();
  public void LynxPageReloadHelperProto-p::attachLynxView:(nonnull LynxView *lynxView);
}

public class LynxPerformance : NSObject {
  public BOOL LynxPerformance::hasActualFMP hasActualFMP;
  public double LynxPerformance::actualFMPDuration actualFMPDuration;
  public double LynxPerformance::actualFirstScreenEndTimeStamp actualFirstScreenEndTimeStamp;
  public instancetype _Nonnull LynxPerformance::initWithPerformance:url:pageType:reactVersion:(NSDictionary *_Nonnull dic,[url] NSString *_Nonnull url,[pageType] NSString *_Nonnull pageType,[reactVersion] NSString *_Nonnull reactVersion);
  public NSDictionary *_Nonnull LynxPerformance::toDictionary();
  public NSString *_Nullable LynxPerformance::toPerfKey:(int index);
  public NSString *_Nullable LynxPerformance::toPerfKey:isSsrHydrate:(int index,[isSsrHydrate] BOOL isSsrHydrate);
  public NSString *_Nullable LynxPerformance::toPerfStampKey:(int index);
}

public class LynxPerformanceEntry : NSObject {
  public NSString* LynxPerformanceEntry::name name;
  public NSString* LynxPerformanceEntry::entryType entryType;
  public NSDictionary* LynxPerformanceEntry::rawDictionary rawDictionary;
  public instancetype LynxPerformanceEntry::initWithDictionary:(NSDictionary *dictionary);
  public NSDictionary * LynxPerformanceEntry::toDictionary();
}

public class LynxPerformanceEntryConverter : NSObject {
  public LynxPerformanceEntry * LynxPerformanceEntryConverter::makePerformanceEntry:(NSDictionary *dict);
}

public class LynxPerformanceMetric : NSObject {
  public NSString* LynxPerformanceMetric::name name;
  public NSNumber* LynxPerformanceMetric::duration duration;
  public NSString* LynxPerformanceMetric::startTimestampName startTimestampName;
  public NSNumber* LynxPerformanceMetric::startTimestamp startTimestamp;
  public NSString* LynxPerformanceMetric::endTimestampName endTimestampName;
  public NSNumber* LynxPerformanceMetric::endTimestamp endTimestamp;
  public instancetype LynxPerformanceMetric::initWithDictionary:(NSDictionary *dictionary);
}

public class LynxPipelineEntry : LynxPerformanceEntry {
  public NSString* LynxPipelineEntry::identifier identifier;
  public NSNumber* LynxPipelineEntry::pipelineStart pipelineStart;
  public NSNumber* LynxPipelineEntry::pipelineEnd pipelineEnd;
  public NSNumber* LynxPipelineEntry::mtsRenderStart mtsRenderStart;
  public NSNumber* LynxPipelineEntry::mtsRenderEnd mtsRenderEnd;
  public NSNumber* LynxPipelineEntry::resolveStart resolveStart;
  public NSNumber* LynxPipelineEntry::resolveEnd resolveEnd;
  public NSNumber* LynxPipelineEntry::layoutStart layoutStart;
  public NSNumber* LynxPipelineEntry::layoutEnd layoutEnd;
  public NSNumber* LynxPipelineEntry::paintingUiOperationExecuteStart paintingUiOperationExecuteStart;
  public NSNumber* LynxPipelineEntry::paintingUiOperationExecuteEnd paintingUiOperationExecuteEnd;
  public NSNumber* LynxPipelineEntry::layoutUiOperationExecuteStart layoutUiOperationExecuteStart;
  public NSNumber* LynxPipelineEntry::layoutUiOperationExecuteEnd layoutUiOperationExecuteEnd;
  public NSNumber* LynxPipelineEntry::paintEnd paintEnd;
  public NSDictionary* LynxPipelineEntry::frameworkRenderingTiming frameworkRenderingTiming;
  public instancetype LynxPipelineEntry::initWithDictionary:(NSDictionary *dictionary);
}

public class LynxPipelineInfo : NSObject {
  public NSString* LynxPipelineInfo::url url;
  public NSInteger LynxPipelineInfo::pipelineOrigin pipelineOrigin;
  public nonnull instancetype LynxPipelineInfo::initWithUrl:(nullable NSString *url);
  public void LynxPipelineInfo::addPipelineOrigin:(NSInteger pipelineOrigin);
}

public class LynxPlatformLength : NSObject, <NSCopying> {
  public LynxPlatformLengthUnit LynxPlatformLength::type type;
  public CGFloat LynxPlatformLength::value value;
  public NSArray* LynxPlatformLength::calcArray calcArray;
  public instancetype LynxPlatformLength::initWithValue:type:(id value,[type] LynxPlatformLengthUnit type);
  public CGFloat LynxPlatformLength::valueWithParentValue:(CGFloat parentValue);
  public CGFloat LynxPlatformLength::numberValue();
}

public class LynxPropertyDiffMap : NSObject {
  public void LynxPropertyDiffMap::putValue:forKey:(id value,[forKey] NSString *key);
  public void LynxPropertyDiffMap::deleteKey:(NSString *key);
  public id _Nullable LynxPropertyDiffMap::getValueForKey:(NSString *key);
  public id _Nullable LynxPropertyDiffMap::getValueForKey:defaultValue:(NSString *key,[defaultValue] id _Nullable defaultValue);
  public id _Nullable LynxPropertyDiffMap::getUpdatedValueForKey:(NSString *key);
  public BOOL LynxPropertyDiffMap::valueChangedForKey:updateTo:(NSString *key,[updateTo] NSObject *_Nullable __strong *_Nonnull owner);
  public BOOL LynxPropertyDiffMap::isValueForKeyUpdated:(NSString *key);
  public NSSet< NSString * > * LynxPropertyDiffMap::getUpdatedKeys();
  public void LynxPropertyDiffMap::clearDirtyRecords();
}

public class LynxPropsProcessor : NSObject {
  public void LynxPropsProcessor::updateProp:withKey:forUI:(id value,[withKey] NSString *key,[forUI] LynxUI *ui);
  public void LynxPropsProcessor::updateProp:withKey:forShadowNode:(id value,[withKey] NSString *key,[forShadowNode] LynxShadowNode *shadowNode);
}

public class LynxProviderRegistry : NSObject {
  public void LynxProviderRegistry::addLynxResourceProvider:provider:(NSString *key,[provider] id< LynxResourceProvider > provider);
  public id< LynxResourceProvider > LynxProviderRegistry::getResourceProviderByKey:(NSString *key);
}

public class LynxRadialGradient : LynxGradient {
  public LynxRadialCenterType LynxRadialGradient::centerX centerX;
  public LynxRadialCenterType LynxRadialGradient::centerY centerY;
  public CGFloat LynxRadialGradient::centerXValue centerXValue;
  public CGFloat LynxRadialGradient::centerYValue centerYValue;
  public CGPoint LynxRadialGradient::at at;
  public LynxRadialGradientShapeType LynxRadialGradient::shape shape;
  public LynxRadialGradientSizeType LynxRadialGradient::shapeSize shapeSize;
  public CGFloat LynxRadialGradient::shapeSizeXValue shapeSizeXValue;
  public LynxPlatformLengthUnit LynxRadialGradient::shapeSizeXUnit shapeSizeXUnit;
  public CGFloat LynxRadialGradient::shapeSizeYValue shapeSizeYValue;
  public LynxPlatformLengthUnit LynxRadialGradient::shapeSizeYUnit shapeSizeYUnit;
  public instancetype LynxRadialGradient::initWithArray:(NSArray *arr);
  public CGPoint LynxRadialGradient::calculateCenterWithWidth:andHeight:(CGFloat width,[andHeight] CGFloat height);
  public CGPoint LynxRadialGradient::calculateRadiusWithCenter:sizeX:sizeY:(const CGPoint *_Nonnull center,[sizeX] CGFloat width,[sizeY] CGFloat height);
}

public class LynxRawTextShadowNode : LynxShadowNode {
  public NSString* LynxRawTextShadowNode::text text;
}

public class LynxRecorder : NSObject {
  public instancetype LynxRecorder::sharedInstance();
  public void LynxRecorder::recordUIEvent:withLynxView:(nullable UIEvent *event,[withLynxView] nullable LynxView *lynxView);
}

public protocol LynxResourceFetcher-p : <NSObject> {
  public dispatch_block_t LynxResourceFetcher-p::loadResourceWithURL:type:completion:(NSURL *url,[type] LynxFetchResType type,[completion] LynxResourceLoadCompletionBlock completionBlock);
  public dispatch_block_t LynxResourceFetcher-p::fetchResourceWithURL:type:completion:(NSURL *url,[type] LynxFetchResType type,[completion] LynxResourceLoadCompletionBlock completionBlock);
  public NSString * LynxResourceFetcher-p::translatedResourceWithId:theme:themeKey:view:(NSString *resId,[theme] LynxTheme *theme,[themeKey] NSString *key,[view] __weak LynxView *view);
  public NSString * LynxResourceFetcher-p::redirectURL:(NSString *urlString);
  public void LynxResourceFetcher-p::resolveResourceURL:completion:(NSString *url,[completion] LynxResourceResolveHandler completionBlock);
  public void LynxResourceFetcher-p::storeExtraModuleData:(id lynxModuleExtraData);
  public void LynxResourceFetcher-p::fetchResourceDataWithURLString:context:completionHandler:(NSString *urlString,[context] nullable NSDictionary *context,[completionHandler] LynxResourceCompletionHandler completionHandler);
  public void LynxResourceFetcher-p::fetchLocalFileWithURLString:context:completionHandler:(NSString *urlString,[context] nullable NSDictionary *context,[completionHandler] LynxLocalFileCompletionHandler completionHandler);
  public dispatch_block_t LynxResourceFetcher-p::loadResourceWithURL:delegate:(NSURL *url,[delegate] nonnull id< LynxResourceLoadDelegate > delegate);
  public void LynxResourceFetcher-p::loadResourceWithURLString:completion:(NSString *urlString,[completion] LynxResourceLoadCompletionBlock completionBlock);
  public _Nullable dispatch_block_t LynxResourceFetcher-p::requestAsyncWithResourceRequest:type:lynxResourceLoadCompletedBlock:(LynxResourceRequest *lynxResourceRequest,[type] LynxFetchResType type,[lynxResourceLoadCompletedBlock] LynxResourceLoadCompletedBlock loadCompletedBlock);
  public nonnull LynxResourceResponse * LynxResourceFetcher-p::requestSyncWithResourceRequest:type:(LynxResourceRequest *lynxResourceRequest,[type] LynxFetchResType type);
}

public protocol LynxResourceLoadDelegate-p : <NSObject> {
  public void LynxResourceLoadDelegate-p::onStart:(NSInteger contentLength);
  public void LynxResourceLoadDelegate-p::onData:(NSData *data);
  public void LynxResourceLoadDelegate-p::onEnd();
  public void LynxResourceLoadDelegate-p::onError:(NSString *msg);
}

public class LynxResourceModule : NSObject, <LynxContextModule> {
  public instancetype LynxResourceModule::initWithLynxContext:(LynxContext *context);
}

public protocol LynxResourceProvider-p : <NSObject> {
  public void LynxResourceProvider-p::request:onComplete:(LynxResourceRequest *request,[onComplete] LynxResourceLoadBlock callback);
  public void LynxResourceProvider-p::cancel:(LynxResourceRequest *request);
}

public class LynxResourceRequest : NSObject {
  public NSString* LynxResourceRequest::url url;
  public LynxResourceRequestType LynxResourceRequest::type type;
  public id LynxResourceRequest::requestParams requestParams;
  public LynxResourceRequestAsyncMode LynxResourceRequest::mode mode;
  public instancetype LynxResourceRequest::initWithUrl:(NSString *url);
  public instancetype LynxResourceRequest::initWithUrl:type:(NSString *url,[type] LynxResourceRequestType type);
  public instancetype LynxResourceRequest::initWithUrl:andRequestParams:(NSString *url,[andRequestParams] id requestParams);
  public LynxServiceResourceRequestParameters *_Nullable LynxResourceRequest::getLynxResourceServiceRequestParams();
}

public class LynxResourceResponse : NSObject {
  public id LynxResourceResponse::data data;
  public NSError* LynxResourceResponse::error error;
  public NSInteger LynxResourceResponse::code code;
  public instancetype LynxResourceResponse::initWithData:(id data);
  public instancetype LynxResourceResponse::initWithError:code:(NSError *error,[code] NSInteger code);
  public bool LynxResourceResponse::success();
}

public protocol LynxResourceResponseDataInfoProtocol-p : <NSObject> {
  public nullable NSData * LynxResourceResponseDataInfoProtocol-p::data();
  public BOOL LynxResourceResponseDataInfoProtocol-p::isSuccess();
}

public class LynxResourceServiceFetcher : NSObject, <LynxResourceFetcher> {
  public BOOL LynxResourceServiceFetcher::ensureLynxService();
}

public protocol LynxResourceStreamLoadDelegate-p : <NSObject> {
  public void LynxResourceStreamLoadDelegate-p::onStart:(NSInteger contentLength);
  public void LynxResourceStreamLoadDelegate-p::onData:(nullable NSData *data);
  public void LynxResourceStreamLoadDelegate-p::onEnd();
  public void LynxResourceStreamLoadDelegate-p::onError:(nullable NSString *msg);
}

public class LynxRootUI : LynxUI, <UIView>, <LUIBodyView> {
  public LynxView* LynxRootUI::lynxView lynxView;
  public UIView<LUIBodyView>* LynxRootUI::rootView rootView;
  public BOOL LynxRootUI::layoutAnimationRunning layoutAnimationRunning;
  public instancetype LynxRootUI::NS_UNAVAILABLE();
  public instancetype LynxRootUI::initWithView:(nullable UIView *NS_UNAVAILABLE);
  public instancetype LynxRootUI::initWithLynxView:(UIView< LUIBodyView > *lynxView);
  public void LynxRootUI::updateFrame:withPadding:border:margin:withLayoutAnimation:(CGRect frame,[withPadding] UIEdgeInsets padding,[border] UIEdgeInsets border,[margin] UIEdgeInsets margin,[withLayoutAnimation] BOOL with);
  public void LynxRootUI::updateFrame:withPadding:border:withLayoutAnimation:(CGRect frame,[withPadding] UIEdgeInsets padding,[border] UIEdgeInsets border,[withLayoutAnimation] BOOL with);
}

public class LynxRoute : NSObject {
  public NSString* LynxRoute::templateUrl templateUrl;
  public NSString* LynxRoute::routeName routeName;
  public NSDictionary* LynxRoute::param param;
  public instancetype LynxRoute::initWithUrl:param:(NSString *url,[param] NSDictionary *param);
  public instancetype LynxRoute::initWithUrl:routeName:param:(NSString *url,[routeName] NSString *routeName,[param] NSDictionary *param);
}

public protocol LynxRuntimeLifecycleListener-p : <NSObject> {
  public void LynxRuntimeLifecycleListener-p::onRuntimeAttach:(void *_Nonnull env);
  public void LynxRuntimeLifecycleListener-p::onRuntimeDetach();
}

public protocol LynxSchemaInterceptor-p : <NSObject> {
  public bool LynxSchemaInterceptor-p::intercept:withParam:(nonnull NSString *schema,[withParam] nonnull NSDictionary *param);
}

public class LynxScreenMetrics : NSObject {
  public CGSize LynxScreenMetrics::screenSize screenSize;
  public CGFloat LynxScreenMetrics::scale scale;
  public LynxScreenMetrics * LynxScreenMetrics::getDefaultLynxScreenMetrics();
  public instancetype LynxScreenMetrics::initWithScreenSize:scale:(CGSize screenSize,[scale] CGFloat scale);
  public void LynxScreenMetrics::setLynxScreenSize:(CGSize screenSize);
}

public class LynxScrollEventManager : NSObject {
  public instancetype LynxScrollEventManager::initWithContext:sign:eventSet:(LynxUIContext *context,[sign] NSInteger sign,[eventSet] NSDictionary *_Nullable eventSet);
  public void LynxScrollEventManager::sendScrollEvent:scrollView:(NSString *name,[scrollView] UIScrollView *scrollView);
  public void LynxScrollEventManager::sendScrollEvent:scrollView:detail:(NSString *name,[scrollView] UIScrollView *scrollView,[detail] NSDictionary *detail);
  public void LynxScrollEventManager::sendScrollEvent:scrollView:deltaX:deltaY:(NSString *name,[scrollView] UIScrollView *scrollView,[deltaX] CGFloat x,[deltaY] CGFloat y);
  public void LynxScrollEventManager::sendScrollEvent:scrollView:targetContentOffset:(NSString *name,[scrollView] UIScrollView *scrollView,[targetContentOffset] CGPoint targetContentOffset);
  public BOOL LynxScrollEventManager::eventBound:(NSString *name);
}

public class LynxScrollFluency : NSObject, <LynxScrollListener> {
  public void LynxScrollFluency::setEnabledBySampling:(LynxBooleanOption enabledBySampling);
  public void LynxScrollFluency::setPageConfigProbability:(CGFloat probability);
  public BOOL LynxScrollFluency::shouldSendAllScrollEvent();
}

public class LynxScrollInfo : NSObject, <NSCopying> {
  public UIView<LUIBodyView>* LynxScrollInfo::lynxView lynxView;
  public NSString* LynxScrollInfo::tagName tagName;
  public NSString* LynxScrollInfo::scrollMonitorTagName scrollMonitorTagName;
  public NSString* LynxScrollInfo::lynxViewUrl lynxViewUrl;
  public UIScrollView* LynxScrollInfo::scrollView scrollView;
  public SEL LynxScrollInfo::selector selector;
  public BOOL LynxScrollInfo::decelerate decelerate;
  public instancetype LynxScrollInfo::infoWithScrollView:tagName:scrollMonitorTagName:(UIScrollView *scrollView,[tagName] NSString *tagName,[scrollMonitorTagName] NSString *scrollMonitorTagName);
}

public protocol LynxScrollListener-p : <NSObject> {
  public void LynxScrollListener-p::scrollerDidScroll:(LynxScrollInfo *info);
  public void LynxScrollListener-p::scrollerWillBeginDragging:(LynxScrollInfo *info);
  public void LynxScrollListener-p::scrollerDidEndDragging:willDecelerate:(LynxScrollInfo *info,[willDecelerate] BOOL decelerate);
  public void LynxScrollListener-p::scrollerDidEndDecelerating:(LynxScrollInfo *info);
  public void LynxScrollListener-p::scrollerDidEndScrollingAnimation:(LynxScrollInfo *info);
}

public class LynxScrollView : UIScrollView {
  public BOOL LynxScrollView::forceCanScroll forceCanScroll;
  public Class LynxScrollView::blockGestureClass blockGestureClass;
  public NSInteger LynxScrollView::recognizedViewTag recognizedViewTag;
  public LynxUIScroller* LynxScrollView::weakUIScroller weakUIScroller;
  public BOOL LynxScrollView::duringGestureScroll duringGestureScroll;
  public BOOL LynxScrollView::gestureEnabled gestureEnabled;
  public BOOL LynxScrollView::increaseFrequencyWithGesture increaseFrequencyWithGesture;
  public LynxGestureConsumer* LynxScrollView::gestureConsumer gestureConsumer;
  public void LynxScrollView::updateContentSize();
}

public protocol LynxScrollViewUIDelegate-p : <NSObject> {
  public UIView< LynxBounceView > * LynxScrollViewUIDelegate-p::LynxBounceView:(UIScrollView *scrollView);
}

public protocol LynxServiceContainerProtocol-p : <NSObject> {
  public __kindof LynxView * LynxServiceContainerProtocol-p::getLynxView();
}

public protocol LynxServiceDevToolProtocol-p : <LynxServiceProtocol> {
  public id< LynxBaseInspectorOwner > LynxServiceDevToolProtocol-p::createInspectorOwnerWithLynxView:(LynxView *lynxView);
  public id< LynxBaseLogBoxProxy > LynxServiceDevToolProtocol-p::createLogBoxProxyWithLynxView:(LynxView *lynxView);
  public Class< LynxContextModule > LynxServiceDevToolProtocol-p::devtoolSetModuleClass();
  public Class< LynxContextModule > LynxServiceDevToolProtocol-p::devtoolWebSocketModuleClass();
  public Class< LynxContextModule > LynxServiceDevToolProtocol-p::devtoolTrailModuleClass();
  public nullable Class< LynxBaseInspectorOwner > LynxServiceDevToolProtocol-p::inspectorOwnerClass();
  public Class< LynxDebuggerProtocol > LynxServiceDevToolProtocol-p::debuggerBridgeClass();
  public id LynxServiceDevToolProtocol-p::devtoolEnvSharedInstance();
  public void LynxServiceDevToolProtocol-p::devtoolEnvPrepareWithConfig:(LynxConfig *lynxConfig);
  public void LynxServiceDevToolProtocol-p::devtoolEnvSetValue:forKey:(BOOL value,[forKey] NSString *key);
  public BOOL LynxServiceDevToolProtocol-p::devtoolEnvGetValue:withDefaultValue:(NSString *key,[withDefaultValue] BOOL value);
  public void LynxServiceDevToolProtocol-p::devtoolEnvSet:forGroup:(NSSet *newGroupValues,[forGroup] NSString *groupKey);
  public NSSet * LynxServiceDevToolProtocol-p::devtoolEnvGetGroup:(NSString *groupKey);
}

public protocol LynxServiceExtensionProtocol-p : <LynxServiceProtocol> {
  public void LynxServiceExtensionProtocol-p::onLynxEnvSetup();
  public void LynxServiceExtensionProtocol-p::onLynxViewSetup:group:config:(LynxContext *lynxContext,[group] LynxGroup *group,[config] LynxConfig *config);
  public void LynxServiceExtensionProtocol-p::onLynxViewDestroy();
}

public protocol LynxServiceHttpProtocol-p : <LynxServiceProtocol> {
  public void LynxServiceHttpProtocol-p::invokeWithRequest:callback:(LynxHttpRequest *request,[callback] LynxHttpCallback callback);
  public BOOL LynxServiceHttpProtocol-p::setHttpInterceptor:(id< LynxHttpInterceptor > interceptor);
}

public protocol LynxServiceI18nProtocol-p : <LynxServiceProtocol> {
  public void LynxServiceI18nProtocol-p::registerNapiEnv:(napi_env napiEnv);
}

public class LynxServiceInfo : NSObject {
  public LynxContext* LynxServiceInfo::context context;
  public NSDictionary* LynxServiceInfo::extra extra;
}

public protocol LynxServiceLogProtocol-p : <LynxServiceProtocol> {
  public void * LynxServiceLogProtocol-p::getWriteFunction();
}

public protocol LynxServiceModuleProtocol-p : <LynxServiceProtocol> {
  public void LynxServiceModuleProtocol-p::initGlobalProps:(LynxView *lynxView);
  public void LynxServiceModuleProtocol-p::clearModuleForDestroy:(LynxView *lynxView);
}

public protocol LynxServiceMonitorProtocol-p : <LynxServiceProtocol> {
  public void LynxServiceMonitorProtocol-p::reportTrailEvent:data:(NSString *event,[data] NSDictionary *data);
  public void LynxServiceMonitorProtocol-p::reportImageStatus:data:(NSString *event,[data] NSDictionary *data);
  public void LynxServiceMonitorProtocol-p::reportErrorGlobalContextTag:data:(LynxContextTagType type,[data] NSString *data);
  public void LynxServiceMonitorProtocol-p::reportResourceStatus:data:extra:(LynxView *__nonnull LynxView,[data] NSDictionary *__nonnull data,[extra] NSDictionary *__nullable extra);
}

public protocol LynxServiceProtocol-p : <NSObject> {
  public LynxServiceScope LynxServiceProtocol-p::serviceScope();
  public NSUInteger LynxServiceProtocol-p::serviceType();
  public NSString * LynxServiceProtocol-p::serviceBizID();
  public instancetype LynxServiceProtocol-p::sharedInstance();
}

public protocol LynxServiceResourceProtocol-p : <LynxServiceProtocol> {
  public nonnull id< LynxServiceResourceRequestOperationProtocol > LynxServiceResourceProtocol-p::fetchResourceAsync:parameters:completion:(NSString *url,[parameters] nullable LynxServiceResourceRequestParameters *parameters,[completion] nullable LynxServiceResourceCompletionHandler completionHandler);
  public nonnull id< LynxServiceResourceResponseProtocol > LynxServiceResourceProtocol-p::fetchResourceSync:parameters:error:(NSString *url,[parameters] nullable LynxServiceResourceRequestParameters *parameters,[error] NSError *_Nullable *errorPtr);
  public void LynxServiceResourceProtocol-p::preloadMedia:cacheKey:videoID:videoModel:resolution:encodeType:apiString:size:(NSString *url,[cacheKey] NSString *cacheKey,[videoID] nullable NSString *videoID,[videoModel] nullable NSDictionary *videoModel,[resolution] NSUInteger resolution,[encodeType] NSUInteger encodeType,[apiString] nullable NSString *apiString,[size] NSInteger size);
  public void LynxServiceResourceProtocol-p::cancelPreloadMedia:videoID:videoModel:(NSString *cacheKey,[videoID] nullable NSString *videoID,[videoModel] BOOL videoModel);
  public void LynxServiceResourceProtocol-p::addResourceLoader:TemplateUrl:(id loader,[TemplateUrl] NSString *templateUrl);
}

public protocol LynxServiceResourceRequestOperationProtocol-p : <NSObject> {
  public NSString* _Nullable LynxServiceResourceRequestOperationProtocol-p::url url;
  public BOOL LynxServiceResourceRequestOperationProtocol-p::cancel();
}

public class LynxServiceResourceRequestParameters : NSObject, <NSCopying> {
  public NSNumber* LynxServiceResourceRequestParameters::enableRequestReuse enableRequestReuse;
  public LynxServiceResourceScene LynxServiceResourceRequestParameters::resourceScene resourceScene;
  public NSString* LynxServiceResourceRequestParameters::templateUrl templateUrl;
}

public protocol LynxServiceResourceResponseProtocol-p : <LynxResourceResponseDataInfoProtocol> {
  public nullable NSData * LynxServiceResourceResponseProtocol-p::data();
  public BOOL LynxServiceResourceResponseProtocol-p::isSuccess();
}

public class LynxServices : NSObject {
  public void LynxServices::registerService:(Class cls);
  public id LynxServices::getInstanceWithProtocol:bizID:(Protocol *protocol,[bizID] NSString *__nullable bizID);
}

public protocol LynxServiceSecurityProtocol-p : <NSObjectLynxServiceProtocol> {
  public LynxVerificationResult * LynxServiceSecurityProtocol-p::verifyTASM:view:url:type:(NSData *data,[view] nullable LynxView *lynxView,[url] nullable NSString *url,[type] LynxTASMType type);
  public BOOL LynxServiceSecurityProtocol-p::onPiperInvoked:method:param:url:(NSString *module,[method] NSString *method,[param] NSString *param,[url] NSString *url);
}

public protocol LynxServiceSystemInvokeProtocol-p : <LynxServiceProtocol> {
  public void LynxServiceSystemInvokeProtocol-p::setString:(NSString *string);
  public void LynxServiceSystemInvokeProtocol-p::startDeviceMotionUpdates:toQueue:withHandler:(CMMotionManager *motionManager,[toQueue] NSOperationQueue *queue,[withHandler] void(^ handler)(CMDeviceMotion *__nullable motion, NSError *__nullable error));
  public void LynxServiceSystemInvokeProtocol-p::stopDeviceMotionUpdates:(CMMotionManager *motionManager);
  public void LynxServiceSystemInvokeProtocol-p::startCaptureSessionRunning:(AVCaptureSession *captureSession);
  public void LynxServiceSystemInvokeProtocol-p::stopCaptureSessionRunning:(AVCaptureSession *captureSession);
  public UIImage * LynxServiceSystemInvokeProtocol-p::takeScreenshot:withBackgroundColor:scale:(UIView *view,[withBackgroundColor] UIColor *color,[scale] float scale);
  public NSInteger LynxServiceSystemInvokeProtocol-p::startAudioOutputUnit:(void *audioUnitPtr);
  public NSInteger LynxServiceSystemInvokeProtocol-p::stopAudioOutputUnit:(void *audioUnitPtr);
}

public protocol LynxServiceTrailProtocol-p : <LynxServiceProtocol> {
  public NSString * LynxServiceTrailProtocol-p::stringValueForTrailKey:(NSString *key);
  public id LynxServiceTrailProtocol-p::objectValueForTrailKey:(NSString *key);
  public NSDictionary * LynxServiceTrailProtocol-p::getAllValues();
}

public class LynxSetModule : NSObject, <LynxContextModule> {
  public instancetype LynxSetModule::initWithLynxContext:(LynxContext *context);
  public void LynxSetModule::switchKeyBoardDetect:(BOOL arg);
  public BOOL LynxSetModule::getEnableLayoutOnly();
  public void LynxSetModule::switchEnableLayoutOnly:(BOOL arg);
  public BOOL LynxSetModule::getAutoResumeAnimation();
  public void LynxSetModule::setAutoResumeAnimation:(BOOL arg);
  public BOOL LynxSetModule::getEnableNewTransformOrigin();
  public void LynxSetModule::setEnableNewTransformOrigin:(BOOL arg);
  public NSMutableDictionary * LynxSetModule::setUpSettingsDict();
}

public class LynxShadowNode : LynxLayoutNode, <LynxShadowNode> {
  public LynxUIOwner* LynxShadowNode::uiOwner uiOwner;
  public LynxShadowNodeStyle* LynxShadowNode::shadowNodeStyle shadowNodeStyle;
  public BOOL LynxShadowNode::isDestroy isDestroy;
  public BOOL LynxShadowNode::needsEventSet needsEventSet;
  public enum LynxEventPropStatus LynxShadowNode::ignoreFocus ignoreFocus;
  public NSDictionary* LynxShadowNode::dataset dataset;
  public BOOL LynxShadowNode::enableTouchPseudoPropagation enableTouchPseudoPropagation;
  public enum LynxEventPropStatus LynxShadowNode::eventThrough eventThrough;
  public NSDictionary<NSString*, LynxEventSpec*>* LynxShadowNode::eventSet eventSet;
  public NSInteger LynxShadowNode(VirtualAnchor)::totalCountOfChildrenThatHaveUI totalCountOfChildrenThatHaveUI;
  public instancetype LynxShadowNode::initWithSign:tagName:(NSInteger sign,[tagName] NSString *tagName);
  public void LynxShadowNode::setUIOperation:(LynxUIOwner *owner);
  public void LynxShadowNode::setDelegate:(id< LynxShadowNodeDelegate > delegate);
  public void LynxShadowNode::postExtraDataToUI:(id value);
  public void LynxShadowNode::postFrameToUI:(CGRect frame);
  public void LynxShadowNode::destroy();
  public id LynxShadowNode::getExtraBundle();
  public void LynxShadowNode::setVerticalAlignOnShadowNode:value:(BOOL requestReset,[value] NSArray *value);
  public BOOL LynxShadowNode::isVirtual();
  public BOOL LynxShadowNode::supportInlineView();
  public void LynxShadowNode(VirtualAnchor)::adjustUIHierarchyWhileInsertNode:atIndex:(LynxShadowNode *child, [atIndex] NSInteger index);
  public void LynxShadowNode(VirtualAnchor)::adjustUIHierarchyWhileRemoveNode:atIndex:(LynxShadowNode *child, [atIndex] NSInteger index);
  public NSInteger LynxShadowNode(VirtualAnchor)::mapToUIIndexWithChild:(LynxShadowNode *child);
  public LynxShadowNode * LynxShadowNode(VirtualAnchor)::nodeThatHasUI();
}

public interface LynxShadowNode(VirtualAnchor) {
  public NSInteger LynxShadowNode(VirtualAnchor)::totalCountOfChildrenThatHaveUI totalCountOfChildrenThatHaveUI;
  public void LynxShadowNode(VirtualAnchor)::adjustUIHierarchyWhileInsertNode:atIndex:(LynxShadowNode *child,[atIndex] NSInteger index);
  public void LynxShadowNode(VirtualAnchor)::adjustUIHierarchyWhileRemoveNode:atIndex:(LynxShadowNode *child,[atIndex] NSInteger index);
  public NSInteger LynxShadowNode(VirtualAnchor)::mapToUIIndexWithChild:(LynxShadowNode *child);
  public LynxShadowNode * LynxShadowNode(VirtualAnchor)::nodeThatHasUI();
}

public protocol LynxShadowNodeDelegate-p : <NSObject> {
  public void LynxShadowNodeDelegate-p::updateExtraData:value:(NSInteger sign,[value] id value);
  public void LynxShadowNodeDelegate-p::updateLayout:layoutLeft:top:width:height:(NSInteger sign,[layoutLeft] CGFloat left,[top] CGFloat top,[width] CGFloat width,[height] CGFloat height);
  public void LynxShadowNodeDelegate-p::finishLayoutOperation();
}

public class LynxShadowNodeOwner : NSObject {
  public LynxLayoutTick* LynxShadowNodeOwner::layoutTick layoutTick;
  public LynxUIContext* LynxShadowNodeOwner::uiContext uiContext;
  public instancetype LynxShadowNodeOwner::NS_UNAVAILABLE();
  public instancetype LynxShadowNodeOwner::initWithUIOwner:layoutTick:isAsyncLayout:(LynxUIOwner *uiOwner,[layoutTick] nullable LynxLayoutTick *layoutTick,[isAsyncLayout] BOOL isAsyncLayout);
  public void LynxShadowNodeOwner::setDelegate:(id< LynxShadowNodeDelegate > delegate);
  public void LynxShadowNodeOwner::initLayoutNodeManager:(void *layoutNodeManagerPtr);
  public NSInteger LynxShadowNodeOwner::createNodeWithSign:tagName:props:eventSet:lepusEventSet:isParentInlineContainer:(NSInteger sign,[tagName] nonnull NSString *tagName,[props] nullable NSDictionary *props,[eventSet] nullable NSSet< NSString * > *eventSet,[lepusEventSet] nullable NSSet< NSString * > *lepusEventSet,[isParentInlineContainer] bool allowInline);
  public void LynxShadowNodeOwner::updateNodeWithSign:props:eventSet:lepusEventSet:(NSInteger sign,[props] nullable NSDictionary *props,[eventSet] nullable NSSet< NSString * > *eventSet,[lepusEventSet] nullable NSSet< NSString * > *lepusEventSet);
  public void LynxShadowNodeOwner::insertNode:toParent:atIndex:(NSInteger childSign,[toParent] NSInteger parentSign,[atIndex] NSInteger index);
  public void LynxShadowNodeOwner::removeNode:fromParent:atIndex:(NSInteger childSign,[fromParent] NSInteger parentSign,[atIndex] NSInteger index);
  public void LynxShadowNodeOwner::moveNode:inParent:fromIndex:toIndex:(NSInteger childSign,[inParent] NSInteger parentSign,[fromIndex] NSInteger from,[toIndex] NSInteger to);
  public void LynxShadowNodeOwner::destroyNode:(NSInteger sign);
  public void LynxShadowNodeOwner::didLayoutStartOnNode:(NSInteger sign);
  public void LynxShadowNodeOwner::didUpdateLayoutLeft:top:width:height:onNode:(CGFloat left,[top] CGFloat top,[width] CGFloat width,[height] CGFloat height,[onNode] NSInteger sign);
  public void LynxShadowNodeOwner::didLayoutFinished();
  public void LynxShadowNodeOwner::destroy();
  public void LynxShadowNodeOwner::destroySelf();
  public LynxShadowNode * LynxShadowNodeOwner::nodeWithSign:(NSInteger sign);
  public void LynxShadowNodeOwner::updateRootSize:height:(float width,[height] float height);
  public float LynxShadowNodeOwner::rootWidth();
  public float LynxShadowNodeOwner::rootHeight();
}

public class LynxShadowNodeStyle : NSObject {
  public LynxVerticalAlign LynxShadowNodeStyle::valign valign;
  public CGFloat LynxShadowNodeStyle::valignLength valignLength;
}

public class LynxSizeValue : NSObject {
  public LynxSizeValueType LynxSizeValue::type type;
  public CGFloat LynxSizeValue::value value;
  public nullable instancetype LynxSizeValue::sizeValueFromCSSString:(nullable NSString *valueStr);
  public instancetype LynxSizeValue::initWithType:value:(LynxSizeValueType type,[value] CGFloat value);
  public CGFloat LynxSizeValue::convertToDevicePtWithFullSize:(CGFloat fullSize);
}

public class LynxSSRHelper : NSObject {
  public void LynxSSRHelper::onLoadSSRDataBegan:(NSString *url);
  public void LynxSSRHelper::onHydrateBegan:(NSString *url);
  public void LynxSSRHelper::onHydrateFinished:(NSString *url);
  public void LynxSSRHelper::onErrorOccurred:sourceError:(NSInteger code,[sourceError] NSError *source);
  public BOOL LynxSSRHelper::isHydratePending();
  public BOOL LynxSSRHelper::shouldSendEventToSSR();
  public NSArray * LynxSSRHelper::processEventParams:(NSArray *params);
}

public class LynxSubErrorCodeMetaData : NSObject {
  public ELynxLevel LynxSubErrorCodeMetaData::level level;
  public NSString* LynxSubErrorCodeMetaData::fixSuggestion fixSuggestion;
  public NSArray* LynxSubErrorCodeMetaData::consumer consumer;
  public instancetype LynxSubErrorCodeMetaData::initWithLevel:fixSuggestion:consumer:(ELynxLevel level,[fixSuggestion] NSString *fixSuggestion,[consumer] NSArray *consumer);
}

public class LynxSubErrorCodeUtils : NSObject {
  public LynxSubErrorCodeMetaData * LynxSubErrorCodeUtils::getMetaData:(NSInteger subCode);
  public NSString * LynxSubErrorCodeUtils::levelToStr:(ELynxLevel level);
  public NSString * LynxSubErrorCodeUtils::consumerToStr:(ELynxConsumer consumer);
}

public class LynxTemplateBundle : NSObject {
  public NSString* LynxTemplateBundle::url url;
  public instancetype _Nullable LynxTemplateBundle::initWithTemplate:(nonnull NSData *tem);
  public instancetype _Nullable LynxTemplateBundle::initWithTemplate:option:(nonnull NSData *tem,[option] nullable LynxTemplateBundleOption *option);
  public NSString *_Nullable LynxTemplateBundle::errorMsg();
  public NSDictionary *_Nullable LynxTemplateBundle::extraInfo();
  public BOOL LynxTemplateBundle::isElementBundleValid();
  public void LynxTemplateBundle::postJsCacheGenerationTask:(nonnull NSString *bytecodeSourceUrl);
}

public class LynxTemplateBundleOption : NSObject {
  public int LynxTemplateBundleOption::contextPoolSize contextPoolSize;
  public BOOL LynxTemplateBundleOption::enableContextAutoRefill enableContextAutoRefill;
  public NSString* LynxTemplateBundleOption::url url;
}

public class LynxTemplateData : NSObject {
  public instancetype LynxTemplateData::NS_UNAVAILABLE();
  public instancetype LynxTemplateData::initWithJson:(NSString *json);
  public instancetype LynxTemplateData::initWithDictionary:(NSDictionary *dictionary);
  public instancetype LynxTemplateData::initWithJson:useBoolLiterals:(NSString *json,[useBoolLiterals] BOOL useBoolLiterals);
  public instancetype LynxTemplateData::initWithDictionary:useBoolLiterals:(NSDictionary *dictionary,[useBoolLiterals] BOOL useBoolLiterals);
  public void LynxTemplateData::updateWithJson:(NSString *json);
  public void LynxTemplateData::updateWithDictionary:(NSDictionary *dictionary);
  public void LynxTemplateData::setObject:withKey:(id object,[withKey](deprecated("Use updateObject:forKey: instead.")) __attribute__);
  public void LynxTemplateData::updateObject:forKey:(id object,[forKey] NSString *key);
  public void LynxTemplateData::updateBool:forKey:(BOOL value,[forKey] NSString *key);
  public void LynxTemplateData::updateInteger:forKey:(NSInteger value,[forKey] NSString *key);
  public void LynxTemplateData::updateDouble:forKey:(CGFloat value,[forKey] NSString *key);
  public void LynxTemplateData::updateWithTemplateData:(LynxTemplateData *value);
  public BOOL LynxTemplateData::checkIsLegalData();
  public NSDictionary * LynxTemplateData::dictionary();
  public void LynxTemplateData::markState:(NSString *name);
  public void LynxTemplateData::markReadOnly();
  public BOOL LynxTemplateData::isReadOnly();
  public LynxTemplateData * LynxTemplateData::deepClone();
  public NSArray * LynxTemplateData()::obtainUpdateActions();
  public NSArray * LynxTemplateData()::copyUpdateActions();
  public lynx::lepus::Value LynxTemplateData()::getDataForJSThread();
  public NSString* LynxTemplateData()::processorName processorName;
}

public protocol LynxTemplateProvider-p : <NSObject> {
  public void LynxTemplateProvider-p::loadTemplateWithUrl:onComplete:(NSString *url,[onComplete] LynxTemplateLoadBlock callback);
}

public class LynxTemplateRender : NSObject, <LynxTemplateRenderProtocol>, <TemplateRenderCallbackProtocol> {
  public LynxViewSizeMode LynxTemplateRender::layoutWidthMode layoutWidthMode;
  public LynxViewSizeMode LynxTemplateRender::layoutHeightMode layoutHeightMode;
  public CGFloat LynxTemplateRender::preferredMaxLayoutWidth preferredMaxLayoutWidth;
  public CGFloat LynxTemplateRender::preferredMaxLayoutHeight preferredMaxLayoutHeight;
  public CGFloat LynxTemplateRender::preferredLayoutWidth preferredLayoutWidth;
  public CGFloat LynxTemplateRender::preferredLayoutHeight preferredLayoutHeight;
  public CGRect LynxTemplateRender::frameOfLynxView frameOfLynxView;
  public BOOL LynxTemplateRender::isDestroyed isDestroyed;
  public BOOL LynxTemplateRender::hasRendered hasRendered;
  public NSString* LynxTemplateRender::url url;
  public BOOL LynxTemplateRender::enableJSRuntime enableJSRuntime;
  public LynxDevtool* LynxTemplateRender::devTool devTool;
  public BOOL LynxTemplateRender::enablePrePainting enablePrePainting;
  public BOOL LynxTemplateRender::enableDumpElement enableDumpElement;
  public BOOL LynxTemplateRender::enableRecycleTemplateBundle enableRecycleTemplateBundle;
  public NSMutableDictionary<NSString*, id>* LynxTemplateRender::lepusModulesClasses lepusModulesClasses;
  public id<LynxResourceFetcher> LynxTemplateRender::resourceFetcher resourceFetcher;
  public nullable id< LynxEventTarget > LynxTemplateRender::hitTestInEventHandler:withEvent:(CGPoint point,[withEvent] UIEvent *_Nonnull event);
  public void LynxTemplateRender::willMoveToWindow:(nonnull UIWindow *newWindow);
  public void LynxTemplateRender::setCustomizedLayoutInUIContext:(id< LynxListLayoutProtocol > _Nullable customizedListLayout);
  public void LynxTemplateRender::setScrollListener:(id< LynxScrollListener > _Nullable scrollListener);
  public void LynxTemplateRender::setImageFetcherInUIOwner:(id< LynxImageFetcher > _Nullable imageFetcher);
  public void LynxTemplateRender::setResourceFetcherInUIOwner:(id< LynxResourceFetcher > _Nullable resourceFetcher);
  public void LynxTemplateRender::setNeedPendingUIOperation:(BOOL needPendingUIOperation);
  public BOOL LynxTemplateRender::enableAirStrictMode();
  public BOOL LynxTemplateRender::enableLayoutOnly();
  public BOOL LynxTemplateRender::enableFiberArch();
  public BOOL LynxTemplateRender::enableBackgroundShapeLayer();
  public BOOL LynxTemplateRender::isLayoutFinish();
  public void LynxTemplateRender::resetLayoutStatus();
  public void LynxTemplateRender::preloadLazyBundles:(NSArray *_Nonnull urls);
  public BOOL LynxTemplateRender::registerLazyBundle:bundle:(nonnull NSString *url,[bundle] nonnull LynxTemplateBundle *bundle);
  public void LynxTemplateRender::setEnableUserBytecode:url:(BOOL enableUserBytecode,[url] nonnull NSString *url);
  public void LynxTemplateRender::syncFlush();
  public void LynxTemplateRender::attachEngineToUIThread();
  public void LynxTemplateRender::detachEngineFromUIThread();
  public nullable LynxUI * LynxTemplateRender::findUIBySign:(NSInteger sign);
  public nullable UIView * LynxTemplateRender::findViewWithName:(nonnull NSString *name);
  public nullable LynxUI * LynxTemplateRender::uiWithName:(nonnull NSString *name);
  public nullable LynxUI * LynxTemplateRender::uiWithIdSelector:(nonnull NSString *idSelector);
  public nullable UIView * LynxTemplateRender::viewWithIdSelector:(nonnull NSString *idSelector);
  public nullable UIView * LynxTemplateRender::viewWithName:(nonnull NSString *name);
  public void LynxTemplateRender::setImageDownsampling:(BOOL enableImageDownsampling);
  public BOOL LynxTemplateRender::enableImageDownsampling();
  public BOOL LynxTemplateRender::enableNewImage();
  public BOOL LynxTemplateRender::trailNewImage();
  public float LynxTemplateRender::rootWidth();
  public float LynxTemplateRender::rootHeight();
  public void LynxTemplateRender::attachLynxView:(LynxView *_Nonnull lynxView);
  public BOOL LynxTemplateRender::processRender:(LynxView *_Nonnull lynxView);
  public void LynxTemplateRender::processLayout:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxTemplateRender::processLayoutWithTemplateBundle:withURL:initData:(nonnull LynxTemplateBundle *bundle,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxTemplateRender::processLayoutWithSSRData:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxTemplateRender::setTemplateRenderDelegate:(LynxTemplateRenderDelegateExternal *_Nonnull delegate);
  public NSInteger LynxTemplateRender()::logBoxImageSizeWarningThreshold();
  public void LynxTemplateRender()::didMoveToWindow:(BOOL windowIsNil);
  public BOOL LynxTemplateRender()::enableNewListContainer();
  public void LynxTemplateRender()::runOnTasmThread:(dispatch_block_t task);
  public BOOL LynxTemplateRender()::onLynxEvent:(LynxEvent *event);
  public void LynxTemplateRender()::onErrorOccurred:(LynxError *error);
  public int32_t LynxTemplateRender()::instanceId();
  public LynxGestureArenaManager * LynxTemplateRender()::getGestureArenaManager();
  public LynxEngineProxy * LynxTemplateRender()::getEngineProxy();
  public void LynxTemplateRender()::markDirty();
  public id< LynxUIRendererProtocol > LynxTemplateRender()::lynxUIRenderer();
  public LynxUIOwner * LynxTemplateRender()::uiOwner();
  public void LynxTemplateRender()::setAttachLynxPageUICallback:(attachLynxPageUI callback);
  public lynx::piper::ModuleFactoryDarwin * LynxTemplateRender()::getModuleFactory();
}

public protocol LynxTemplateRenderDelegate-p : <NSObject> {
  public void LynxTemplateRenderDelegate-p::templateRenderOnDataUpdated:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRender:onPageChanged:(LynxTemplateRender *templateRender,[onPageChanged] BOOL isFirstScreen);
  public void LynxTemplateRenderDelegate-p::templateRenderOnTasmFinishByNative:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRender:onTemplateLoaded:(LynxTemplateRender *templateRender,[onTemplateLoaded] NSString *url);
  public void LynxTemplateRenderDelegate-p::templateRenderOnRuntimeReady:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRender:onReceiveFirstLoadPerf:(LynxTemplateRender *templateRender,[onReceiveFirstLoadPerf] LynxPerformance *perf);
  public void LynxTemplateRenderDelegate-p::templateRender:onUpdatePerf:(LynxTemplateRender *templateRender,[onUpdatePerf] LynxPerformance *perf);
  public void LynxTemplateRenderDelegate-p::templateRender:onReceiveDynamicComponentPerf:(LynxTemplateRender *templateRender,[onReceiveDynamicComponentPerf] NSDictionary *perf);
  public NSString * LynxTemplateRenderDelegate-p::templateRender:translatedResourceWithId:themeKey:(LynxTemplateRender *templateRender,[translatedResourceWithId] NSString *resId,[themeKey] NSString *key);
  public void LynxTemplateRenderDelegate-p::templateRender:didInvokeMethod:inModule:errorCode:(LynxTemplateRender *templateRender,[didInvokeMethod] NSString *method,[inModule] NSString *module,[errorCode] int code);
  public void LynxTemplateRenderDelegate-p::templateRender:onErrorOccurred:(LynxTemplateRender *templateRender,[onErrorOccurred] LynxError *error);
  public void LynxTemplateRenderDelegate-p::templateRenderOnResetViewAndLayer:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRenderOnTemplateStartLoading:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRenderOnFirstScreen:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRenderOnPageUpdate:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRenderOnDetach:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRender:onCallJSBFinished:(LynxTemplateRender *templateRender,[onCallJSBFinished] NSDictionary *info);
  public void LynxTemplateRenderDelegate-p::templateRender:onJSBInvoked:(LynxTemplateRender *templateRender,[onJSBInvoked] NSDictionary *info);
  public void LynxTemplateRenderDelegate-p::templateRender:onLynxEvent:(LynxTemplateRender *templateRender,[onLynxEvent] LynxEventDetail *event);
  public void LynxTemplateRenderDelegate-p::templateRenderSetLayoutOption:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRenderRequestNeedsLayout:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRenderOnTransitionUnregister:(LynxTemplateRender *templateRender);
  public void LynxTemplateRenderDelegate-p::templateRender:onTemplateBundleReady:(LynxTemplateRender *templateRender,[onTemplateBundleReady] LynxTemplateBundle *bundle);
  public void LynxTemplateRenderDelegate-p::templateRenderOnPageStarted:withPipelineInfo:(LynxTemplateRender *templateRender,[withPipelineInfo] LynxPipelineInfo *pipelineInfo);
}

public protocol LynxTemplateRenderProtocol-p : <NSObject> {
  public LynxViewSizeMode LynxTemplateRenderProtocol-p::layoutWidthMode layoutWidthMode;
  public LynxViewSizeMode LynxTemplateRenderProtocol-p::layoutHeightMode layoutHeightMode;
  public CGFloat LynxTemplateRenderProtocol-p::preferredMaxLayoutWidth preferredMaxLayoutWidth;
  public CGFloat LynxTemplateRenderProtocol-p::preferredMaxLayoutHeight preferredMaxLayoutHeight;
  public CGFloat LynxTemplateRenderProtocol-p::preferredLayoutWidth preferredLayoutWidth;
  public CGFloat LynxTemplateRenderProtocol-p::preferredLayoutHeight preferredLayoutHeight;
  public CGRect LynxTemplateRenderProtocol-p::frameOfLynxView frameOfLynxView;
  public BOOL LynxTemplateRenderProtocol-p::isDestroyed isDestroyed;
  public BOOL LynxTemplateRenderProtocol-p::hasRendered hasRendered;
  public NSString* LynxTemplateRenderProtocol-p::url url;
  public BOOL LynxTemplateRenderProtocol-p::enableJSRuntime enableJSRuntime;
  public NSMutableDictionary<NSString*, id>* LynxTemplateRenderProtocol-p::lepusModulesClasses lepusModulesClasses;
  public nonnull instancetype LynxTemplateRenderProtocol-p::initWithBuilderBlock:lynxView:(void(^_Nullable block)(NS_NOESCAPE LynxViewBuilder *_Nonnull),[lynxView] LynxView *_Nullable lynxView);
  public void LynxTemplateRenderProtocol-p::loadTemplateFromURL:initData:(NSString *_Nonnull url,[initData] LynxTemplateData *_Nullable data);
  public void LynxTemplateRenderProtocol-p::reset();
  public void LynxTemplateRenderProtocol-p::clearForDestroy();
  public void LynxTemplateRenderProtocol-p::loadTemplate:(nonnull LynxLoadMeta *meta);
  public void LynxTemplateRenderProtocol-p::loadTemplate:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::loadTemplateBundle:withURL:initData:(nonnull LynxTemplateBundle *bundle,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::loadTemplateWithoutLynxView:withURL:initData:(NSData *_Nonnull tem,[withURL] NSString *_Nonnull url,[initData] LynxTemplateData *_Nullable data);
  public void LynxTemplateRenderProtocol-p::updateMetaData:(nonnull LynxUpdateMeta *meta);
  public void LynxTemplateRenderProtocol-p::updateDataWithString:processorName:(nullable NSString *data,[processorName] nullable NSString *name);
  public void LynxTemplateRenderProtocol-p::updateDataWithDictionary:processorName:(nullable NSDictionary< NSString *, id > *data,[processorName] nullable NSString *name);
  public void LynxTemplateRenderProtocol-p::updateDataWithTemplateData:(nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::resetDataWithTemplateData:(nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::reloadTemplateWithTemplateData:globalProps:(nullable LynxTemplateData *data,[globalProps] nullable LynxTemplateData *globalProps);
  public void LynxTemplateRenderProtocol-p::loadSSRData:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::loadSSRDataFromURL:initData:(nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::ssrHydrate:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::ssrHydrateFromURL:initData:(nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::setSessionStorageItem:WithTemplateData:(nonnull NSString *key,[WithTemplateData] nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::getSessionStorageItem:withCallback:(nonnull NSString *key,[withCallback] void(^_Nonnull callback)(id< NSObject > _Nullable));
  public double LynxTemplateRenderProtocol-p::subscribeSessionStorage:withCallback:(nonnull NSString *key,[withCallback] void(^_Nonnull callback)(id< NSObject > _Nullable));
  public void LynxTemplateRenderProtocol-p::unSubscribeSessionStorage:withId:(nonnull NSString *key,[withId] double callbackId);
  public void LynxTemplateRenderProtocol-p::updateGlobalPropsWithDictionary:(nullable NSDictionary< NSString *, id > *data);
  public void LynxTemplateRenderProtocol-p::updateGlobalPropsWithTemplateData:(nullable LynxTemplateData *data);
  public void LynxTemplateRenderProtocol-p::sendGlobalEvent:withParams:(nonnull NSString *name,[withParams] nullable NSArray *params);
  public void LynxTemplateRenderProtocol-p::sendGlobalEventToLepus:withParams:(nonnull NSString *name,[withParams] nullable NSArray *params);
  public void LynxTemplateRenderProtocol-p::triggerEventBus:withParams:(nonnull NSString *name,[withParams] nullable NSArray *params);
  public void LynxTemplateRenderProtocol-p::onEnterForeground();
  public void LynxTemplateRenderProtocol-p::onEnterBackground();
  public void LynxTemplateRenderProtocol-p::onLongPress();
  public void LynxTemplateRenderProtocol-p::triggerLayout();
  public void LynxTemplateRenderProtocol-p::triggerLayoutInTick();
  public void LynxTemplateRenderProtocol-p::updateViewport();
  public void LynxTemplateRenderProtocol-p::updateViewport:(BOOL needLayout);
  public void LynxTemplateRenderProtocol-p::updateScreenMetricsWithWidth:height:(CGFloat width,[height] CGFloat height);
  public void LynxTemplateRenderProtocol-p::updateFontScale:(CGFloat scale);
  public void LynxTemplateRenderProtocol-p::pauseRootLayoutAnimation();
  public void LynxTemplateRenderProtocol-p::resumeRootLayoutAnimation();
  public void LynxTemplateRenderProtocol-p::restartAnimation();
  public void LynxTemplateRenderProtocol-p::resetAnimation();
  public void LynxTemplateRenderProtocol-p::setTheme:(LynxTheme *_Nullable theme);
  public void LynxTemplateRenderProtocol-p::setLocalTheme:(LynxTheme *_Nonnull theme);
  public nullable LynxTheme * LynxTemplateRenderProtocol-p::theme();
  public void LynxTemplateRenderProtocol-p::registerModule:param:(Class< LynxModule > _Nonnull module,[param] id _Nullable param);
  public BOOL LynxTemplateRenderProtocol-p::isModuleExist:(NSString *_Nonnull moduleName);
  public nullable JSModule * LynxTemplateRenderProtocol-p::getJSModule:(nonnull NSString *name);
  public void LynxTemplateRenderProtocol-p::setEnableAsyncDisplay:(BOOL enableAsyncDisplay);
  public BOOL LynxTemplateRenderProtocol-p::enableAsyncDisplay();
  public BOOL LynxTemplateRenderProtocol-p::enableTextNonContiguousLayout();
  public nonnull LynxContext * LynxTemplateRenderProtocol-p::getLynxContext();
  public LynxThreadStrategyForRender LynxTemplateRenderProtocol-p::getThreadStrategyForRender();
  public nonnull NSDictionary * LynxTemplateRenderProtocol-p::getCurrentData();
  public nonnull NSDictionary * LynxTemplateRenderProtocol-p::getPageDataByKey:(nonnull NSArray *keys);
  public NSString *_Nonnull LynxTemplateRenderProtocol-p::cardVersion();
  public nonnull NSDictionary * LynxTemplateRenderProtocol-p::getAllJsSource();
  public nullable NSNumber * LynxTemplateRenderProtocol-p::getLynxRuntimeId();
  public void LynxTemplateRenderProtocol-p::onErrorOccurred:message:(NSInteger errCode,[message] NSString *_Nonnull errMessage);
  public void LynxTemplateRenderProtocol-p::onErrorOccurred:sourceError:(NSInteger errCode,[sourceError] NSError *_Nonnull source);
  public void LynxTemplateRenderProtocol-p::setLongTaskMonitorEnabled:(LynxBooleanOption enabled);
  public void LynxTemplateRenderProtocol-p::setExtraTiming:(LynxExtraTiming *_Nonnull timing);
  public void LynxTemplateRenderProtocol-p::setFluencyTracerEnabled:(LynxBooleanOption enabled);
  public void LynxTemplateRenderProtocol-p::putExtraParamsForReportingEvents:(NSDictionary< NSString *, id > *_Nonnull params);
  public nullable NSDictionary * LynxTemplateRenderProtocol-p::getAllTimingInfo();
  public nullable NSDictionary * LynxTemplateRenderProtocol-p::getExtraInfo();
  public void LynxTemplateRenderProtocol-p::notifyIntersectionObservers();
  public void LynxTemplateRenderProtocol-p::startLynxRuntime();
}

public class LynxTemplateResource : NSObject {
  public NSData* _Nonnull LynxTemplateResource::data data;
  public LynxTemplateBundle* _Nonnull LynxTemplateResource::bundle bundle;
  public instancetype LynxTemplateResource::NS_UNAVAILABLE();
  public instancetype LynxTemplateResource::initWithNSData:(NSData *_Nonnull data);
  public instancetype LynxTemplateResource::initWithBundle:(LynxTemplateBundle *_Nonnull bundle);
}

public protocol LynxTemplateResourceFetcher-p : <NSObject> {
  public void LynxTemplateResourceFetcher-p::fetchTemplate:onComplete:(LynxResourceRequest *_Nonnull request,[onComplete] LynxTemplateResourceCompletionBlock _Nonnull callback);
  public void LynxTemplateResourceFetcher-p::fetchSSRData:onComplete:(LynxResourceRequest *_Nonnull request,[onComplete] LynxSSRResourceCompletionBlock _Nonnull callback);
}

public class LynxTextAttachmentInfo : NSObject {
  public NSInteger LynxTextAttachmentInfo::sign sign;
  public CGRect LynxTextAttachmentInfo::frame frame;
  public BOOL LynxTextAttachmentInfo::nativeAttachment nativeAttachment;
  public instancetype LynxTextAttachmentInfo::initWithSign:andFrame:(NSInteger sign,[andFrame] CGRect frame);
}

public class LynxTextInfoModule : NSObject, <LynxContextModule> {
  public NSDictionary * LynxTextInfoModule::getTextInfo:params:(NSString *text,[params] NSDictionary *params);
}

public class LynxTextLayoutManager : NSLayoutManager {
  public CGPoint LynxTextLayoutManager::overflowOffset overflowOffset;
  public CGSize LynxTextLayoutManager::textBoundingRectSize textBoundingRectSize;
  public NSInteger LynxTextLayoutManager::glyphCount glyphCount;
  public CGPoint LynxTextLayoutManager::preEndPosition preEndPosition;
  public NSRange LynxTextLayoutManager::preDrawableRange preDrawableRange;
}

public class LynxTextOverflowLayer : CALayer {
  public LynxTextView* LynxTextOverflowLayer::view view;
  public instancetype LynxTextOverflowLayer::init();
  public instancetype LynxTextOverflowLayer::initWithView:(nullable LynxTextView *view);
}

public class LynxTextRenderer : NSObject {
  public NSAttributedString* LynxTextRenderer::attrStr attrStr;
  public NSAttributedString* LynxTextRenderer::truncationToken truncationToken;
  public LynxLayoutSpec* LynxTextRenderer::layoutSpec layoutSpec;
  public NSLayoutManager* LynxTextRenderer::layoutManager layoutManager;
  public NSTextStorage* LynxTextRenderer::textStorage textStorage;
  public NSArray<LynxEventTargetSpan *>* LynxTextRenderer::subSpan subSpan;
  public NSArray<LynxTextAttachmentInfo *>* LynxTextRenderer::attachments attachments;
  public CGFloat LynxTextRenderer::baseline baseline;
  public NSInteger LynxTextRenderer::ellipsisCount ellipsisCount;
  public void(^ LynxTextRenderer::layoutTruncationBlock) (NSMutableAttributedString *) layoutTruncationBlock;
  public instancetype LynxTextRenderer::initWithAttributedString:layoutSpec:(NSAttributedString *attrStr,[layoutSpec] LynxLayoutSpec *spec);
  public CGSize LynxTextRenderer::size();
  public CGSize LynxTextRenderer::textsize();
  public CGFloat LynxTextRenderer::maxFontSize();
  public CGFloat LynxTextRenderer::textContentOffsetX();
  public void LynxTextRenderer::drawRect:padding:border:(CGRect bounds,[padding] UIEdgeInsets padding,[border] UIEdgeInsets border);
  public void LynxTextRenderer::genSubSpan();
  public void LynxTextRenderer::ensureTextRenderLayout();
}

public class LynxTextRendererCache : NSObject, <NSCacheDelegate> {
  public instancetype LynxTextRendererCache::cache();
  public instancetype LynxTextRendererCache::NS_UNAVAILABLE();
  public LynxTextRenderer * LynxTextRendererCache::rendererWithString:layoutSpec:(NSAttributedString *str,[layoutSpec] LynxLayoutSpec *spec);
  public void LynxTextRendererCache::clearCache();
}

public class LynxTextStyle : NSObject, <NSCopying> {
  public CGFloat LynxTextStyle::fontSize fontSize;
  public CGFloat LynxTextStyle::lineHeight lineHeight;
  public CGFloat LynxTextStyle::lineSpacing lineSpacing;
  public CGFloat LynxTextStyle::letterSpacing letterSpacing;
  public NSTextAlignment LynxTextStyle::textAlignment textAlignment;
  public NSTextAlignment LynxTextStyle::usedParagraphTextAlignment usedParagraphTextAlignment;
  public NSWritingDirection LynxTextStyle::direction direction;
  public CGFloat LynxTextStyle::fontWeight fontWeight;
  public LynxFontStyleType LynxTextStyle::fontStyle fontStyle;
  public UIColor* LynxTextStyle::foregroundColor foregroundColor;
  public UIColor* LynxTextStyle::backgroundColor backgroundColor;
  public NSString* LynxTextStyle::fontFamilyName fontFamilyName;
  public NSString* LynxTextStyle::underLine underLine;
  public NSString* LynxTextStyle::lineThrough lineThrough;
  public NSInteger LynxTextStyle::textDecorationStyle textDecorationStyle;
  public UIColor* LynxTextStyle::textDecorationColor textDecorationColor;
  public NSShadow* LynxTextStyle::textShadow textShadow;
  public LynxGradient* LynxTextStyle::textGradient textGradient;
  public LynxWordBreakType LynxTextStyle::wordBreak wordBreak;
  public NSMutableArray* LynxTextStyle::backgroundDrawable backgroundDrawable;
  public NSMutableArray* LynxTextStyle::backgroundPosition backgroundPosition;
  public NSMutableArray* LynxTextStyle::backgroundRepeat backgroundRepeat;
  public NSMutableArray* LynxTextStyle::backgroundImageSize backgroundImageSize;
  public LynxBorderRadii LynxTextStyle::borderRadius borderRadius;
  public BOOL LynxTextStyle::enableFontScaling enableFontScaling;
  public BOOL LynxTextStyle::textFakeBold textFakeBold;
  public BOOL LynxTextStyle::enableLanguageAlignment enableLanguageAlignment;
  public CGFloat LynxTextStyle::textIndent textIndent;
  public CGFloat LynxTextStyle::textStrokeWidth textStrokeWidth;
  public UIColor* LynxTextStyle::textStrokeColor textStrokeColor;
  public BOOL LynxTextStyle::isAutoFontSize isAutoFontSize;
  public CGFloat LynxTextStyle::autoFontSizeMaxSize autoFontSizeMaxSize;
  public CGFloat LynxTextStyle::autoFontSizeMinSize autoFontSizeMinSize;
  public CGFloat LynxTextStyle::autoFontSizeStepGranularity autoFontSizeStepGranularity;
  public NSArray* LynxTextStyle::autoFontSizePresetSizes autoFontSizePresetSizes;
  public BOOL LynxTextStyle::hyphen hyphen;
  public NSAttributedString* LynxTextStyle::truncationAttributedStr truncationAttributedStr;
  public void LynxTextStyle::updateBackgroundDrawableRepeat();
  public void LynxTextStyle::updateBackgroundDrawablePosition();
  public void LynxTextStyle::updateBackgroundDrawableSize();
  public void LynxTextStyle::updateBackgroundRadius();
  public NSDictionary< NSAttributedStringKey, id > * LynxTextStyle::toAttributesWithFontFaceContext:withFontFaceObserver:(LynxFontFaceContext *fontFaceContext,[withFontFaceObserver] id< LynxFontFaceObserver > _Nullable observer);
  public NSParagraphStyle * LynxTextStyle::genParagraphStyle();
  public UIFont * LynxTextStyle::fontWithFontFaceContext:fontFaceObserver:(LynxFontFaceContext *fontFaceContext,[fontFaceObserver] id< LynxFontFaceObserver > observer);
  public void LynxTextStyle::applyTextStyle:(LynxTextStyle *textStyle);
  public void LynxTextStyle::setTextAlignment:(NSTextAlignment textAlignment);
}

public class LynxTextUtils : NSObject {
  public NSTextAlignment LynxTextUtils::applyNaturalAlignmentAccordingToTextLanguage:refactor:(nonnull NSMutableAttributedString *attrString,[refactor] BOOL enableRefactor);
  public nonnull NSString * LynxTextUtils::getEllpsisStringAccordingToWritingDirection:(NSWritingDirection direction);
  public NSDictionary *_Nonnull LynxTextUtils::measureText:fontSize:fontFamily:maxWidth:maxLine:(NSString *_Nullable text,[fontSize] CGFloat fontSize,[fontFamily] NSString *_Nullable fontFamily,[maxWidth] CGFloat maxWidth,[maxLine] NSInteger maxLine);
  public UIFontWeight LynxTextUtils::convertLynxFontWeight:(NSUInteger fontWeight);
  public NSString *_Nonnull LynxTextUtils::ConvertRawText:(id _Nullable rawText);
}

public class LynxTextView : UIView {
  public CALayer* LynxTextView::contentLayer contentLayer;
  public LynxUIText* LynxTextView::ui ui;
  public LynxTextRenderer* LynxTextView::textRenderer textRenderer;
  public UIEdgeInsets LynxTextView::border border;
  public UIEdgeInsets LynxTextView::padding padding;
  public BOOL LynxTextView::enableTextSelection enableTextSelection;
  public BOOL LynxTextView::enableCustomContextMenu enableCustomContextMenu;
  public BOOL LynxTextView::enableCustomTextSelection enableCustomTextSelection;
  public void(^ LynxTextView::selectionChangeEventCallback) (NSDictionary *) selectionChangeEventCallback;
  public void LynxTextView::updateSelectionColor:(UIColor *color);
  public void LynxTextView::updateHandleColor:(UIColor *color);
  public void LynxTextView::updateHandleSize:(CGFloat size);
  public void LynxTextView::setOverflowOffset:(CGPoint overflowOffset);
  public void LynxTextView::initSelectionGesture();
  public NSArray * LynxTextView::getTextBoundingBoxes:withEnd:(NSInteger start,[withEnd] NSInteger end);
  public NSString * LynxTextView::getSelectedText();
  public NSArray * LynxTextView::setTextSelection:startY:endX:endY:showStartHandle:showEndHandle:(CGFloat startX,[startY] CGFloat startY,[endX] CGFloat endX,[endY] CGFloat endY,[showStartHandle] BOOL showStartHandle,[showEndHandle] BOOL showEndHandle);
  public NSArray * LynxTextView::getHandlesInfo();
}

public class LynxTheme : NSObject {
  public NSDictionary* LynxTheme::themeConfig themeConfig;
  public BOOL LynxTheme::updateValue:forKey:(nullable NSString *value,[forKey] nonnull NSString *key);
  public nullable NSString * LynxTheme::valueForKey:(nonnull NSString *key);
  public nullable NSArray< NSString * > * LynxTheme::allKeys();
}

public class LynxThreadManager : NSObject {
  public void LynxThreadManager::createIOSThread:runnable:(NSString *name,[runnable] dispatch_block_t runnable);
  public BOOL LynxThreadManager::isMainQueue();
  public void LynxThreadManager::runBlockInMainQueue:(dispatch_block_t _Nonnull runnable);
  public void LynxThreadManager::runInTargetQueue:runnable:(dispatch_queue_t queue,[runnable] dispatch_block_t runnable);
  public dispatch_queue_t LynxThreadManager::getCachedQueueWithPrefix:(NSString *_Nonnull identifier);
}

public protocol LynxTimingListener-p : <NSObject> {
  public void LynxTimingListener-p::lynxView:onSetup:(LynxView *lynxView,[onSetup] NSDictionary *info);
  public void LynxTimingListener-p::lynxView:onUpdate:timing:(LynxView *lynxView,[onUpdate] NSDictionary *info,[timing] NSDictionary *updateTiming);
}

public class LynxTouchEvent : LynxEvent {
  public CGPoint LynxTouchEvent::clientPoint clientPoint;
  public CGPoint LynxTouchEvent::pagePoint pagePoint;
  public CGPoint LynxTouchEvent::viewPoint viewPoint;
  public BOOL LynxTouchEvent::isMultiTouch isMultiTouch;
  public NSDictionary* LynxTouchEvent::touchMap touchMap;
  public NSMutableDictionary* LynxTouchEvent::uiTouchMap uiTouchMap;
  public NSMutableDictionary<NSString *, id<LynxEventTargetBase> >* LynxTouchEvent::activeUIMap activeUIMap;
  public instancetype LynxTouchEvent::initWithName:targetTag:(NSString *name,[targetTag] NSInteger target);
  public instancetype LynxTouchEvent::initWithName:targetTag:touchPoint:(NSString *name,[targetTag] NSInteger target,[touchPoint] CGPoint point);
  public instancetype LynxTouchEvent::initWithName:targetTag:clientPoint:pagePoint:viewPoint:(NSString *name,[targetTag] NSInteger tag,[clientPoint] CGPoint clientPoint,[pagePoint] CGPoint pagePoint,[viewPoint] CGPoint viewPoint);
  public instancetype LynxTouchEvent::initWithName:targetTag:touchMap:(NSString *name,[targetTag] NSInteger tag,[touchMap] NSDictionary *touchMap);
  public instancetype LynxTouchEvent::initWithName:uiTouchMap:(NSString *name,[uiTouchMap] NSMutableDictionary *uiTouchMap);
}

public class LynxTouchHandler : UIGestureRecognizer {
  public _Nullable id<LynxEventTarget> LynxTouchHandler::target target;
  public _Nullable id<LynxEventTarget> LynxTouchHandler::preTarget preTarget;
  public NSMutableArray<LynxWeakProxy *>* LynxTouchHandler()::touchDeque touchDeque;
  public int32_t LynxTouchHandler()::tapSlop tapSlop;
  public BOOL LynxTouchHandler()::hasMultiTouch hasMultiTouch;
  public LynxGestureArenaManager* _Nullable LynxTouchHandler()::gestureArenaManager gestureArenaManager;
  public BOOL LynxTouchHandler()::enableNewGesture enableNewGesture;
  public instancetype LynxTouchHandler::initWithEventHandler:(LynxEventHandler *eventHandler);
  public void LynxTouchHandler::onGestureRecognized();
  public void LynxTouchHandler::touchesBeganInner:withEvent:(NSSet< UITouch * > *touches,[withEvent] UIEvent *event);
  public void LynxTouchHandler::touchesMovedInner:withEvent:(NSSet< UITouch * > *touches,[withEvent] UIEvent *event);
  public void LynxTouchHandler::touchesEndedInner:withEvent:(NSSet< UITouch * > *touches,[withEvent] UIEvent *event);
  public void LynxTouchHandler::touchesCancelledInner:withEvent:(NSSet< UITouch * > *touches,[withEvent] UIEvent *event);
  public void LynxTouchHandler()::setupVelocityTracker:(UIView *rootView);
  public void LynxTouchHandler()::setEnableTouchRefactor:(BOOL enable);
  public void LynxTouchHandler()::setEnableEndGestureAtLastFingerUp:(BOOL enable);
  public void LynxTouchHandler()::setEnableTouchPseudo:(BOOL enable);
  public void LynxTouchHandler()::setEnableMultiTouch:(BOOL enable);
  public BOOL LynxTouchHandler()::isEnableAndGetMultiTouch();
  public BOOL LynxTouchHandler()::isTouchMoving();
  public BOOL LynxTouchHandler()::checkOuterGestureChanged:(NSSet< UITouch * > *touches);
  public NSInteger LynxTouchHandler()::setGestureArenaManagerAndGetIndex:(LynxGestureArenaManager *gestureArenaManager);
  public void LynxTouchHandler()::removeGestureArenaManager:(NSInteger index);
  public void LynxTouchHandler()::showMessageOnConsole:withLevel:(NSString *msg, [withLevel] int32_t level);
  public void LynxTouchHandler()::resetTouchEnv();
}

public class LynxTransformOriginRaw : NSObject {
  public float LynxTransformOriginRaw::p0 p0;
  public float LynxTransformOriginRaw::p1 p1;
  public bool LynxTransformOriginRaw::isValid();
  public bool LynxTransformOriginRaw::isP0Valid();
  public bool LynxTransformOriginRaw::isP1Valid();
  public bool LynxTransformOriginRaw::isP0Percent();
  public bool LynxTransformOriginRaw::isP1Percent();
  public bool LynxTransformOriginRaw::hasPercent();
  public bool LynxTransformOriginRaw::isDefault();
  public LynxTransformOriginRaw * LynxTransformOriginRaw::convertToLynxTransformOriginRaw:(id arr);
}

public class LynxTransformRaw : NSObject {
  public float LynxTransformRaw::p0 p0;
  public float LynxTransformRaw::p1 p1;
  public float LynxTransformRaw::p2 p2;
  public LynxPlatformLength* LynxTransformRaw::platformLengthP0 platformLengthP0;
  public LynxPlatformLength* LynxTransformRaw::platformLengthP1 platformLengthP1;
  public LynxPlatformLength* LynxTransformRaw::platformLengthP2 platformLengthP2;
  public int LynxTransformRaw::type type;
  public CATransform3D LynxTransformRaw::transformMatrix transformMatrix;
  public instancetype LynxTransformRaw::initWithArray:(NSArray *arr);
  public bool LynxTransformRaw::isP0Percent();
  public bool LynxTransformRaw::isP1Percent();
  public bool LynxTransformRaw::isP2Percent();
  public bool LynxTransformRaw::isRotate();
  public bool LynxTransformRaw::isRotateXY();
  public bool LynxTransformRaw::isMatrix();
  public void LynxTransformRaw::initTransformMatrixWithArray:(NSArray *array);
  public NSArray< LynxTransformRaw * > * LynxTransformRaw::toTransformRaw:(NSArray *arr);
  public bool LynxTransformRaw::hasPercent:(NSArray< LynxTransformRaw * > *arr);
  public bool LynxTransformRaw::hasScaleOrRotate:(NSArray< LynxTransformRaw * > *arr);
  public CGFloat LynxTransformRaw::getRotateZRad:(NSArray< LynxTransformRaw * > *arr);
  public CGFloat LynxTransformRaw::getTranslateX:(NSArray< LynxTransformRaw * > *arr);
  public CGFloat LynxTransformRaw::getTranslateY:(NSArray< LynxTransformRaw * > *arr);
}

public class LynxTransitionAnimationManager : NSObject {
  public instancetype LynxTransitionAnimationManager::initWithLynxUI:(LynxUI *ui);
  public void LynxTransitionAnimationManager::assembleTransitions:(NSArray *params);
  public void LynxTransitionAnimationManager::removeTransitionAnimation:(LynxAnimationProp prop);
  public void LynxTransitionAnimationManager::removeAllTransitionAnimation();
  public void LynxTransitionAnimationManager::removeAllLayoutTransitionAnimation();
  public BOOL LynxTransitionAnimationManager::maybeUpdateBackgroundWithTransitionAnimation:(UIColor *color);
  public BOOL LynxTransitionAnimationManager::maybeUpdateOpacityWithTransitionAnimation:(CGFloat opacity);
  public BOOL LynxTransitionAnimationManager::maybeUpdateFrameWithTransitionAnimation:withPadding:border:margin:(CGRect newFrame,[withPadding] UIEdgeInsets padding,[border] UIEdgeInsets border,[margin] UIEdgeInsets margin);
  public void LynxTransitionAnimationManager::performTransitionAnimationsWithBackground:callback:(UIColor *color,[callback] CompletionBlock block);
  public void LynxTransitionAnimationManager::performTransitionAnimationsWithOpacity:callback:(CGFloat newOpacity,[callback] CompletionBlock block);
  public void LynxTransitionAnimationManager::performTransitionAnimationsWithVisibility:callback:(BOOL show,[callback] CompletionBlock block);
  public void LynxTransitionAnimationManager::performTransitionAnimationsWithTransform:transformWithoutRotate:transformWithoutRotateXY:rotation:callback:(CATransform3D newTransform,[transformWithoutRotate] CATransform3D newTransformWithoutRotate,[transformWithoutRotateXY] CATransform3D newTransformWithoutRotateXY,[rotation] LynxAnimationTransformRotation *newRotation,[callback] CompletionBlock block);
  public BOOL LynxTransitionAnimationManager::isTransitionBackgroundColor();
  public BOOL LynxTransitionAnimationManager::isTransitionOpacity:newOpacity:(CGFloat oldOpacity,[newOpacity] CGFloat newOpacity);
  public BOOL LynxTransitionAnimationManager::isTransitionVisibility:newState:(BOOL oldState,[newState] BOOL newState);
  public BOOL LynxTransitionAnimationManager::isTransitionTransform:newTransform:(CATransform3D oldTransform,[newTransform] CATransform3D newTransform);
  public BOOL LynxTransitionAnimationManager::isTransitionTransformRotation:newTransformRotation:(LynxAnimationTransformRotation *oldTransformRotation,[newTransformRotation] LynxAnimationTransformRotation *newTransformRotation);
  public void LynxTransitionAnimationManager::applyTransitionAnimation();
}

public class LynxUI : <__covariant V UIView> {
  public LynxNewGestureDelegate NSInteger LynxUI::sign sign;
  public NSString* LynxUI::name name;
  public NSString* LynxUI::idSelector idSelector;
  public NSString* LynxUI::refId refId;
  public NSDictionary* LynxUI::dataset dataset;
  public NSString* LynxUI::tagName tagName;
  public LynxBasicShape* LynxUI::clipPath clipPath;
  public LynxBasicShape* LynxUI::offsetPath offsetPath;
  public CGPathRef LynxUI::offsetPathRef offsetPathRef;
  public CGFloat LynxUI::offsetDistance offsetDistance;
  public CGFloat LynxUI::offsetRotate offsetRotate;
  public BOOL LynxUI::isAutoOffsetRotate isAutoOffsetRotate;
  public BOOL LynxUI::offsetHasChanged offsetHasChanged;
  public LynxUIContext* LynxUI::context context;
  public CGRect LynxUI::frame frame;
  public CGRect LynxUI::updatedFrame updatedFrame;
  public UIEdgeInsets LynxUI::padding padding;
  public UIEdgeInsets LynxUI::border border;
  public UIEdgeInsets LynxUI::margin margin;
  public CGFloat LynxUI::fontSize fontSize;
  public CGPoint LynxUI::contentOffset contentOffset;
  public LynxBackgroundManager* LynxUI::backgroundManager backgroundManager;
  public BOOL LynxUI::clipOnBorderRadius clipOnBorderRadius;
  public LynxKeyframeManager* LynxUI::animationManager animationManager;
  public CGSize LynxUI::frameSize frameSize;
  public short LynxUI::overflow overflow;
  public float LynxUI::scaleX scaleX;
  public float LynxUI::scaleY scaleY;
  public BOOL LynxUI::isScrollContainer isScrollContainer;
  public BOOL LynxUI::isOverlay isOverlay;
  public BOOL LynxUI::firstRender firstRender;
  public NSString* LynxUI::exposureScene exposureScene;
  public NSString* LynxUI::exposureID exposureID;
  public NSString* LynxUI::internalSignature internalSignature;
  public NSArray<LynxTransformRaw*>* LynxUI::transformRaw transformRaw;
  public LynxTransformOriginRaw* LynxUI::transformOriginRaw transformOriginRaw;
  public NSArray* LynxUI::perspective perspective;
  public LynxAnimationTransformRotation* LynxUI::lastTransformRotation lastTransformRotation;
  public CATransform3D LynxUI::lastTransformWithoutRotate lastTransformWithoutRotate;
  public CATransform3D LynxUI::lastTransformWithoutRotateXY lastTransformWithoutRotateXY;
  public NSArray<NSString*>* LynxUI::accessibilityElementsIds accessibilityElementsIds;
  public NSArray<NSString*>* LynxUI::accessibilityElementsA11yIds accessibilityElementsA11yIds;
  public NSArray* LynxUI::sticky sticky;
  public NSMutableArray<LynxNodeReadyBlock>* LynxUI::nodeReadyBlockArray nodeReadyBlockArray;
  public BOOL LynxUI::blockListEvent blockListEvent;
  public BOOL LynxUI::copyable copyable;
  public LynxDirectionType LynxUI::directionType directionType;
  public BOOL LynxUI::enableNewTransformOrigin enableNewTransformOrigin;
  public NSString* LynxUI::a11yID a11yID;
  public BOOL LynxUI::hasTranslateDiff hasTranslateDiff;
  public NSMutableDictionary* LynxUI()::lynxProps lynxProps;
  public BOOL LynxUI()::isFirstAnimatedReady isFirstAnimatedReady;
  public BOOL LynxUI()::enableSimultaneousTouch enableSimultaneousTouch;
  public CGFloat LynxUI()::exposureMarginTop exposureMarginTop;
  public CGFloat LynxUI()::exposureMarginBottom exposureMarginBottom;
  public CGFloat LynxUI()::exposureMarginLeft exposureMarginLeft;
  public CGFloat LynxUI()::exposureMarginRight exposureMarginRight;
  public NSString* LynxUI()::exposureUIMarginTop exposureUIMarginTop;
  public NSString* LynxUI()::exposureUIMarginBottom exposureUIMarginBottom;
  public NSString* LynxUI()::exposureUIMarginLeft exposureUIMarginLeft;
  public NSString* LynxUI()::exposureUIMarginRight exposureUIMarginRight;
  public NSString* LynxUI()::exposureArea exposureArea;
  public CGFloat LynxUI()::hitSlopTop hitSlopTop;
  public CGFloat LynxUI()::hitSlopBottom hitSlopBottom;
  public CGFloat LynxUI()::hitSlopLeft hitSlopLeft;
  public CGFloat LynxUI()::hitSlopRight hitSlopRight;
  public int32_t LynxUI()::pseudoStatus pseudoStatus;
  public BOOL LynxUI()::alignHeight alignHeight;
  public BOOL LynxUI()::alignWidth alignWidth;
  public uint32_t LynxUI()::nodeIndex nodeIndex;
  public NSDictionary<NSNumber *, LynxGestureDetectorDarwin *>* _Nullable LynxUI()::gestureMap gestureMap;
  public BOOL LynxUI()::useDefaultAccessibilityLabel useDefaultAccessibilityLabel;
  public BOOL LynxUI()::enableAccessibilityByDefault enableAccessibilityByDefault;
  public UIAccessibilityTraits LynxUI()::accessibilityTraitsByDefault accessibilityTraitsByDefault;
  public NSMutableArray<LynxPropsDidUpdateBlockReadyBlock>* LynxUI()::propsDidUpdateBlockArray propsDidUpdateBlockArray;
  public BOOL LynxUI(Fluency)::enableScrollMonitor enableScrollMonitor;
  public NSString* LynxUI(Fluency)::scrollMonitorTagName scrollMonitorTagName;
  public NSMutableArray *readyBlockArray LynxUI::__attribute__((deprecated("Do not use this after lynx 2.16, use nodeReadyBlockArray")));
  public instancetype LynxUI::init();
  public instancetype LynxUI::initWithView:(nullable V NS_DESIGNATED_INITIALIZER);
  public nonnull V LynxUI::view();
  public nullable V LynxUI::createView();
  public void LynxUI::updateFrame:withPadding:border:margin:withLayoutAnimation:(CGRect frame,[withPadding] UIEdgeInsets padding,[border] UIEdgeInsets border,[margin] UIEdgeInsets margin,[withLayoutAnimation] BOOL with);
  public void LynxUI::updateFrame:withPadding:border:withLayoutAnimation:(CGRect frame,[withPadding] UIEdgeInsets padding,[border] UIEdgeInsets border,[withLayoutAnimation] BOOL with);
  public void LynxUI::updateSticky:(NSArray *info);
  public void LynxUI::checkStickyOnParentScroll:withOffsetY:(CGFloat offsetX,[withOffsetY] CGFloat offsetY);
  public void LynxUI::layoutDidFinished();
  public void LynxUI::finishLayoutOperation();
  public BOOL LynxUI::hasCustomLayout();
  public BOOL LynxUI::hasTranslateDiff:(NSArray *transform);
  public void LynxUI::didInsertChild:atIndex:(LynxUI *child,[atIndex] NSInteger index);
  public void LynxUI::onReceiveUIOperation:(nullable id value);
  public void LynxUI::prepareKeyframeManager();
  public void LynxUI::setRawEvents:andLepusRawEvents:(NSSet< NSString * > *events,[andLepusRawEvents] NSSet< NSString * > *lepusEvents);
  public void LynxUI::eventDidSet();
  public void LynxUI::setGestureDetectors:(NSSet< LynxGestureDetectorDarwin * > *detectors);
  public void LynxUI::gestureDidSet();
  public bool LynxUI::updateLayerMaskOnFrameChanged();
  public (deprecated("Do not use this after lynx 2.5")) LynxUI::__attribute__();
  public (deprecated("Do not use this after lynx 2.5")) LynxUI::__attribute__();
  public void LynxUI::resetContentOffset();
  public LynxUI * LynxUI::getParent();
  public float LynxUI::getTransationX();
  public float LynxUI::getTransationY();
  public float LynxUI::getTransationZ();
  public CALayer * LynxUI::getPresentationLayer();
  public LynxUI * LynxUI::getExposeReceiveTarget();
  public CGRect LynxUI::getBoundingClientRectToScreen();
  public void LynxUI::removeChildrenExposureUI();
  public LynxUI * LynxUI::getRelativeUI:(NSString *idSelector);
  public CGRect LynxUI::getBoundingClientRect();
  public void LynxUI::updateManagerRelated();
  public CGRect LynxUI::getRelativeBoundingClientRect:(NSDictionary *params);
  public TransOffset LynxUI::getTransformValueWithLeft:right:top:bottom:(float left,[right] float right,[top] float top,[bottom] float bottom);
  public CGRect LynxUI::frameFromParent();
  public void LynxUI::willMoveToWindow:(UIWindow *window);
  public void LynxUI::frameDidChange();
  public BOOL LynxUI::shouldHitTest:withEvent:(CGPoint point,[withEvent] nullable UIEvent *event);
  public BOOL LynxUI::dispatchEvent:(LynxEventDetail *event);
  public void LynxUI::onAnimationStart:startFrame:finalFrame:duration:(NSString *type,[startFrame] CGRect startFrame,[finalFrame] CGRect finalFrame,[duration] NSTimeInterval duration);
  public void LynxUI::onAnimationEnd:startFrame:finalFrame:duration:(NSString *type,[startFrame] CGRect startFrame,[finalFrame] CGRect finalFrame,[duration] NSTimeInterval duration);
  public void LynxUI::resetAnimation();
  public void LynxUI::restartAnimation();
  public void LynxUI::removeAnimationForReuse();
  public void LynxUI::setAnimation:(NSArray *value);
  public void LynxUI::setTransition:(NSArray *value);
  public void LynxUI::sendLayoutChangeEvent();
  public CALayer * LynxUI::topLayer();
  public CALayer * LynxUI::bottomLayer();
  public BOOL LynxUI::isRtl();
  public void LynxUI::updateCSSDefaultValue();
  public LynxOverflowType LynxUI::getInitialOverflowType();
  public void LynxUI::onListCellAppear:withList:(nullable NSString *itemKey,[withList] LynxUI *list);
  public void LynxUI::onListCellDisappear:exist:withList:(nullable NSString *itemKey,[exist] BOOL isExist,[withList] LynxUI *list);
  public void LynxUI::onListCellPrepareForReuse:withList:(nullable NSString *itemKey,[withList] LynxUI *list);
  public NSMutableDictionary * LynxUI::getNativeStorageFromList:(LynxUI *list);
  public void LynxUI::removeKeyFromNativeStorage:key:(LynxUI *list,[key] NSString *key);
  public void LynxUI::storeKeyToNativeStorage:key:value:(LynxUI *list,[key] NSString *key,[value] id value);
  public BOOL LynxUI::initialPropsFlushed:cacheKey:(NSString *initialPropKey,[cacheKey] NSString *cacheKey);
  public void LynxUI::setInitialPropsHasFlushed:cacheKey:(NSString *initialPropKey,[cacheKey] NSString *cacheKey);
  public BOOL LynxUI::notifyParent();
  public CGFloat LynxUI::toPtWithUnitValue:fontSize:(NSString *unitValue,[fontSize] CGFloat fontSize);
  public void LynxUI::setLayerValue:forKeyPath:forAllLayers:(id value,[forKeyPath] nonnull NSString *keyPath,[forAllLayers] BOOL forAllLayers);
  public void LynxUI()::dispatchMoveToWindow:(UIWindow *window);
  public BOOL LynxUI()::containsPoint:(CGPoint point);
  public BOOL LynxUI()::containsPoint:inHitTestFrame:(CGPoint point, [inHitTestFrame] CGRect frame);
  public BOOL LynxUI()::childrenContainPoint:(CGPoint point);
  public CGRect LynxUI()::getHitTestFrame();
  public CGRect LynxUI()::getHitTestFrameWithFrame:(CGRect frame);
  public CGPoint LynxUI()::getHitTestPoint:(CGPoint inPoint);
  public BOOL LynxUI()::enableExposureUIMargin();
  public void LynxUI()::setImplicitAnimation();
  public void LynxUI()::scrollIntoViewWithSmooth:blockType:inlineType:callback:(BOOL isSmooth, [blockType] NSString *blockType, [inlineType] NSString *inlineType, [callback] LynxUIMethodCallbackBlock callback);
  public void LynxUI()::setAsyncDisplayFromTTML:(BOOL async);
  public void LynxUI()::updateFrameWithoutLayoutAnimation:withPadding:border:margin:(CGRect frame, [withPadding] UIEdgeInsets padding, [border] UIEdgeInsets border, [margin] UIEdgeInsets margin);
  public void LynxUI()::onLayoutAnimationStart:(CGRect frame);
  public void LynxUI()::onLayoutAnimationEnd:(CGRect frame);
  public CGRect LynxUI()::getRectToWindow();
  public BOOL LynxUI()::didSizeChanged();
  public BOOL LynxUI()::shouldReDoTransform();
  public void LynxUI()::setName:(NSString *name);
  public BOOL LynxUI()::isVisible();
  public BOOL LynxUI()::isVisible();
  public LynxGestureArenaManager * LynxUI()::getGestureArenaManager();
  public void LynxUI()::markNeedDisplay();
  public instancetype LynxUI()::NS_DESIGNATED_INITIALIZER();
  public void LynxUI()::setView:(UIView *view);
  public void LynxUI()::initProperties();
  public void LynxUI()::onNodeReadyForUIOwner();
  public void LynxUI()::propsDidUpdateForUIOwner();
  public void LynxUI(Accessibility)::handleAccessibility:autoScroll:(UIView *accessibilityAttachedCell, [autoScroll] BOOL accessibilityAutoScroll);
  public NSString * LynxUI(Accessibility)::accessibilityText();
  public NSArray * LynxUI(Accessibility)::setExclusiveAccessibilityElements:subTree:previousNodes:(BOOL exclusive, [subTree] LynxUI *subTree, [previousNodes] NSArray *previousNodes);
  public void LynxUI(Accessibility)::clearExclusiveAccessibilityElements:(NSArray *nodes);
  public void LynxUI(AsyncDisplay)::displayAsynchronously();
  public BOOL LynxUI(AsyncDisplay)::enableAsyncDisplay();
  public void LynxUI(AsyncDisplay)::displayAsyncWithCompletionBlock:(lynx_async_display_completion_block_t block);
  public id LynxUI(AsyncDisplay)::drawParameter();
  public void LynxUI(AsyncDisplay)::displayComplexBackgroundAsynchronouslyWithDisplay:completion:(lynx_async_get_background_image_block_t displayBlock, [completion] lynx_async_display_completion_block_t completionBlock);
  public void LynxUI(Fluency)::postFluencyEventWithInfo:(LynxScrollInfo *info);
  public LynxScrollInfo * LynxUI(Fluency)::infoWithScrollView:selector:(UIScrollView *view, [selector] SEL selector);
  public void LynxUI(Gesture)::consumeInternalGesture:(BOOL consume);
  typedef void(^ LynxUI(AsyncDisplay)::LynxCGContextImageDrawingActions) (CGContextRef context)
  public void LynxUI(AsyncDisplay)::drawRect:withParameters:(CGRect bounds, [withParameters] id drawParameters);
  public UIImage * LynxUI(AsyncDisplay)::imageWithActionBlock:opaque:scale:size:(LynxCGContextImageDrawingActions action, [opaque] BOOL opaque, [scale] CGFloat scale, [size] CGSize size);
}

public interface LynxUI(Accessibility) {
  public void LynxUI(Accessibility)::handleAccessibility:autoScroll:(UIView *accessibilityAttachedCell,[autoScroll] BOOL accessibilityAutoScroll);
  public NSString * LynxUI(Accessibility)::accessibilityText();
  public NSArray * LynxUI(Accessibility)::setExclusiveAccessibilityElements:subTree:previousNodes:(BOOL exclusive,[subTree] LynxUI *subTree,[previousNodes] NSArray *previousNodes);
  public void LynxUI(Accessibility)::clearExclusiveAccessibilityElements:(NSArray *nodes);
}

public interface LynxUI(AsyncDisplay) {
  typedef void(^ LynxUI(AsyncDisplay)::LynxCGContextImageDrawingActions) (CGContextRef context)
  public void LynxUI(AsyncDisplay)::displayAsynchronously();
  public BOOL LynxUI(AsyncDisplay)::enableAsyncDisplay();
  public void LynxUI(AsyncDisplay)::displayAsyncWithCompletionBlock:(lynx_async_display_completion_block_t block);
  public id LynxUI(AsyncDisplay)::drawParameter();
  public void LynxUI(AsyncDisplay)::displayComplexBackgroundAsynchronouslyWithDisplay:completion:(lynx_async_get_background_image_block_t displayBlock,[completion] lynx_async_display_completion_block_t completionBlock);
  public void LynxUI(AsyncDisplay)::drawRect:withParameters:(CGRect bounds,[withParameters] id drawParameters);
  public UIImage * LynxUI(AsyncDisplay)::imageWithActionBlock:opaque:scale:size:(LynxCGContextImageDrawingActions action,[opaque] BOOL opaque,[scale] CGFloat scale,[size] CGSize size);
}

public interface LynxUI(Fluency) {
  public BOOL LynxUI(Fluency)::enableScrollMonitor enableScrollMonitor;
  public NSString* LynxUI(Fluency)::scrollMonitorTagName scrollMonitorTagName;
  public void LynxUI(Fluency)::postFluencyEventWithInfo:(LynxScrollInfo *info);
  public LynxScrollInfo * LynxUI(Fluency)::infoWithScrollView:selector:(UIScrollView *view,[selector] SEL selector);
}

public interface LynxUI(Gesture) {
  public void LynxUI(Gesture)::consumeInternalGesture:(BOOL consume);
}

public class LynxUICollection : LynxUIListLoader, <UICollectionView> {
  public <LynxUIListScrollEvent> BOOL LynxUICollection::noRecursiveLayout noRecursiveLayout;
  public BOOL LynxUICollection::forceReloadData forceReloadData;
  public NSMutableDictionary* LynxUICollection::listNativeStateCache listNativeStateCache;
  public NSMutableDictionary<NSString *, NSMutableSet<NSString *> *>* LynxUICollection::initialFlushPropCache initialFlushPropCache;
  public LynxListLayoutOrientation LynxUICollection::layoutOrientation layoutOrientation;
  public BOOL LynxUICollection::enableRtl enableRtl;
  public BOOL LynxUICollection::enableAsyncList enableAsyncList;
  public NSInteger LynxUICollection()::scrollUpperThresholdItemCount scrollUpperThresholdItemCount;
  public NSInteger LynxUICollection()::scrollLowerThresholdItemCount scrollLowerThresholdItemCount;
  public BOOL LynxUICollection()::enableUpdateAnimation enableUpdateAnimation;
  public LynxCollectionCellUpdateAnimationType LynxUICollection()::cellUpdateAnimationType cellUpdateAnimationType;
  public BOOL LynxUICollection()::needsVisibleCells needsVisibleCells;
  public BOOL LynxUICollection()::needsLayoutCompleteEvent needsLayoutCompleteEvent;
  public BOOL LynxUICollection()::enableListDebugInfoEvent enableListDebugInfoEvent;
  public NSInteger LynxUICollection()::numberOfColumns numberOfColumns;
  public CGFloat LynxUICollection()::mainAxisGap mainAxisGap;
  public CGFloat LynxUICollection()::crossAxisGap crossAxisGap;
  public NSInteger LynxUICollection()::initialScrollIndex initialScrollIndex;
  public NSInteger LynxUICollection()::updatedScrollIndex updatedScrollIndex;
  public CGFloat LynxUICollection()::pagingAlignFactor pagingAlignFactor;
  public CGFloat LynxUICollection()::pagingAlignOffset pagingAlignOffset;
  public NSMutableArray* LynxUICollection()::listDelegates listDelegates;
  public BOOL LynxUICollection()::fixedContentOffset fixedContentOffset;
  public BOOL LynxUICollection()::reloadAll reloadAll;
  public LynxBounceForbiddenDirection LynxUICollection()::bounceForbiddenDirection bounceForbiddenDirection;
  public LynxListAppearEventEmitter* LynxUICollection()::appearEventCourier appearEventCourier;
  public LynxListScrollEventEmitter* LynxUICollection()::scrollEventEmitter scrollEventEmitter;
  public LynxCollectionScroll* LynxUICollection()::scroll scroll;
  public LynxCollectionViewLayout* LynxUICollection()::layout layout;
  public LynxCollectionDataSource* LynxUICollection()::dataSource dataSource;
  public NSDictionary<NSString *, NSArray *>* LynxUICollection()::curComponents curComponents;
  public NSDictionary* _Nullable LynxUICollection()::diffResultFromTasm diffResultFromTasm;
  public NSDictionary* _Nullable LynxUICollection()::listNoDiffInfo listNoDiffInfo;
  public LynxListDebugInfoLevel LynxUICollection()::debugInfoLevel debugInfoLevel;
  public LynxGestureConsumer* LynxUICollection()::gestureConsumer gestureConsumer;
  public BOOL LynxUICollection::isNeedRenderComponents();
  public BOOL LynxUICollection::shouldGenerateDebugInfo();
  public void LynxUICollection()::performBatchUpdates:completion:animated:(void(^ updates)(void), [completion] void(^_Nullable completion)(BOOL), [animated] BOOL animated);
  public BOOL LynxUICollection()::isInOfflineMode();
  public void LynxUICollection()::sendListDebugInfoEvent:(NSString *info);
  public void LynxUICollection()::setIncreaseFrequencyWithGesture:(BOOL enable);
  public void LynxUICollection(Delegate)::sendLayoutCompleteEvent();
}

public interface LynxUICollection(Delegate) {
  public void LynxUICollection(Delegate)::sendLayoutCompleteEvent();
}

public class LynxUIComponent : LynxUIView {
  public id<LynxUIComponentLayoutObserver> LynxUIComponent::layoutObserver layoutObserver;
  public NSString* LynxUIComponent::itemKey itemKey;
  public BOOL LynxUIComponent::frameDidSet frameDidSet;
  public NSInteger LynxUIComponent::zIndex zIndex;
  public void LynxUIComponent::asyncListItemRenderFinished:(int64_t operationID);
}

public protocol LynxUIComponentLayoutObserver-p : <NSObject> {
  public void LynxUIComponentLayoutObserver-p::onComponentLayoutUpdated:(LynxUIComponent *component);
  public void LynxUIComponentLayoutObserver-p::onAsyncComponentLayoutUpdated:operationID:(LynxUIComponent *component,[operationID] int64_t operationID);
}

public class LynxUIContext : NSObject {
  public LynxContext* LynxUIContext::lynxContext lynxContext;
  public id<LynxImageFetcher> LynxUIContext::imageFetcher imageFetcher;
  public id<LynxResourceFetcher> LynxUIContext::resourceFetcher resourceFetcher;
  public id<LynxResourceFetcher> LynxUIContext::resourceServiceFetcher resourceServiceFetcher;
  public id<LynxListLayoutProtocol> LynxUIContext::customizedListLayout customizedListLayout;
  public id<LUIErrorHandling> LynxUIContext::errorHandler errorHandler;
  public id<LynxScrollListener> LynxUIContext::scrollListener scrollListener;
  public LynxEventHandler* LynxUIContext::eventHandler eventHandler;
  public LynxEventEmitter* LynxUIContext::eventEmitter eventEmitter;
  public UIView<LUIBodyView>* LynxUIContext::rootView rootView;
  public LynxRootUI* LynxUIContext::rootUI rootUI;
  public LynxFontFaceContext* LynxUIContext::fontFaceContext fontFaceContext;
  public LynxShadowNodeOwner* LynxUIContext::nodeOwner nodeOwner;
  public LynxUIOwner* LynxUIContext::uiOwner uiOwner;
  public id LynxUIContext::lynxModuleExtraData lynxModuleExtraData;
  public int64_t LynxUIContext::shellPtr shellPtr;
  public LynxScreenMetrics* LynxUIContext::screenMetrics screenMetrics;
  public LynxUIIntersectionObserverManager* LynxUIContext::intersectionManager intersectionManager;
  public LynxUIExposure* LynxUIContext::uiExposure uiExposure;
  public NSDictionary* LynxUIContext::keyframesDict keyframesDict;
  public NSDictionary* LynxUIContext::contextDict contextDict;
  public LynxGlobalObserver* LynxUIContext::observer observer;
  public id<LynxGenericResourceFetcher> LynxUIContext::genericResourceFetcher genericResourceFetcher;
  public id<LynxMediaResourceFetcher> LynxUIContext::mediaResourceFetcher mediaResourceFetcher;
  public id<LynxTemplateResourceFetcher> LynxUIContext::templateResourceFetcher templateResourceFetcher;
  public BOOL LynxUIContext::defaultOverflowVisible defaultOverflowVisible;
  public BOOL LynxUIContext::defaultImplicitAnimation defaultImplicitAnimation;
  public BOOL LynxUIContext::enableTextRefactor enableTextRefactor;
  public BOOL LynxUIContext::defaultAutoResumeAnimation defaultAutoResumeAnimation;
  public BOOL LynxUIContext::defaultEnableNewTransformOrigin defaultEnableNewTransformOrigin;
  public BOOL LynxUIContext::enableEventRefactor enableEventRefactor;
  public BOOL LynxUIContext::enableA11yIDMutationObserver enableA11yIDMutationObserver;
  public BOOL LynxUIContext::enableTextOverflow enableTextOverflow;
  public BOOL LynxUIContext::enableNewClipMode enableNewClipMode;
  public BOOL LynxUIContext::enableEventThrough enableEventThrough;
  public BOOL LynxUIContext::enableBackgroundShapeLayer enableBackgroundShapeLayer;
  public BOOL LynxUIContext::enableFiberArch enableFiberArch;
  public BOOL LynxUIContext::enableExposureUIMargin enableExposureUIMargin;
  public BOOL LynxUIContext::enableTextLayerRender enableTextLayerRender;
  public BOOL LynxUIContext::enableTextLanguageAlignment enableTextLanguageAlignment;
  public BOOL LynxUIContext::enableXTextLayoutReused enableXTextLayoutReused;
  public BOOL LynxUIContext::enableNewGesture enableNewGesture;
  public BOOL LynxUIContext::cssAlignWithLegacyW3c cssAlignWithLegacyW3c;
  public NSString* LynxUIContext::targetSdkVersion targetSdkVersion;
  public BOOL LynxUIContext::enableTextNonContiguousLayout enableTextNonContiguousLayout;
  public BOOL LynxUIContext::enableImageDownsampling enableImageDownsampling;
  public BOOL LynxUIContext::enableNewImage enableNewImage;
  public BOOL LynxUIContext::trailUseNewImage trailUseNewImage;
  public NSInteger LynxUIContext::logBoxImageSizeWarningThreshold logBoxImageSizeWarningThreshold;
  public BOOL LynxUIContext()::imageMonitorEnabled imageMonitorEnabled;
  public BOOL LynxUIContext()::devtoolEnabled devtoolEnabled;
  public BOOL LynxUIContext()::fixNewImageDownSampling fixNewImageDownSampling;
  public LynxScrollFluency* LynxUIContext()::fluencyInnerListener fluencyInnerListener;
  public instancetype LynxUIContext::initWithScreenMetrics:(LynxScreenMetrics *screenMetrics);
  public void LynxUIContext::updateScreenSize:(CGSize screenSize);
  public void LynxUIContext::onGestureRecognized();
  public void LynxUIContext::onGestureRecognizedByUI:(LynxUI *ui);
  public void LynxUIContext::onPropsChangedByUI:(LynxUI *ui);
  public BOOL LynxUIContext::isTouchMoving();
  public NSNumber * LynxUIContext::getLynxRuntimeId();
  public void LynxUIContext::didReceiveResourceError:(NSError *error);
  public void LynxUIContext::reportError:(nonnull NSError *error);
  public void LynxUIContext::didReceiveException:withMessage:forUI:(NSException *exception,[withMessage] NSString *message,[forUI] LynxUI *ui);
  public BOOL LynxUIContext::isDev();
  public void LynxUIContext::addUIToExposedMap:(LynxUI *ui);
  public void LynxUIContext::addUIToExposedMap:withUniqueIdentifier:extraData:useOptions:(LynxUI *ui,[withUniqueIdentifier] NSString *_Nullable uniqueID,[extraData] NSDictionary *_Nullable data,[useOptions] NSDictionary *_Nullable options);
  public void LynxUIContext::removeUIFromExposedMap:(LynxUI *ui);
  public void LynxUIContext::removeUIFromExposedMap:withUniqueIdentifier:(LynxUI *ui,[withUniqueIdentifier] NSString *_Nullable uniqueID);
  public void LynxUIContext::removeUIFromIntersectionManager:(LynxUI *ui);
  public nullable id< LynxResourceFetcher > LynxUIContext::getGenericResourceFetcher();
  public void LynxUIContext()::setUIConfig:(id< LUIConfig > config);
  public void LynxUIContext()::setDefaultOverflowVisible:(BOOL enable);
  public void LynxUIContext()::setDefaultImplicitAnimation:(BOOL enable);
  public void LynxUIContext()::setEnableTextRefactor:(BOOL enable);
  public void LynxUIContext()::setEnableTextOverflow:(BOOL enable);
  public void LynxUIContext()::setEnableNewClipMode:(BOOL enable);
  public void LynxUIContext()::setEnableEventRefactor:(BOOL enable);
  public void LynxUIContext()::setEnableA11yIDMutationObserver:(BOOL enable);
  public void LynxUIContext()::setEnableEventThrough:(BOOL enable);
  public void LynxUIContext()::setEnableBackgroundShapeLayer:(BOOL enable);
  public void LynxUIContext()::setEnableFiberArch:(BOOL enable);
  public void LynxUIContext()::setEnableNewGesture:(BOOL enable);
  public void LynxUIContext()::setEnableExposureUIMargin:(BOOL enable);
  public void LynxUIContext()::setEnableTextLanguageAlignment:(BOOL enable);
  public void LynxUIContext()::setEnableXTextLayoutReused:(BOOL enable);
  public void LynxUIContext()::setCSSAlignWithLegacyW3c:(BOOL algin);
  public void LynxUIContext()::setTargetSdkVersion:(NSString *version);
  public void LynxUIContext()::setEnableTextLayerRender:(BOOL enable);
  public void LynxUIContext()::setEnableTextNonContiguousLayout:(BOOL enable);
  public void LynxUIContext()::setEnableImageDownsampling:(BOOL enable);
  public void LynxUIContext()::setEnableNewImage:(BOOL enable);
  public void LynxUIContext()::setTrailUseNewImage:(BOOL enable);
  public void LynxUIContext()::setLogBoxImageSizeWarningThreshold:(NSInteger threshold);
  public void LynxUIContext()::didReceiveResourceError:withSource:type:(LynxError *error, [withSource] NSString *resourceUrl, [type] NSString *type);
  public void LynxUIContext()::reportLynxError:(LynxError *error);
  public int32_t LynxUIContext()::instanceId();
  public void LynxUIContext(Internal)::mergeKeyframesWithLynxKeyframes:forKey:(LynxKeyframes *keyframes, [forKey] NSString *name);
}

public interface LynxUIContext(Internal) {
  public id<LynxImageFetcher> LynxUIContext(Internal)::imageFetcher imageFetcher;
  public LynxEventHandler* LynxUIContext(Internal)::eventHandler eventHandler;
  public LynxEventEmitter* LynxUIContext(Internal)::eventEmitter eventEmitter;
  public UIView* LynxUIContext(Internal)::rootView rootView;
  public NSDictionary* LynxUIContext(Internal)::keyframesDict keyframesDict;
  public void LynxUIContext(Internal)::mergeKeyframesWithLynxKeyframes:forKey:(LynxKeyframes *keyframes,[forKey] NSString *name);
}

public class LynxUIExposure : NSObject {
  public void LynxUIExposure::setRootUI:(LynxRootUI *rootUI);
  public BOOL LynxUIExposure::addLynxUI:withUniqueIdentifier:extraData:useOptions:(LynxUI *ui,[withUniqueIdentifier] NSString *_Nullable uniqueID,[extraData] NSDictionary *_Nullable data,[useOptions] NSDictionary *_Nullable options);
  public void LynxUIExposure::removeLynxUI:withUniqueIdentifier:(LynxUI *ui,[withUniqueIdentifier] NSString *_Nullable uniqueID);
  public void LynxUIExposure::willMoveToWindow:(BOOL windowIsNil);
  public void LynxUIExposure::didMoveToWindow:(BOOL windowIsNil);
  public void LynxUIExposure::destroyExposure();
  public void LynxUIExposure::addExposureToRunLoop();
  public void LynxUIExposure::stopExposure:(NSDictionary *options);
  public void LynxUIExposure::resumeExposure();
  public void LynxUIExposure::setObserverFrameRateDynamic:(NSDictionary *options);
  public instancetype LynxUIExposure()::initWithObserver:(LynxGlobalObserver *observer);
  public BOOL LynxUIExposure()::isLynxViewChanged();
  public void LynxUIExposure()::setObserverFrameRate:(int32_t rate);
  public void LynxUIExposure()::setEnableCheckExposureOptimize:(BOOL enableCheckExposureOptimize);
  public void LynxUIExposure()::exposureHandler:(CADisplayLink *sender);
  public void LynxUIExposure()::sendEvent:eventName:(NSMutableSet< LynxUIExposureDetail * > *uiSet, [eventName] NSString *eventName);
  public void LynxUIExposure()::removeFromRunLoop();
  public CGRect LynxUIExposure()::borderOfExposureScreen:(LynxUI *ui);
  public BOOL LynxUIExposure()::isStopExposure isStopExposure;
  public LynxRootUI* LynxUIExposure()::rootUI rootUI;
  public CADisplayLink* LynxUIExposure()::displayLink displayLink;
  public NSMutableSet<LynxUIExposureDetail *>* LynxUIExposure()::uiInWindowMapBefore uiInWindowMapBefore;
  public NSMutableDictionary<NSString *, LynxUIExposureDetail *>* LynxUIExposure()::exposedLynxUIMap exposedLynxUIMap;
}

public class LynxUIExposureDetail : NSObject {
  public LynxUI* LynxUIExposureDetail::ui ui;
}

public class LynxUIImage : LynxUI, <UIImageView> {
  public BOOL LynxUIImage::animated animated;
  public id LynxUIImage::customImageRequest customImageRequest;
  public NSMutableDictionary* LynxUIImage::resLoaderInfo resLoaderInfo;
  public LynxRequestOptions LynxUIImage::requestOptions requestOptions;
  public void LynxUIImage::startAnimating();
  public bool LynxUIImage::getEnableImageDownsampling();
  public BOOL LynxUIImage::shouldUseNewImage();
  public void LynxUIImage::requestImage();
  public BOOL LynxUIImage::isAnimatedImage:(UIImage *image);
}

public class LynxUIIntersectionObserver : NSObject {
  public LynxUI* LynxUIIntersectionObserver::attachedUI attachedUI;
  public instancetype LynxUIIntersectionObserver::initWithManager:observerId:componentId:options:(LynxUIIntersectionObserverManager *manager,[observerId] NSInteger observerId,[componentId] NSString *componentId,[options] NSDictionary *options);
  public instancetype LynxUIIntersectionObserver::initWithOptions:manager:attachedUI:(NSDictionary *options,[manager] LynxUIIntersectionObserverManager *manager,[attachedUI] LynxUI *attachedUI);
  public void LynxUIIntersectionObserver::relativeTo:margins:(NSString *selector,[margins] NSDictionary *margins);
  public void LynxUIIntersectionObserver::relativeToViewportWithMargins:(NSDictionary *margins);
  public void LynxUIIntersectionObserver::relativeToScreenWithMargins:(NSDictionary *margins);
  public void LynxUIIntersectionObserver::observe:callbackId:(NSString *targetSelector,[callbackId] NSInteger callbackId);
  public void LynxUIIntersectionObserver::disconnect();
  public void LynxUIIntersectionObserver::checkForIntersections();
}

public class LynxUIIntersectionObserverManager : NSObject, <LynxEventObserver> {
  public LynxUIOwner* LynxUIIntersectionObserverManager::uiOwner uiOwner;
  public BOOL LynxUIIntersectionObserverManager::enableNewIntersectionObserver enableNewIntersectionObserver;
  public instancetype LynxUIIntersectionObserverManager::initWithLynxContext:(LynxContext *context);
  public void LynxUIIntersectionObserverManager::addIntersectionObserver:(LynxUIIntersectionObserver *observer);
  public void LynxUIIntersectionObserverManager::removeIntersectionObserver:(NSInteger observerId);
  public void LynxUIIntersectionObserverManager::removeAttachedIntersectionObserver:(LynxUI *attachedUI);
  public LynxUIIntersectionObserver * LynxUIIntersectionObserverManager::getObserverById:(NSInteger observerId);
  public void LynxUIIntersectionObserverManager::notifyObservers();
  public void LynxUIIntersectionObserverManager::didMoveToWindow:(BOOL windowIsNil);
  public void LynxUIIntersectionObserverManager::addIntersectionObserverToRunLoop();
  public void LynxUIIntersectionObserverManager::destroyIntersectionObserver();
  public void LynxUIIntersectionObserverManager::reset();
  public void LynxUIIntersectionObserverManager()::setEnableNewIntersectionObserver:(BOOL enable);
}

public class LynxUIKitAPIAdapter : NSObject {
  public NSArray< UIWindow * > * LynxUIKitAPIAdapter::getWindows();
  public UIWindow * LynxUIKitAPIAdapter::getKeyWindow();
  public CGRect LynxUIKitAPIAdapter::getStatusBarFrame();
}

public class LynxUILayoutTick : LynxLayoutTick {
  public instancetype LynxUILayoutTick::initWithRoot:block:(LynxView *root,[block] nonnull LynxOnLayoutBlock block);
  public void LynxUILayoutTick::attach:(LynxView *_Nonnull root);
}

public class LynxUIListCellContentProducer : NSObject {
  public NSInteger LynxUIListCellContentProducer::sign sign;
  public BOOL LynxUIListCellContentProducer::needsInternalCellAppearNotification needsInternalCellAppearNotification;
  public BOOL LynxUIListCellContentProducer::needsInternalCellDisappearNotification needsInternalCellDisappearNotification;
  public BOOL LynxUIListCellContentProducer::needsInternalCellPrepareForReuseNotification needsInternalCellPrepareForReuseNotification;
  public void LynxUIListCellContentProducer::setUIContext:(LynxUIContext *UIContext);
  public LynxListViewCellLight * LynxUIListCellContentProducer::listView:cellForItemAtIndex:(LynxListViewLight *listView,[cellForItemAtIndex] NSInteger index);
  public void LynxUIListCellContentProducer::listView:enqueueCell:(LynxListViewLight *listView,[enqueueCell] LynxListViewCellLightLynxUI *cell);
  public id< LynxListCell > LynxUIListCellContentProducer::listView:updateCell:toItemAtIndex:(LynxListViewLight *listView,[updateCell] id< LynxListCell > cell,[toItemAtIndex] NSInteger index);
}

public class LynxUIListContainer : LynxUIScroller, <LynxUIComponentLayoutObserver> {
  public BOOL LynxUIListContainer::needAdjustContentOffset needAdjustContentOffset;
  public CGPoint LynxUIListContainer::targetDelta targetDelta;
  public CGFloat LynxUIListContainer::targetContentSize targetContentSize;
  public NSMutableDictionary* LynxUIListContainer::listNativeStateCache listNativeStateCache;
  public NSMutableDictionary<NSString *, NSMutableSet<NSString *> *>* LynxUIListContainer::initialFlushPropCache initialFlushPropCache;
  public void LynxUIListContainer::updateScrollInfoWithEstimatedOffset:smooth:scrolling:(CGFloat estimatedOffset,[smooth] BOOL smooth,[scrolling] BOOL scrolling);
  public void LynxUIListContainer::insertListComponent:(LynxUIComponent *component);
  public void LynxUIListContainer::removeListComponent:(LynxUIComponent *component);
  public void LynxUIListContainer::detachedFromWindow();
}

public class LynxUIListDataSource : NSObject {
  public void LynxUIListDataSource::setLynxSign:(NSInteger sign);
  public void LynxUIListDataSource::setLynxUIContext:(LynxUIContext *context);
  public id< LynxListCell > _Nullable LynxUIListDataSource::listView:cellForItemAtIndex:(LynxListViewLight *listView,[cellForItemAtIndex] NSInteger index);
  public void LynxUIListDataSource::listView:recycleCell:(LynxListViewLight *view,[recycleCell] id< LynxListCell > cell);
  public id< LynxListCell > LynxUIListDataSource::listView:updateCell:toItemAtIndex:(LynxListViewLight *listView,[updateCell] id< LynxListCell > cell,[toItemAtIndex] NSInteger index);
}

public protocol LynxUIListDelegate-p : <NSObject> {
  public void LynxUIListDelegate-p::listDidScroll:(UIScrollView *scrollView);
  public void LynxUIListDelegate-p::listWillBeginDragging:(UIScrollView *scrollView);
  public void LynxUIListDelegate-p::listDidEndDragging:willDecelerate:(UIScrollView *scrollView,[willDecelerate] BOOL decelerate);
  public void LynxUIListDelegate-p::listDidEndDecelerating:(UIScrollView *scrollView);
}

public class LynxUIListDiffResult : NSObject {
  public NSArray<NSIndexPath *>* LynxUIListDiffResult::removePaths removePaths;
  public NSArray<NSIndexPath *>* LynxUIListDiffResult::insertPaths insertPaths;
  public NSArray<NSIndexPath *>* LynxUIListDiffResult::updateFromPaths updateFromPaths;
  public NSArray<NSIndexPath *>* LynxUIListDiffResult::updateToPaths updateToPaths;
  public NSArray<NSIndexPath *>* LynxUIListDiffResult::moveFromPaths moveFromPaths;
  public NSArray<NSIndexPath *>* LynxUIListDiffResult::moveToPaths moveToPaths;
  public BOOL LynxUIListDiffResult::empty empty;
}

public protocol LynxUIListInspector-p : <NSObject> {
  public double LynxUIListInspector-p::getCellOffsetByIndex:(int index);
}

public class LynxUIListInvalidationContext : NSObject {
  public NSArray<NSNumber*>* LynxUIListInvalidationContext::removals removals;
  public NSArray<NSNumber*>* LynxUIListInvalidationContext::insertions insertions;
  public NSArray<NSNumber*>* LynxUIListInvalidationContext::moveFrom moveFrom;
  public NSArray<NSNumber*>* LynxUIListInvalidationContext::moveTo moveTo;
  public NSArray<NSNumber*>* LynxUIListInvalidationContext::updateTo updateTo;
  public NSArray<NSNumber*>* LynxUIListInvalidationContext::updateFrom updateFrom;
  public NSDictionary<NSNumber*, NSValue*>* LynxUIListInvalidationContext::updates updates;
  public NSDictionary<NSNumber*, NSNumber*>* LynxUIListInvalidationContext::estimatedHeights estimatedHeights;
  public NSArray<NSNumber*>* LynxUIListInvalidationContext::fullSpanItems fullSpanItems;
  public NSArray<NSNumber*>* LynxUIListInvalidationContext::stickyTopItems stickyTopItems;
  public NSArray<NSNumber*>* LynxUIListInvalidationContext::stickyBottomItems stickyBottomItems;
  public UIEdgeInsets LynxUIListInvalidationContext::insets insets;
  public NSInteger LynxUIListInvalidationContext::numberOfColumns numberOfColumns;
  public CGFloat LynxUIListInvalidationContext::mainAxisGap mainAxisGap;
  public CGFloat LynxUIListInvalidationContext::crossAxisGap crossAxisGap;
  public LynxListLayoutType LynxUIListInvalidationContext::layoutType layoutType;
  public BOOL LynxUIListInvalidationContext::needsInternalCellAppearNotification needsInternalCellAppearNotification;
  public BOOL LynxUIListInvalidationContext::needsInternalCellDisappearNotification needsInternalCellDisappearNotification;
  public BOOL LynxUIListInvalidationContext::needsInternalCellPrepareForReuseNotification needsInternalCellPrepareForReuseNotification;
  public LynxListLayoutUpdateType LynxUIListInvalidationContext::listUpdateType listUpdateType;
  public NSNumber* LynxUIListInvalidationContext::needsVisibleCells needsVisibleCells;
  public NSInteger LynxUIListInvalidationContext::scrollToPosition scrollToPosition;
  public BOOL LynxUIListInvalidationContext::smooth smooth;
  public NSString* LynxUIListInvalidationContext::alignTo alignTo;
  public CGFloat LynxUIListInvalidationContext::offset offset;
  public NSInteger LynxUIListInvalidationContext::initialScrollIndex initialScrollIndex;
  public LynxUIListScrollThresholds* LynxUIListInvalidationContext::scrollThresholds scrollThresholds;
  public instancetype LynxUIListInvalidationContext::initWithBoundsChange();
  public instancetype LynxUIListInvalidationContext::initWithScrollThresholdsUpdate:(LynxUIListScrollThresholds *scrollThresholds);
  public instancetype LynxUIListInvalidationContext::initWithModelUpdates:(NSDictionary *updates);
  public instancetype LynxUIListInvalidationContext::initWithGeneralPropsUpdate();
  public instancetype LynxUIListInvalidationContext::initWithInitialScrollIndex:(NSInteger index);
  public instancetype LynxUIListInvalidationContext::initWithScrollToInfo:offset:alignTo:smooth:(NSInteger position,[offset] CGFloat offset,[alignTo] NSString *alignTo,[smooth] BOOL smooth);
}

public class LynxUIListLight : LynxUI, <UIScrollView> {
  public <LynxUIComponentLayoutObserver> LynxUIListInvalidationContext* LynxUIListLight::generalPropsInfo generalPropsInfo;
  public LynxListLayoutManager* LynxUIListLight::layout layout;
  public LynxUIListScrollThresholds* LynxUIListLight::scrollThreSholds scrollThreSholds;
  public NSNumber* _Nullable LynxUIListLight::initialScrollIndex initialScrollIndex;
  public NSDictionary* _Nullable LynxUIListLight::listNoDiffInfo listNoDiffInfo;
  public NSDictionary* _Nullable LynxUIListLight::diffResultFromTasm diffResultFromTasm;
  public NSDictionary<NSString*, NSArray*>* LynxUIListLight::curComponents curComponents;
  public void LynxUIListLight::setVerticalOrientation:(BOOL value);
}

public class LynxUIListLoader : <NSObject__covariant V UIView> {
  public BOOL LynxUIListLoader::diffable diffable;
  public LynxUIListDiffResult* LynxUIListLoader::diffResult diffResult;
  public NSMutableArray<NSIndexPath *>* LynxUIListLoader::fullSpanItems fullSpanItems;
  public NSMutableArray<NSIndexPath *>* LynxUIListLoader::stickyTopItems stickyTopItems;
  public NSMutableArray<NSIndexPath *>* LynxUIListLoader::stickyBottomItems stickyBottomItems;
  public BOOL LynxUIListLoader::elementTypeUpdate elementTypeUpdate;
  public NSMutableArray* LynxUIListLoader::fiberFullSpanItems fiberFullSpanItems;
  public NSMutableArray* LynxUIListLoader::fiberStickyTopItems fiberStickyTopItems;
  public NSMutableArray* LynxUIListLoader::fiberStickyBottomItems fiberStickyBottomItems;
  public NSMutableArray<NSString *>* LynxUIListLoader::reuseIdentifiers reuseIdentifiers;
  public NSMutableArray<NSString *>* LynxUIListLoader::currentItemKeys currentItemKeys;
  public NSMutableDictionary<NSIndexPath *, NSNumber *>* LynxUIListLoader::estimatedHeights estimatedHeights;
  public BOOL LynxUIListLoader::newArch newArch;
  public BOOL LynxUIListLoader::needsInternalCellAppearNotification needsInternalCellAppearNotification;
  public BOOL LynxUIListLoader::needsInternalCellDisappearNotification needsInternalCellDisappearNotification;
  public BOOL LynxUIListLoader::needsInternalCellPrepareForReuseNotification needsInternalCellPrepareForReuseNotification;
  public LynxUI * LynxUIListLoader::renderLynxUIAtIndexPath:(NSIndexPath *indexPath);
  public void LynxUIListLoader::updateLynxUI:toIndexPath:(LynxUI *lynxUI,[toIndexPath] NSIndexPath *indexPath);
  public LynxUI * LynxUIListLoader::uiAtIndexPath:(NSIndexPath *indexPath);
  public void LynxUIListLoader::asyncUIAtIndexPath:operationID:(NSIndexPath *indexPath,[operationID] int64_t operationID);
  public void LynxUIListLoader::recycleLynxUI:(LynxUI *ui);
  public void LynxUIListLoader::asyncRecycleLynxUI:(LynxUI *ui);
  public void LynxUIListLoader::loadListInfo:components:(NSDictionary *diffResult,[components] NSDictionary< NSString *, NSArray * > *components);
  public void LynxUIListLoader::updateListActionInfo:(NSDictionary *noDiffResult);
  public void LynxUIListLoader::markIsNewArch();
  public BOOL LynxUIListLoader::isAsync();
  public BOOL LynxUIListLoader::isPartOnLayout();
}

public protocol LynxUIListScrollEvent-p : <NSObject> {
  public void LynxUIListScrollEvent-p::addListDelegate:(id< LynxUIListDelegate > delegate);
  public void LynxUIListScrollEvent-p::removeListDelegate:(id< LynxUIListDelegate > delegate);
}

public class LynxUIListScrollManager : NSObject, <UIScrollViewDelegate> {
  public LynxListScrollStatus LynxUIListScrollManager::scrollStatus scrollStatus;
  public LynxListScrollDirection LynxUIListScrollManager::scrollingDirection scrollingDirection;
  public BOOL LynxUIListScrollManager::horizontal horizontal;
  public void LynxUIListScrollManager::setSign:(NSInteger sign);
  public void LynxUIListScrollManager::updateScrollThresholds:(LynxUIListScrollThresholds *scrollThreSholds);
  public void LynxUIListScrollManager::sendCustomEvent:detail:(NSString *name,[detail] NSDictionary *detail);
  public void LynxUIListScrollManager::sendScrollEvent:(UIScrollView *scrollView);
  public void LynxUIListScrollManager::setEventEmitter:(LynxEventEmitter *eventEmitter);
}

public class LynxUIListScrollThresholds : NSObject {
  public NSNumber* LynxUIListScrollThresholds::scrollToStartOffset scrollToStartOffset;
  public NSNumber* LynxUIListScrollThresholds::scrollToEndOffset scrollToEndOffset;
  public NSNumber* LynxUIListScrollThresholds::scrollToStartItemCount scrollToStartItemCount;
  public NSNumber* LynxUIListScrollThresholds::scrollToEndItemCount scrollToEndItemCount;
  public NSNumber* LynxUIListScrollThresholds::throttle throttle;
}

public class LynxUIMethodProcessor : NSObject {
  public void LynxUIMethodProcessor::invokeMethod:withParams:withResult:forUI:(NSString *method,[withParams] NSDictionary *params,[withResult] LynxUIMethodCallbackBlock callback,[forUI] LynxUI *ui);
}

public class LynxUIMockContext : NSObject {
  public LynxUIOwner* LynxUIMockContext::UIOwner UIOwner;
  public LynxUI* LynxUIMockContext::mockUI mockUI;
  public LynxUIContext* LynxUIMockContext::mockUIContext mockUIContext;
  public LynxEventEmitterUnitTestHelper* LynxUIMockContext::mockEventEmitter mockEventEmitter;
  public LynxView* LynxUIMockContext::rootView rootView;
}

public class LynxUIObservationTarget : NSObject {
  public LynxUI* LynxUIObservationTarget::ui ui;
  public NSInteger LynxUIObservationTarget::jsCallbackId jsCallbackId;
  public IntersectionObserverEntry* LynxUIObservationTarget::entry entry;
}

public class LynxUIOwner : NSObject {
  public LynxUIContext* LynxUIOwner::uiContext uiContext;
  public LynxRootUI* LynxUIOwner::rootUI rootUI;
  public LynxFontFaceContext* LynxUIOwner::fontFaceContext fontFaceContext;
  public id<LynxBaseInspectorOwner> LynxUIOwner::baseInspectOwner baseInspectOwner;
  public LynxGestureArenaManager* _Nullable LynxUIOwner::gestureArenaManager gestureArenaManager;
  public LynxTemplateRender* LynxUIOwner::templateRender templateRender;
  public attachLynxPageUI LynxUIOwner::attachLynxPageUICallback attachLynxPageUICallback;
  public void LynxUIOwner::attachLynxView:(LynxView *_Nonnull containerView);
  public instancetype LynxUIOwner::initWithContainerView:templateRender:componentRegistry:screenMetrics:(LynxView *containerView,[templateRender] LynxTemplateRender *templateRender,[componentRegistry] LynxComponentScopeRegistry *registry,[screenMetrics] LynxScreenMetrics *screenMetrics);
  public instancetype LynxUIOwner::initWithContainerView:templateRender:componentRegistry:screenMetrics:errorHandler:uiConfig:(UIView< LUIBodyView > *containerView,[templateRender] nullable LynxTemplateRender *templateRender,[componentRegistry] LynxComponentScopeRegistry *registry,[screenMetrics] LynxScreenMetrics *screenMetrics,[errorHandler] id< LUIErrorHandling > _Nullable errorHandler,[uiConfig] id< LUIConfig > _Nullable uiConfig);
  public LynxUI * LynxUIOwner::findUIBySign:(NSInteger sign);
  public LynxUI * LynxUIOwner::findUIByComponentId:(NSString *componentId);
  public LynxUI * LynxUIOwner::findUIByIdSelector:withinUI:(NSString *idSelector,[withinUI] LynxUI *ui);
  public LynxUI * LynxUIOwner::findUIByIdSelectorInParent:child:(NSString *idSelector,[child] LynxUI *child);
  public LynxUI * LynxUIOwner::findUIByRefId:withinUI:(NSString *refId,[withinUI] LynxUI *ui);
  public NSSet< NSString * > * LynxUIOwner::componentSet();
  public void LynxUIOwner::componentStatistic:(NSString *componentName);
  public void LynxUIOwner::createUIWithSign:tagName:eventSet:lepusEventSet:props:nodeIndex:gestureDetectorSet:(NSInteger sign,[tagName] nullable NSString *tagName,[eventSet] nullable NSSet< NSString * > *eventSet,[lepusEventSet] nullable NSSet< NSString * > *lepusEventSet,[props] nullable NSDictionary *props,[nodeIndex] uint32_t nodeIndex,[gestureDetectorSet] nullable NSSet< LynxGestureDetectorDarwin * > *gestureDetectorSet);
  public void LynxUIOwner::updateUIWithSign:props:eventSet:lepusEventSet:gestureDetectorSet:(NSInteger sign,[props] NSDictionary *props,[eventSet] NSSet< NSString * > *eventSet,[lepusEventSet] nullable NSSet< NSString * > *lepusEventSet,[gestureDetectorSet] nullable NSSet< LynxGestureDetectorDarwin * > *gestureDetectorSet);
  public void LynxUIOwner::insertNode:toParent:atIndex:(NSInteger childSign,[toParent] NSInteger parentSign,[atIndex] NSInteger index);
  public void LynxUIOwner::listWillReuseNode:withItemKey:(NSInteger sign,[withItemKey] NSString *itemKey);
  public void LynxUIOwner::listCellWillAppear:withItemKey:(NSInteger sign,[withItemKey] NSString *itemKey);
  public void LynxUIOwner::ListCellDisappear:exist:withItemKey:(NSInteger sign,[exist] BOOL isExist,[withItemKey] NSString *itemKey);
  public void LynxUIOwner::detachNode:(NSInteger sign);
  public void LynxUIOwner::recycleNode:(NSInteger sign);
  public void LynxUIOwner::updateUI:layoutLeft:top:width:height:padding:border:margin:sticky:(NSInteger sign,[layoutLeft] CGFloat left,[top] CGFloat top,[width] CGFloat width,[height] CGFloat height,[padding] UIEdgeInsets padding,[border] UIEdgeInsets border,[margin] UIEdgeInsets margin,[sticky] nullable NSArray *sticky);
  public void LynxUIOwner::updateUI:layoutLeft:top:width:height:padding:border:(NSInteger sign,[layoutLeft] CGFloat left,[top] CGFloat top,[width] CGFloat width,[height] CGFloat height,[padding] UIEdgeInsets padding,[border] UIEdgeInsets border);
  public void LynxUIOwner::invokeUIMethod:params:callback:fromRoot:toNodes:(NSString *method,[params] NSDictionary *params,[callback] LynxUIMethodCallbackBlock callback,[fromRoot] NSString *componentId,[toNodes] NSArray *nodes);
  public void LynxUIOwner::invokeUIMethodForSelectorQuery:params:callback:toNode:(NSString *method,[params] NSDictionary *params,[callback] LynxUIMethodCallbackBlock callback,[toNode] int sign);
  public void LynxUIOwner::willContainerViewMoveToWindow:(UIWindow *window);
  public void LynxUIOwner::onReceiveUIOperation:onUI:(id value,[onUI] NSInteger sign);
  public void LynxUIOwner::layoutDidFinish();
  public void LynxUIOwner::finishLayoutOperation:componentID:(int64_t operationID,[componentID] NSInteger componentID);
  public void LynxUIOwner::onNodeReady:(NSInteger sign);
  public void LynxUIOwner::onNodeRemoved:(NSInteger sign);
  public void LynxUIOwner::onNodeReload:(NSInteger sign);
  public nullable LynxUI * LynxUIOwner::uiWithName:(NSString *name);
  public nullable LynxUI * LynxUIOwner::uiWithIdSelector:(NSString *idSelector);
  public nullable LynxWeakProxy * LynxUIOwner::weakLynxUIWithName:(NSString *name);
  public void LynxUIOwner::reset();
  public void LynxUIOwner::pauseRootLayoutAnimation();
  public void LynxUIOwner::resumeRootLayoutAnimation();
  public void LynxUIOwner::resetAnimation();
  public void LynxUIOwner::restartAnimation();
  public void LynxUIOwner::resumeAnimation();
  public void LynxUIOwner::onEnterForeground();
  public void LynxUIOwner::onEnterBackground();
  public void LynxUIOwner::registerForegroundListener:(id< LynxForegroundProtocol > listener);
  public void LynxUIOwner::unRegisterForegroundListener:(id< LynxForegroundProtocol > listener);
  public void LynxUIOwner::updateFontFaceWithDictionary:(NSDictionary *dic);
  public LynxComponentScopeRegistry * LynxUIOwner::getComponentRegistry();
  public void LynxUIOwner::didMoveToWindow:(BOOL windowIsNil);
  public void LynxUIOwner::updateAnimationKeyframes:(NSDictionary *keyframesDict);
  public NSArray< LynxUI * > * LynxUIOwner::uiWithA11yID:(NSString *a11yID);
  public NSArray< UIView * > * LynxUIOwner::viewsWithA11yID:(NSString *a11yID);
  public NSInteger LynxUIOwner::getTagInfo:(NSString *tagName);
  public void LynxUIOwner::updateScrollInfo:estimatedOffset:smooth:scrolling:(NSInteger containerID,[estimatedOffset] float estimatedOffset,[smooth] bool smooth,[scrolling] bool scrolling);
  public void LynxUIOwner::insertListComponent:componentSign:(NSInteger listSign,[componentSign] NSInteger componentSign);
  public void LynxUIOwner::removeListComponent:componentSign:(NSInteger listSign,[componentSign] NSInteger componentSign);
  public void LynxUIOwner::updateContentOffsetForListContainer:contentSize:deltaX:deltaY:(NSInteger containerID,[contentSize] float contentSize,[deltaX] float deltaX,[deltaY] float deltaY);
  public void LynxUIOwner::initNewGestureInUIThread:(BOOL enableNewGesture);
  public NSInteger LynxUIOwner::getRootSign();
  public void LynxUIOwner(Accessibility)::addA11yMutation:sign:a11yID:toArray:(NSString *_Nonnull action, [sign] NSNumber *_Nonnull sign, [a11yID] NSString *_Nullable a11yID, [toArray] NSMutableArray *array);
  public void LynxUIOwner(Accessibility)::addA11yPropsMutation:sign:a11yID:toArray:(NSString *_Nonnull property, [sign] NSNumber *_Nonnull sign, [a11yID] NSString *_Nullable a11yID, [toArray] NSMutableArray *array);
  public void LynxUIOwner(Accessibility)::flushMutations:withBodyView:(NSMutableArray *array, [withBodyView] id< LUIBodyView > _Nullable lynxView);
  public void LynxUIOwner(Accessibility)::listenAccessibilityFocused();
  public void LynxUIOwner(Accessibility)::setA11yFilter:(NSSet< NSString * > *filter);
  public BOOL LynxUIOwner(Accessibility)::checkNestedAccessibilityElements:(LynxUI *subTree);
  public Class LynxUIOwner(Private)::getTargetClass:props:supportedState:(NSString *tagName, [props] NSDictionary *props, [supportedState] TagSupportedState *state);
  public BOOL LynxUIOwner(Private)::needCreateUIAsync:(NSString *tagName);
  public LynxUI * LynxUIOwner(Private)::createUIWithClass:supportedState:onMainThread:(Class clazz, [supportedState] TagSupportedState state, [onMainThread] BOOL onMainThread);
  public void LynxUIOwner(Private)::createUISyncWithSign:tagName:clazz:supportedState:eventSet:lepusEventSet:props:nodeIndex:gestureDetectorSet:(NSInteger sign, [tagName] NSString *tagName, [clazz] Class clazz, [supportedState] TagSupportedState state, [eventSet] NSSet< NSString * > *eventSet, [lepusEventSet] NSSet< NSString * > *lepusEventSet, [props] NSDictionary *props, [nodeIndex] uint32_t nodeIndex, [gestureDetectorSet] NSSet< LynxGestureDetectorDarwin * > *gestureDetectorSet);
  public LynxUI * LynxUIOwner(Private)::createUIAsyncWithSign:tagName:clazz:supportedState:eventSet:lepusEventSet:props:nodeIndex:gestureDetectorSet:(NSInteger sign, [tagName] NSString *tagName, [clazz] Class clazz, [supportedState] TagSupportedState state, [eventSet] NSSet< NSString * > *eventSet, [lepusEventSet] NSSet< NSString * > *lepusEventSet, [props] NSDictionary *props, [nodeIndex] uint32_t nodeIndex, [gestureDetectorSet] NSSet< LynxGestureDetectorDarwin * > *gestureDetectorSet);
  public void LynxUIOwner(Private)::processUIOnMainThread:withSign:tagName:props:(LynxUI *ui, [withSign] NSInteger sign, [tagName] NSString *tagName, [props] NSDictionary *props);
}

public interface LynxUIOwner(Accessibility) {
  public void LynxUIOwner(Accessibility)::addA11yMutation:sign:a11yID:toArray:(NSString *_Nonnull action,[sign] NSNumber *_Nonnull sign,[a11yID] NSString *_Nullable a11yID,[toArray] NSMutableArray *array);
  public void LynxUIOwner(Accessibility)::addA11yPropsMutation:sign:a11yID:toArray:(NSString *_Nonnull property,[sign] NSNumber *_Nonnull sign,[a11yID] NSString *_Nullable a11yID,[toArray] NSMutableArray *array);
  public void LynxUIOwner(Accessibility)::flushMutations:withBodyView:(NSMutableArray *array,[withBodyView] id< LUIBodyView > _Nullable lynxView);
  public void LynxUIOwner(Accessibility)::listenAccessibilityFocused();
  public void LynxUIOwner(Accessibility)::setA11yFilter:(NSSet< NSString * > *filter);
  public BOOL LynxUIOwner(Accessibility)::checkNestedAccessibilityElements:(LynxUI *subTree);
}

public interface LynxUIOwner(Private) {
  public Class LynxUIOwner(Private)::getTargetClass:props:supportedState:(NSString *tagName,[props] NSDictionary *props,[supportedState] TagSupportedState *state);
  public BOOL LynxUIOwner(Private)::needCreateUIAsync:(NSString *tagName);
  public LynxUI * LynxUIOwner(Private)::createUIWithClass:supportedState:onMainThread:(Class clazz,[supportedState] TagSupportedState state,[onMainThread] BOOL onMainThread);
  public void LynxUIOwner(Private)::createUISyncWithSign:tagName:clazz:supportedState:eventSet:lepusEventSet:props:nodeIndex:gestureDetectorSet:(NSInteger sign,[tagName] NSString *tagName,[clazz] Class clazz,[supportedState] TagSupportedState state,[eventSet] NSSet< NSString * > *eventSet,[lepusEventSet] NSSet< NSString * > *lepusEventSet,[props] NSDictionary *props,[nodeIndex] uint32_t nodeIndex,[gestureDetectorSet] NSSet< LynxGestureDetectorDarwin * > *gestureDetectorSet);
  public LynxUI * LynxUIOwner(Private)::createUIAsyncWithSign:tagName:clazz:supportedState:eventSet:lepusEventSet:props:nodeIndex:gestureDetectorSet:(NSInteger sign,[tagName] NSString *tagName,[clazz] Class clazz,[supportedState] TagSupportedState state,[eventSet] NSSet< NSString * > *eventSet,[lepusEventSet] NSSet< NSString * > *lepusEventSet,[props] NSDictionary *props,[nodeIndex] uint32_t nodeIndex,[gestureDetectorSet] NSSet< LynxGestureDetectorDarwin * > *gestureDetectorSet);
  public void LynxUIOwner(Private)::processUIOnMainThread:withSign:tagName:props:(LynxUI *ui,[withSign] NSInteger sign,[tagName] NSString *tagName,[props] NSDictionary *props);
}

public protocol LynxUIRendererProtocol-p : <NSObject> {
  public BOOL LynxUIRendererProtocol-p::useInvokeUIMethodFunction useInvokeUIMethodFunction;
  public void LynxUIRendererProtocol-p::attachLynxView:(LynxView *lynxView);
  public void LynxUIRendererProtocol-p::onSetupUIDelegate:(lynx::tasm::UIDelegate *uiDelegate);
  public void LynxUIRendererProtocol-p::onSetupUIDelegate:withModuleManager:withJSProxy:(lynx::shell::LynxShell *shell,[withModuleManager] lynx::piper::LynxModuleManager *moduleManager,[withJSProxy] std::shared_ptr< lynx::shell::LynxRuntimeProxy > jsProxy);
  public lynx::tasm::UIDelegate * LynxUIRendererProtocol-p::uiDelegate();
  public void LynxUIRendererProtocol-p::setupEventHandler:engineProxy:lynxView:context:shellPtr:(LynxTemplateRender *templateRenderer,[engineProxy] LynxEngineProxy *engineProxy,[lynxView] LynxView *lynxView,[context] LynxContext *context,[shellPtr] int64_t shellPtr);
  public void LynxUIRendererProtocol-p::setPageConfig:context:(const std::shared_ptr< lynx::tasm::PageConfig > &pageConfig,[context] LynxContext *context);
  public void LynxUIRendererProtocol-p::setFluencyTracerEnabled:(LynxBooleanOption enabled);
  public BOOL LynxUIRendererProtocol-p::needPaintingContextProxy();
  public void LynxUIRendererProtocol-p::onSetFrame:(CGRect frame);
  public nullable LynxUIIntersectionObserverManager * LynxUIRendererProtocol-p::getLynxUIIntersectionObserverManager();
  public BOOL LynxUIRendererProtocol-p::needHandleHitTest();
  public UIView * LynxUIRendererProtocol-p::hitTest:withEvent:(CGPoint point,[withEvent] UIEvent *event);
  public id< LynxEventTarget > LynxUIRendererProtocol-p::hitTestInEventHandler:withEvent:(CGPoint point,[withEvent] UIEvent *event);
  public void LynxUIRendererProtocol-p::handleFocus:onView:withContainer:andPoint:andEvent:(id< LynxEventTarget > target,[onView] UIView *view,[withContainer] UIView *container,[andPoint] CGPoint point,[andEvent] UIEvent *event);
  public UIView * LynxUIRendererProtocol-p::eventHandlerRootView();
  public LynxUIOwner * LynxUIRendererProtocol-p::uiOwner();
  public LynxRootUI * LynxUIRendererProtocol-p::rootUI();
  public void LynxUIRendererProtocol-p::setupWithContainerView:templateRenderer:builder:screenSize:(LynxView *containerView,[templateRenderer] LynxTemplateRender *templateRenderer,[builder] LynxViewBuilder *builder,[screenSize] CGSize screenSize);
  public void LynxUIRendererProtocol-p::setLynxContext:(LynxContext *context);
  public void LynxUIRendererProtocol-p::setEnableGenericResourceFetcher:(BOOL enable);
  public id< LynxTemplateResourceFetcher > LynxUIRendererProtocol-p::templateResourceFetcher();
  public id< LynxGenericResourceFetcher > LynxUIRendererProtocol-p::genericResourceFetcher();
  public void LynxUIRendererProtocol-p::setupResourceProvider:withBuilder:(id< LynxResourceProvider > resourceProvider,[withBuilder] LynxViewBuilder *builder);
  public void LynxUIRendererProtocol-p::reset();
  public LynxScreenMetrics * LynxUIRendererProtocol-p::getScreenMetrics();
  public LynxGestureArenaManager * LynxUIRendererProtocol-p::getGestureArenaManager();
  public void LynxUIRendererProtocol-p::onEnterForeground();
  public void LynxUIRendererProtocol-p::onEnterBackground();
  public void LynxUIRendererProtocol-p::willMoveToWindow:(UIWindow *newWindow);
  public void LynxUIRendererProtocol-p::didMoveToWindow:(BOOL windowIsNil);
  public void LynxUIRendererProtocol-p::setCustomizedLayoutInUIContext:(id< LynxListLayoutProtocol > _Nullable customizedListLayout);
  public void LynxUIRendererProtocol-p::setScrollListener:(id< LynxScrollListener > scrollListener);
  public void LynxUIRendererProtocol-p::setImageFetcherInUIOwner:(id< LynxImageFetcher > imageFetcher);
  public void LynxUIRendererProtocol-p::setResourceFetcherInUIOwner:(id< LynxResourceFetcher > resourceFetcher);
  public void LynxUIRendererProtocol-p::updateScreenWidth:height:(CGFloat width,[height] CGFloat height);
  public void LynxUIRendererProtocol-p::pauseRootLayoutAnimation();
  public void LynxUIRendererProtocol-p::resumeRootLayoutAnimation();
  public void LynxUIRendererProtocol-p::restartAnimation();
  public void LynxUIRendererProtocol-p::resetAnimation();
  public void LynxUIRendererProtocol-p::invokeUIMethodForSelectorQuery:params:callback:toNode:(NSString *method,[params] NSDictionary *params,[callback] LynxUIMethodCallbackBlock callback,[toNode] int sign);
  public LynxUI * LynxUIRendererProtocol-p::findUIBySign:(NSInteger sign);
  public nullable UIView * LynxUIRendererProtocol-p::findViewWithName:(nonnull NSString *name);
  public nullable LynxUI * LynxUIRendererProtocol-p::uiWithName:(nonnull NSString *name);
  public nullable LynxUI * LynxUIRendererProtocol-p::uiWithIdSelector:(nonnull NSString *idSelector);
  public nullable UIView * LynxUIRendererProtocol-p::viewWithIdSelector:(nonnull NSString *idSelector);
  public nullable UIView * LynxUIRendererProtocol-p::viewWithName:(nonnull NSString *name);
}

public protocol LynxUIReportInfoDelegate-p : <NSObject> {
  public NSDictionary * LynxUIReportInfoDelegate-p::reportUserInfoOnError();
}

public class LynxUIScroller : AbsLynxUIScroller, <LynxScrollView> {
  public <UIScrollViewDelegate> BOOL LynxUIScroller::enableSticky enableSticky;
  public BOOL LynxUIScroller::enableScrollY enableScrollY;
  public Class<LynxScrollViewUIDelegate> LynxUIScroller::UIDelegate UIDelegate;
  public LynxBounceView* LynxUIScroller::upperBounceUI upperBounceUI;
  public LynxBounceView* LynxUIScroller::lowerBounceUI lowerBounceUI;
  public LynxBounceView* LynxUIScroller::defaultBounceUI defaultBounceUI;
  public BOOL LynxUIScroller::isTransferring isTransferring;
  public NSString* LynxUIScroller::currentItemKey currentItemKey;
  public LynxScrollEventManager* LynxUIScroller::scrollEventManager scrollEventManager;
  public float LynxUIScroller::scrollLeftLimit();
  public float LynxUIScroller::scrollRightLimit();
  public float LynxUIScroller::scrollUpLimit();
  public float LynxUIScroller::scrollDownLimit();
  public void LynxUIScroller::updateContentSize();
}

public protocol LynxUIScrollerDelegate-p : <NSObject> {
  public void LynxUIScrollerDelegate-p::scrollerDidScroll:(UIScrollView *scrollView);
  public void LynxUIScrollerDelegate-p::scrollerWillBeginDragging:(UIScrollView *scrollView);
  public void LynxUIScrollerDelegate-p::scrollerDidEndDragging:willDecelerate:(UIScrollView *scrollView,[willDecelerate] BOOL decelerate);
  public void LynxUIScrollerDelegate-p::scrollerDidEndDecelerating:(UIScrollView *scrollView);
  public void LynxUIScrollerDelegate-p::scrollerDidEndScrollingAnimation:(UIScrollView *scrollView);
}

public class LynxUIScrollerUnitTestUtils : LynxUIUnitTestUtils {
  public void LynxUIScrollerUnitTestUtils::mockBounceView:direction:triggerBounceDistance:size:(LynxUIMockContext *context,[direction] NSString *direction,[triggerBounceDistance] CGFloat distance,[size] CGSize size);
  public void LynxUIScrollerUnitTestUtils::mockChildren:context:scrollY:size:(NSInteger count,[context] LynxUIMockContext *context,[scrollY] BOOL enableScrollY,[size] CGSize size);
}

public protocol LynxUITarget-p : <NSObject> {
  public void LynxUITarget-p::targetOnScreen();
  public void LynxUITarget-p::freeMemoryCache();
  public void LynxUITarget-p::targetOffScreen();
}

public class LynxUIText : LynxUI, <LynxTextView> {
  public LynxTextRenderer* LynxUIText::renderer renderer;
  public CGPoint LynxUIText::overflowLayerOffset overflowLayerOffset;
  public CALayer * LynxUIText::getOverflowLayer();
}

public class LynxUIUnitTestUtils : NSObject {
  public LynxUIMockContext * LynxUIUnitTestUtils::initUIMockContextWithUI:(LynxUI *ui);
  public LynxUIMockContext * LynxUIUnitTestUtils::updateUIMockContext:sign:tag:eventSet:lepusEventSet:props:(nullable LynxUIMockContext *mockContext,[sign] NSInteger sign,[tag] NSString *tagName,[eventSet] NSSet *eventSet,[lepusEventSet] NSSet *lepusEventSet,[props] nonnull NSDictionary *props);
}

public class LynxUIUnitUtils : NSObject {
  public CGFloat LynxUIUnitUtils::screenScale();
  public CGFloat LynxUIUnitUtils::roundPtToPhysicalPixel:(CGFloat number);
  public void LynxUIUnitUtils::roundRectToPhysicalPixelGrid:(CGRect *rect);
  public void LynxUIUnitUtils::roundInsetsToPhysicalPixelGrid:(UIEdgeInsets *insets);
}

public class LynxUnitTest : XCTestCase {
  public NSData * LynxUnitTest::getNSData:(NSString *fileName);
}

public class LynxUnitUtils : NSObject {
  public CGFloat LynxUnitUtils::toPtFromUnitValue:(NSString *unitValue);
  public CGFloat LynxUnitUtils::toPtFromUnitValue:withDefaultPt:(NSString *unitValue,[withDefaultPt] CGFloat defaultPt);
  public CGFloat LynxUnitUtils::toPtFromIDUnitValue:withDefaultPt:(id unitValue,[withDefaultPt] CGFloat defaultPt);
  public CGFloat LynxUnitUtils::toPtFromUnitValue:rootFontSize:curFontSize:rootWidth:rootHeight:(NSString *unitValue,[rootFontSize] CGFloat rootFontSize,[curFontSize] CGFloat curFontSize,[rootWidth] int rootWidth,[rootHeight] int rootHeight);
  public CGFloat LynxUnitUtils::toPtFromUnitValue:rootFontSize:curFontSize:rootWidth:rootHeight:withDefaultPt:(NSString *unitValue,[rootFontSize] CGFloat rootFontSize,[curFontSize] CGFloat curFontSize,[rootWidth] int rootWidth,[rootHeight] int rootHeight,[withDefaultPt] CGFloat defaultPt);
  public CGFloat LynxUnitUtils::toPtWithScreenMetrics:unitValue:rootFontSize:curFontSize:rootWidth:rootHeight:viewSize:withDefaultPt:(LynxScreenMetrics *screenMetrics,[unitValue] NSString *unitValue,[rootFontSize] CGFloat rootFontSize,[curFontSize] CGFloat curFontSize,[rootWidth] int rootWidth,[rootHeight] int rootHeight,[viewSize] CGFloat viewSize,[withDefaultPt] CGFloat defaultPt);
  public CGFloat LynxUnitUtils::toPtFromUnitValue:rootFontSize:curFontSize:rootWidth:rootHeight:viewSize:withDefaultPt:(NSString *unitValue,[rootFontSize] CGFloat rootFontSize,[curFontSize] CGFloat curFontSize,[rootWidth] int rootWidth,[rootHeight] int rootHeight,[viewSize] CGFloat viewSize,[withDefaultPt] CGFloat defaultPt);
  public CGFloat LynxUnitUtils::toPtWithScreenMetrics:unitValue:rootFontSize:curFontSize:rootWidth:rootHeight:withDefaultPt:(LynxScreenMetrics *screenMetrics,[unitValue] NSString *unitValue,[rootFontSize] CGFloat rootFontSize,[curFontSize] CGFloat curFontSize,[rootWidth] int rootWidth,[rootHeight] int rootHeight,[withDefaultPt] CGFloat defaultPt);
  public CGFloat LynxUnitUtils::toPhysicalPixelFromPt:(CGFloat valuePt);
  public BOOL LynxUnitUtils::isPercentage:(NSString *unitValue);
  public CGFloat LynxUnitUtils::clamp:min:max:(CGFloat value,[min] CGFloat minValue,[max] CGFloat maxValue);
}

public class LynxUpdateMeta : NSObject {
  public LynxTemplateData* LynxUpdateMeta::data data;
  public LynxTemplateData* LynxUpdateMeta::globalProps globalProps;
}

public class LynxURL : NSObject {
  public NSURL* LynxURL::url url;
  public NSURL* LynxURL::redirectedURL redirectedURL;
  public BOOL LynxURL::initiallyLoaded initiallyLoaded;
  public LynxImageRequestType LynxURL::type type;
  public NSURL* LynxURL::lastRequestUrl lastRequestUrl;
  public NSURL* LynxURL::preUrl preUrl;
  public BOOL LynxURL::fromMemoryCache fromMemoryCache;
  public LynxResourceRequest* LynxURL::request request;
  public NSTimeInterval LynxURL::fetchTime fetchTime;
  public NSTimeInterval LynxURL::completeTime completeTime;
  public CGFloat LynxURL::memoryCost memoryCost;
  public NSInteger LynxURL::isSuccess isSuccess;
  public NSError* LynxURL::error error;
  public CGSize LynxURL::imageSize imageSize;
  public NSMutableDictionary* LynxURL::resourceInfo resourceInfo;
  public NSMutableDictionary* LynxURL::reportInfo reportInfo;
  public void LynxURL::updatePreviousUrl();
  public BOOL LynxURL::isPreviousUrl();
  public void LynxURL::initResourceInformation();
  public void LynxURL::updateTimeStamp:startRequestTime:(NSDate *getImageTime,[startRequestTime] NSDate *startRequestTime);
}

public class LynxVerificationResult : NSObject {
  public BOOL LynxVerificationResult::verified verified;
  public NSString* LynxVerificationResult::errorMsg errorMsg;
}

public class LynxVersion : NSObject {
  public NSString * LynxVersion::versionString();
}

public class LynxVersionUtils : NSObject {
  public NSInteger LynxVersionUtils::compareLeft:withRight:(const NSString *left,[withRight] const NSString *right);
}

public class LynxView : UIView, <LUIBodyView> {
  public NSString* LynxView::url url;
  public BOOL LynxView::enableAsyncDisplay enableAsyncDisplay;
  public LynxViewSizeMode LynxView::layoutWidthMode layoutWidthMode;
  public LynxViewSizeMode LynxView::layoutHeightMode layoutHeightMode;
  public CGFloat LynxView::preferredMaxLayoutWidth preferredMaxLayoutWidth;
  public CGFloat LynxView::preferredMaxLayoutHeight preferredMaxLayoutHeight;
  public CGFloat LynxView::preferredLayoutWidth preferredLayoutWidth;
  public CGFloat LynxView::preferredLayoutHeight preferredLayoutHeight;
  public id<LynxImageFetcher> LynxView::imageFetcher imageFetcher;
  public id<LynxScrollListener> LynxView::scrollListener scrollListener;
  public id<LynxListLayoutProtocol> LynxView::customizedListLayout customizedListLayout;
  public id<LynxBaseInspectorOwner> LynxView::baseInspectorOwner baseInspectorOwner;
  public BOOL LynxView::catchAllException catchAllException;
  public NSString* LynxView(Identify)::containerID containerID;
  public NSString* LynxView(Identify)::namescope namescope;
  public nullable id< LynxResourceFetcher > LynxView::resourceFetcher();
  public void LynxView::setResourceFetcher:(nullable id< LynxResourceFetcher > resourceFetcher);
  public nonnull instancetype LynxView::init();
  public nonnull instancetype LynxView::initWithFrame:(CGRect frame);
  public nonnull instancetype LynxView::initWithBuilderBlock:(void(^_Nullable builder)(NS_NOESCAPE LynxViewBuilder *_Nonnull));
  public instancetype _Nullable LynxView::initWithoutRender();
  public void LynxView::initLifecycleDispatcher();
  public void LynxView::clearForDestroy();
  public void LynxView::resetViewAndLayer();
  public void LynxView::loadTemplate:(nonnull LynxLoadMeta *meta);
  public void LynxView::loadTemplateFromURL:(nonnull NSString *url);
  public void LynxView::loadTemplateFromURL:initData:(nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxView::loadTemplate:withURL:(nonnull NSData *tem,[withURL] nonnull NSString *url);
  public void LynxView::loadTemplate:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxView::loadTemplateBundle:withURL:initData:(nonnull LynxTemplateBundle *bundle,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxView::loadSSRData:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxView::loadSSRDataFromURL:initData:(nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxView::ssrHydrate:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxView::ssrHydrateFromURL:initData:(nonnull NSString *url,[initData] nullable LynxTemplateData *data);
  public void LynxView::updateDataWithString:(nullable NSString *data);
  public void LynxView::updateDataWithString:processorName:(nullable NSString *data,[processorName] nullable NSString *name);
  public void LynxView::updateDataWithTemplateData:(nullable LynxTemplateData *data);
  public void LynxView::updateDataWithDictionary:(nullable NSDictionary< NSString *, id > *data);
  public void LynxView::updateDataWithDictionary:processorName:(nullable NSDictionary< NSString *, id > *data,[processorName] nullable NSString *name);
  public void LynxView::updateMetaData:(nonnull LynxUpdateMeta *meta);
  public void LynxView::updateGlobalPropsWithTemplateData:(nonnull LynxTemplateData *data);
  public void LynxView::updateGlobalPropsWithDictionary:(NSDictionary< NSString *, id > *_Nonnull data);
  public void LynxView::resetDataWithTemplateData:(nonnull LynxTemplateData *data);
  public void LynxView::reloadTemplateWithTemplateData:(nonnull LynxTemplateData *data);
  public void LynxView::reloadTemplateWithTemplateData:globalProps:(nonnull LynxTemplateData *data,[globalProps] nullable LynxTemplateData *globalProps);
  public void LynxView::setSessionStorageItem:withTemplateData:(nonnull NSString *key,[withTemplateData] nullable LynxTemplateData *data);
  public void LynxView::getSessionStorageItem:withCallback:(nonnull NSString *key,[withCallback] void(^_Nullable callback)(id< NSObject > _Nullable));
  public double LynxView::subscribeSessionStorage:withCallback:(nonnull NSString *key,[withCallback] void(^_Nullable callback)(id< NSObject > _Nullable));
  public void LynxView::unSubscribeSessionStorage:withId:(nonnull NSString *key,[withId] double callbackId);
  public void LynxView::updateScreenMetricsWithWidth:height:(CGFloat width,[height] CGFloat height);
  public void LynxView::updateFontScale:(CGFloat scale);
  public void LynxView::pauseRootLayoutAnimation();
  public void LynxView::resumeRootLayoutAnimation();
  public void LynxView::resetAnimation();
  public void LynxView::restartAnimation();
  public void LynxView::setTheme:(nonnull LynxTheme *theme);
  public nullable LynxTheme * LynxView::theme();
  public void LynxView::setNeedPendingUIOperation:(BOOL needPendingUIOperation);
  public void LynxView::triggerLayout();
  public nullable LynxUI * LynxView::findUIByIndex:(int index);
  public nullable UIView * LynxView::findViewWithName:(nonnull NSString *name);
  public nullable LynxUI * LynxView::uiWithName:(nonnull NSString *name);
  public nullable LynxUI * LynxView::uiWithIdSelector:(nonnull NSString *idSelector);
  public nullable UIView * LynxView::viewWithIdSelector:(nonnull NSString *idSelector);
  public void LynxView::addLifecycleClient:(nonnull id< LynxViewBaseLifecycle > lifecycleClient);
  public void LynxView::removeLifecycleClient:(nonnull id< LynxViewBaseLifecycle > lifecycleClient);
  public void LynxView::updateViewportWithPreferredLayoutWidth:preferredLayoutHeight:(CGFloat preferredLayoutWidth,[preferredLayoutHeight] CGFloat preferredLayoutHeight);
  public void LynxView::updateViewportWithPreferredLayoutWidth:preferredLayoutHeight:needLayout:(CGFloat preferredLayoutWidth,[preferredLayoutHeight] CGFloat preferredLayoutHeight,[needLayout] BOOL needLayout);
  public void LynxView::setEnableTextNonContiguousLayout:(BOOL enableTextNonContiguousLayout);
  public BOOL LynxView::enableTextNonContiguousLayout();
  public void LynxView::setEnableLayoutOnly:(BOOL enableLayoutOnly);
  public void LynxView::setEnableSyncFlush:(BOOL enableSyncFlush);
  public CGSize LynxView::intrinsicContentSize();
  public LynxLifecycleDispatcher *_Nullable LynxView::getLifecycleDispatcher();
  public void LynxView::onEnterForeground();
  public void LynxView::onEnterBackground();
  public void LynxView::sendGlobalEvent:withParams:(nonnull NSString *name,[withParams] nullable NSArray *params);
  public void LynxView::sendGlobalEventToLepus:withParams:(nonnull NSString *name,[withParams] nullable NSArray *params);
  public void LynxView::triggerEventBus:withParams:(nonnull NSString *name,[withParams] nullable NSArray *params);
  public void LynxView::sendTouchEvent:(nullable LynxTouchEvent *event);
  public NSDictionary *_Nullable LynxView::getPageDataByKey:(nonnull NSArray *keys);
  public nullable JSModule * LynxView::getJSModule:(nonnull NSString *name);
  public nonnull LynxContext * LynxView::getLynxContext();
  public LynxThreadStrategyForRender LynxView::getThreadStrategyForRender();
  public BOOL LynxView::isLayoutFinish();
  public NSDictionary *_Nullable LynxView::getCurrentData();
  public float LynxView::rootWidth();
  public float LynxView::rootHeight();
  public void LynxView::setExtraTiming:(LynxExtraTiming *_Nonnull timing);
  public nullable NSDictionary * LynxView::getAllTimingInfo();
  public void LynxView::setExtraTimingWithDictionary:(NSDictionary *_Nonnull timing);
  public void LynxView::setLongTaskMonitorEnabled:(LynxBooleanOption enabled);
  public void LynxView::setFluencyTracerEnabled:(LynxBooleanOption enabled);
  public void LynxView::putParamsForReportingEvents:(NSDictionary< NSString *, id > *_Nonnull params);
  public void LynxView::runOnTasmThread:(dispatch_block_t _Nonnull task);
  public void LynxView::syncFlush();
  public void LynxView::attachEngineToUIThread();
  public void LynxView::detachEngineFromUIThread();
  public void LynxView::preloadDynamicComponents:("LynxTemplateResourceFetcher", ios(6.0, API_TO_BE_DEPRECATED) API_DEPRECATED_WITH_REPLACEMENT);
  public BOOL LynxView::registerDynamicComponent:bundle:(nonnull NSString *url,[bundle] "LynxTemplateResourceFetcher", ios(6.0, API_TO_BE_DEPRECATED) API_DEPRECATED_WITH_REPLACEMENT);
  public void LynxView::setEnableUserBytecode:url:(BOOL enableUserBytecode,[url] nonnull NSString *url);
  public void LynxView::attachTemplateRender:(LynxTemplateRender *_Nullable templateRender);
  public void LynxView::processRender();
  public void LynxView::detachRender();
  public void LynxView::startLynxRuntime();
  public BOOL isUIRunningMode LynxView::__attribute__((deprecated("try to set 'threadStrategyForRendering' if you want to change the " "thread strategy for rendering")));
  public id< LynxViewClient > client LynxView::__attribute__((deprecated("use xxxxFetcher instead, and use LynxViewLifecycle to " "implement lifecycle methods")));
  public (deprecated("Use `triggerLayout` instead.")) LynxView::__attribute__();
  public void LynxView::setEnableRadonCompatible:((deprecated("Radon diff mode can't be close after lynx 2.3.")) __attribute__);
  public void LynxView::requestLayoutWhenSafepointEnable();
  public void LynxView::setGlobalPropsWithTemplateData:((deprecated("Use `updateGlobalPropsWithTemplateData` instead.")) __attribute__);
  public void LynxView::setGlobalPropsWithDictionary:((deprecated("Use `updateGlobalPropsWithDictionary` instead.")) __attribute__);
  public nullable UIView * LynxView::viewWithName:(nonnull NSString *name);
  public (deprecated("Use `lynxReportInfo` instead.")) LynxView::__attribute__();
  public (deprecated("Use `LynxViewLifecycle didReceivexxx` instead.")) LynxView::__attribute__();
  public void LynxView::processLayout:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData](deprecated("Use `loadTemplate:LynxLoadMeta` with " "LynxLoadOption(LynxLoadOptionProcessLayoutWithoutUIFlush)instead.")) __attribute__);
  public void LynxView::processLayoutWithTemplateBundle:withURL:initData:(nonnull LynxTemplateBundle *bundle,[withURL] nonnull NSString *url,[initData](deprecated("Use `loadTemplate:LynxLoadMeta` with " "LynxLoadOption(LynxLoadOptionProcessLayoutWithoutUIFlush)instead.")) __attribute__);
  public void LynxView::processLayoutWithSSRData:withURL:initData:(nonnull NSData *tem,[withURL] nonnull NSString *url,[initData](deprecated("Use `loadTemplate:LynxLoadMeta` with " "LynxLoadOption(LynxLoadOptionProcessLayoutWithoutUIFlush)instead.")) __attribute__);
  public nonnull LynxConfigInfo * LynxView::lynxConfigInfo();
  public LynxTemplateRender *_Nullable LynxView()::templateRender();
  public NSDictionary *_Nullable LynxView()::getAllJsSource();
  public void LynxView()::onLongPress();
  public void LynxView()::setIntrinsicContentSize:(CGSize size);
  public void LynxView()::dispatchError:(LynxError *error);
}

public interface LynxView(Identify) {
  public NSString* LynxView(Identify)::containerID containerID;
  public NSString* LynxView(Identify)::namescope namescope;
}

public class LynxViewBuilder : NSObject {
  public LynxConfig* LynxViewBuilder::config config;
  public LynxGroup* LynxViewBuilder::group group;
  public LynxBackgroundRuntime* LynxViewBuilder::lynxBackgroundRuntime lynxBackgroundRuntime;
  public BOOL LynxViewBuilder::enableLayoutSafepoint enableLayoutSafepoint;
  public BOOL LynxViewBuilder::enableAutoExpose enableAutoExpose;
  public BOOL LynxViewBuilder::enableTextNonContiguousLayout enableTextNonContiguousLayout;
  public BOOL LynxViewBuilder::enableLayoutOnly enableLayoutOnly;
  public BOOL LynxViewBuilder::enableUIOperationQueue enableUIOperationQueue;
  public BOOL LynxViewBuilder::enablePendingJSTaskOnLayout enablePendingJSTaskOnLayout;
  public BOOL LynxViewBuilder::enableJSRuntime enableJSRuntime;
  public BOOL LynxViewBuilder::enableAirStrictMode enableAirStrictMode;
  public BOOL LynxViewBuilder::enableAsyncCreateRender enableAsyncCreateRender;
  public BOOL LynxViewBuilder::enableRadonCompatible enableRadonCompatible;
  public BOOL LynxViewBuilder::enableSyncFlush enableSyncFlush;
  public BOOL LynxViewBuilder::enableMultiAsyncThread enableMultiAsyncThread;
  public BOOL LynxViewBuilder::enableVSyncAlignedMessageLoop enableVSyncAlignedMessageLoop;
  public BOOL LynxViewBuilder::enableAsyncHydration enableAsyncHydration;
  public CGRect LynxViewBuilder::frame frame;
  public id<LynxDynamicComponentFetcher> LynxViewBuilder::fetcher fetcher;
  public CGFloat LynxViewBuilder::fontScale fontScale;
  public NSMutableDictionary<NSString*, id>* LynxViewBuilder::lynxViewConfig lynxViewConfig;
  public LynxBooleanOption LynxViewBuilder::enableGenericResourceFetcher enableGenericResourceFetcher;
  public id<LynxGenericResourceFetcher> LynxViewBuilder::genericResourceFetcher genericResourceFetcher;
  public id<LynxMediaResourceFetcher> LynxViewBuilder::mediaResourceFetcher mediaResourceFetcher;
  public id<LynxTemplateResourceFetcher> LynxViewBuilder::templateResourceFetcher templateResourceFetcher;
  public id LynxViewBuilder::lynxModuleExtraData lynxModuleExtraData;
  public CGSize LynxViewBuilder::screenSize screenSize;
  public BOOL LynxViewBuilder::debuggable debuggable;
  public BOOL LynxViewBuilder::enablePreUpdateData enablePreUpdateData;
  public BOOL LynxViewBuilder::enableLynxResourceServiceLoaderInjection enableLynxResourceServiceLoaderInjection;
  public LynxBackgroundJsRuntimeType LynxViewBuilder::backgroundJsRuntimeType backgroundJsRuntimeType;
  public BOOL LynxViewBuilder::enableBytecode enableBytecode;
  public NSString* LynxViewBuilder::bytecodeUrl bytecodeUrl;
  public LynxBackgroundRuntimeOptions* LynxViewBuilder()::lynxBackgroundRuntimeOptions lynxBackgroundRuntimeOptions;
  public id<LynxUIRendererProtocol> LynxViewBuilder()::lynxUIRenderer lynxUIRenderer;
  public BOOL isUIRunningMode LynxViewBuilder::__attribute__((deprecated("try to set 'threadStrategy' variable if you want to change the thread strategy for rendering")));
  public void LynxViewBuilder::setThreadStrategyForRender:(LynxThreadStrategyForRender threadStrategy);
  public LynxThreadStrategyForRender LynxViewBuilder::getThreadStrategyForRender();
  public void LynxViewBuilder::addLynxResourceProvider:provider:(NSString *_Nonnull resType,[provider] id< LynxResourceProvider > _Nonnull provider);
  public void LynxViewBuilder::registerFont:forName:(UIFont *_Nonnull font,[forName] NSString *_Nonnull name);
  public void LynxViewBuilder::registerFamilyName:withAliasName:(NSString *_Nonnull fontFamilyName,[withAliasName] NSString *_Nonnull aliasName);
  public NSDictionary *_Nonnull LynxViewBuilder()::getLynxResourceProviders();
  public NSDictionary *_Nonnull LynxViewBuilder()::getBuilderRegisteredAliasFontMap();
}

public class LynxViewConfigProcessor : NSObject {
  public void LynxViewConfigProcessor::processorMap:lynxViewBuilder:(NSMutableDictionary *dictionary,[lynxViewBuilder] LynxViewBuilder *lynxViewBuilder);
}

public protocol LynxViewLifecycle-p : <NSObject>, <LynxTimingListener>, <LynxJSBTimingListener>, <LynxViewBaseLifecycle> {
  public void LynxViewLifecycle-p::lynxView:reportResourceInfo:eventType:(LynxView *lynxView,[reportResourceInfo] NSDictionary *info,[eventType] NSString *eventType);
  public void LynxViewLifecycle-p::lynxView:didInvokeMethod:inModule:errorCode:(LynxView *view,[didInvokeMethod] NSString *method,[inModule] NSString *module,[errorCode] int code);
  public void LynxViewLifecycle-p::lynxViewDidStartLoading:(LynxView *view);
  public void LynxViewLifecycle-p::lynxView:didLoadFinishedWithUrl:(LynxView *view,[didLoadFinishedWithUrl] NSString *url);
  public void LynxViewLifecycle-p::lynxView:didLoadFinishedWithConfigInfo:(LynxView *view,[didLoadFinishedWithConfigInfo] "This callback will not be invoked, use `didLoadFinishedWithUrl` instead" __deprecated_msg);
  public void LynxViewLifecycle-p::lynxViewDidFirstScreen:(LynxView *view);
  public void LynxViewLifecycle-p::lynxViewDidPageUpdate:(LynxView *view);
  public void LynxViewLifecycle-p::lynxViewDidConstructJSRuntime:(LynxView *view);
  public void LynxViewLifecycle-p::lynxViewDidUpdate:(LynxView *view);
  public void LynxViewLifecycle-p::lynxViewDidChangeIntrinsicContentSize:(LynxView *view);
  public void LynxViewLifecycle-p::lynxViewOnTasmFinishByNative:(LynxView *view);
  public NSURL * LynxViewLifecycle-p::shouldRedirectImageUrl:((deprecated("Use loadImage:size:completion: to load image.")) __attribute__);
  public void LynxViewLifecycle-p::lynxView:didLoadFailedWithUrl:error:(LynxView *view,[didLoadFailedWithUrl] NSString *url,[error](deprecated("Use `lynxView:didRecieveError:`.")) __attribute__);
  public void LynxViewLifecycle-p::lynxView:didRecieveError:(LynxView *view,[didRecieveError] NSError *error);
  public void LynxViewLifecycle-p::lynxView:didReceiveFirstLoadPerf:(LynxView *view,[didReceiveFirstLoadPerf] LynxPerformance *perf);
  public void LynxViewLifecycle-p::lynxView:didReceiveUpdatePerf:(LynxView *view,[didReceiveUpdatePerf] LynxPerformance *perf);
  public void LynxViewLifecycle-p::lynxView:didReceiveDynamicComponentPerf:(LynxView *view,[didReceiveDynamicComponentPerf] "Will be provided by TimingObserver", ios(6.0, API_TO_BE_DEPRECATED) API_DEPRECATED);
  public void LynxViewLifecycle-p::lynxView:didReportComponentInfo:(LynxView *view,[didReportComponentInfo] NSSet< NSString * > *componentSet);
  public void LynxViewLifecycle-p::onLynxEvent:(LynxEventDetail *event);
  public void LynxViewLifecycle-p::onPiperInvoked:(NSDictionary *info);
  public void LynxViewLifecycle-p::onPiperResponsed:(NSDictionary *info);
  public void LynxViewLifecycle-p::onTemplateBundleReady:(LynxTemplateBundle *bundle);
}

public protocol LynxViewLifecycleV2-p : <LynxViewBaseLifecycle> {
  public void LynxViewLifecycleV2-p::onPageStartedWithLynxView:withPipelineInfo:(nonnull LynxView *lynxView,[withPipelineInfo] nonnull LynxPipelineInfo *info);
  public void LynxViewLifecycleV2-p::onPerformanceEvent:(nonnull LynxPerformanceEntry *entry);
}

public protocol LynxViewStateListener-p : <NSObject> {
  public void LynxViewStateListener-p::onLoadFinished();
  public void LynxViewStateListener-p::onMovedToWindow();
  public void LynxViewStateListener-p::onEnterForeground();
  public void LynxViewStateListener-p::onEnterBackground();
  public void LynxViewStateListener-p::onDestroy();
}

public protocol LynxViewVisibleHelper-p : <NSObject> {
  public BOOL LynxViewVisibleHelper-p::IsViewVisible();
}

public class LynxWasmFuncRegistry : NSObject {
  public void LynxWasmFuncRegistry::registerWasmFunc:(void *func);
}

public class LynxWeakProxy : NSObject {
  public id LynxWeakProxy::target target;
  public instancetype LynxWeakProxy::proxyWithTarget:(id target);
}

public class MeasureContext : NSObject {
  public CGFloat MeasureContext()::rootWidth rootWidth;
  public LynxMeasureMode MeasureContext()::rootWidthMode rootWidthMode;
  public CGFloat MeasureContext()::rootHeight rootHeight;
  public LynxMeasureMode MeasureContext()::rootHeightMode rootHeightMode;
  public BOOL MeasureContext()::finalMeasure finalMeasure;
}

public class MeasureParam : NSObject {
  public CGFloat MeasureParam::width width;
  public LynxMeasureMode MeasureParam::widthMode widthMode;
  public CGFloat MeasureParam::height height;
  public LynxMeasureMode MeasureParam::heightMode heightMode;
  public id MeasureParam::initWithWidth:WdithMode:Height:HeightMode:(CGFloat width,[WdithMode] LynxMeasureMode widthMode,[Height] CGFloat height,[HeightMode](deprecated("Use -[initWithWidth:WidthMode:Height:HeightMode] instead.")) __attribute__);
  public id MeasureParam::initWithWidth:WidthMode:Height:HeightMode:(CGFloat width,[WidthMode] LynxMeasureMode widthMode,[Height] CGFloat height,[HeightMode] LynxMeasureMode heightMode);
}

public struct MeasureResult {
  public CGSize MeasureResult::size size;
  public CGFloat MeasureResult::baseline baseline;
}

public protocol MessageHandler-p : <NSObject> {
  public void MessageHandler-p::onMessage:(nonnull NSString *message);
}

public class NavigationModule : NSObject, <LynxModule> {
  public void NavigationModule::registerRoute:(NSDictionary *routeTable);
  public void NavigationModule::navigateTo:param:(NSString *url,[param] NSDictionary *param);
  public void NavigationModule::replace:param:(NSString *url,[param] NSDictionary *param);
  public void NavigationModule::goBack();
}

public interface NSString(LynxHtmlEscape) {
  public NSString * NSString(LynxHtmlEscape)::stringByUnescapingFromHtml();
}

public class OptimizedSyncedTimeHelper : NSObject {
  public CFTimeInterval OptimizedSyncedTimeHelper::getSyncedTime();
  public instancetype OptimizedSyncedTimeHelper::sharedInstance();
}

public protocol OverlayService-p : <NSObject> {
  public NSArray< NSNumber * > * OverlayService-p::getAllVisibleOverlaySign();
}

public class PaintingContextProxy : NSObject, <LynxShadowNodeDelegate> {
  public instancetype PaintingContextProxy::initWithPaintingContext:(lynx::tasm::PaintingContextDarwin *paintingContext);
  public BOOL PaintingContextProxy::isLayoutFinish();
  public void PaintingContextProxy::resetLayoutStatus();
}

public class PathLengthCache : NSObject {
  public CGFloat PathLengthCache::totalLength totalLength;
  public NSMutableArray<NSNumber *>* PathLengthCache::segmentLengths segmentLengths;
}

public class ShareElementPair : NSObject {
  public UIView* ShareElementPair::fromView fromView;
  public UIView* ShareElementPair::toView toView;
  public UIView* ShareElementPair::originParent originParent;
  public CGRect ShareElementPair::originFrame originFrame;
  public CGPoint ShareElementPair::originPoint originPoint;
  public CGRect ShareElementPair::originBounds originBounds;
  public NSTimeInterval ShareElementPair::duration duration;
  public BOOL ShareElementPair::crossPage crossPage;
  public instancetype ShareElementPair::initWithFrom:to:(UIView *from,[to] UIView *to);
  public void ShareElementPair::backToOrigin();
}

public class SyncedTimeHelper : NSObject {
  public CFTimeInterval SyncedTimeHelper::getSyncedTime();
  public instancetype SyncedTimeHelper::sharedInstance();
}

public protocol TemplateRenderCallbackProtocol-p : <LynxErrorReceiverProtocol> {
  public void TemplateRenderCallbackProtocol-p::onDataUpdated();
  public void TemplateRenderCallbackProtocol-p::onPageChanged:(BOOL isFirstScreen);
  public void TemplateRenderCallbackProtocol-p::onTasmFinishByNative();
  public void TemplateRenderCallbackProtocol-p::onTemplateLoaded:(NSString *url);
  public void TemplateRenderCallbackProtocol-p::onRuntimeReady();
  public void TemplateRenderCallbackProtocol-p::onErrorOccurred:message:(NSInteger code,[message] NSString *errMessage);
  public void TemplateRenderCallbackProtocol-p::didInvokeMethod:inModule:errorCode:(NSString *method,[inModule] NSString *module,[errorCode] int32_t code);
  public void TemplateRenderCallbackProtocol-p::onTimingSetup:(NSDictionary *timingInfo);
  public void TemplateRenderCallbackProtocol-p::onTimingUpdate:updateTiming:(NSDictionary *timingInfo,[updateTiming] NSDictionary *updateTiming);
  public void TemplateRenderCallbackProtocol-p::onPerformanceEvent:(NSDictionary *originDict);
  public void TemplateRenderCallbackProtocol-p::onFirstLoadPerf:(NSDictionary *perf);
  public void TemplateRenderCallbackProtocol-p::onUpdatePerfReady:(NSDictionary *perf);
  public void TemplateRenderCallbackProtocol-p::onDynamicComponentPerf:(NSDictionary *perf);
  public void TemplateRenderCallbackProtocol-p::setPageConfig:(const std::shared_ptr< lynx::tasm::PageConfig > &pageConfig);
  public void TemplateRenderCallbackProtocol-p::setTiming:key:pipelineID:(uint64_t timestamp,[key] NSString *key,[pipelineID] nullable NSString *pipelineID);
  public NSString * TemplateRenderCallbackProtocol-p::translatedResourceWithId:themeKey:(NSString *resId,[themeKey] NSString *key);
  public void TemplateRenderCallbackProtocol-p::getI18nResourceForChannel:withFallbackUrl:(NSString *channel,[withFallbackUrl] NSString *url);
  public void TemplateRenderCallbackProtocol-p::invokeLepusFunc:callbackID:(NSDictionary *data,[callbackID] int32_t callbackID);
  public void TemplateRenderCallbackProtocol-p::onCallJSBFinished:(NSDictionary *info);
  public void TemplateRenderCallbackProtocol-p::onJSBInvoked:(NSDictionary *info);
  public void TemplateRenderCallbackProtocol-p::onReceiveMessageEvent:(NSDictionary *event);
  public long TemplateRenderCallbackProtocol-p::initStartTiming();
  public long TemplateRenderCallbackProtocol-p::initEndTiming();
  public BOOL TemplateRenderCallbackProtocol-p::enableAirStrictMode();
  public void TemplateRenderCallbackProtocol-p::invokeUIMethod:params:callback:toNode:(NSString *_Nonnull method_string,[params] NSDictionary *_Nonnull params,[callback] int callback,[toNode] int node);
  public void TemplateRenderCallbackProtocol-p::onSSRHydrateFinished:(NSString *url);
  public void TemplateRenderCallbackProtocol-p::onTemplateBundleReady:(LynxTemplateBundle *bundle);
  public void TemplateRenderCallbackProtocol-p::setLocalTheme:(LynxTheme *theme);
  public LynxContext * TemplateRenderCallbackProtocol-p::getLynxContext();
  public NSMutableDictionary< NSString *, id > * TemplateRenderCallbackProtocol-p::getLepusModulesClasses();
  public void TemplateRenderCallbackProtocol-p::onReloadTemplate:withURL:initData:(const std::vector< uint8_t > &data,[withURL] const std::string &url,[initData] const std::shared_ptr< lynx::tasm::TemplateData > &init_data);
  public void TemplateRenderCallbackProtocol-p::onEventCapture:withEventCatch:andEventID:(NSInteger targetID,[withEventCatch] BOOL isCatch,[andEventID] int64_t eventID);
  public void TemplateRenderCallbackProtocol-p::onEventBubble:withEventCatch:andEventID:(NSInteger targetID,[withEventCatch] BOOL isCatch,[andEventID] int64_t eventID);
  public void TemplateRenderCallbackProtocol-p::onEventFire:withEventStop:andEventID:(NSInteger targetID,[withEventStop] BOOL isStop,[andEventID] int64_t eventID);
}

public struct TransOffset {
  public CGPoint TransOffset::left_top left_top;
  public CGPoint TransOffset::right_top right_top;
  public CGPoint TransOffset::right_bottom right_bottom;
  public CGPoint TransOffset::left_bottom left_bottom;
}

public interface UIDevice(Lynx) {
  public BOOL UIDevice(Lynx)::lynx_isIPhoneX();
}

public interface UIScrollView(Lynx) {
  public BOOL UIScrollView(Lynx)::scrollEnableFromLynx scrollEnableFromLynx;
  public void UIScrollView(Lynx)::setLynxListAdjustingContentOffset:(BOOL value);
  public BOOL UIScrollView(Lynx)::isLynxListAdjustingContentOffset();
  public CGPoint UIScrollView(Lynx)::targetContentOffset:withScrollingVelocity:withVisibleItems:getIndexFromView:getViewRectAtIndex:vertical:rtl:factor:offset:callback:(CGPoint proposedContentOffset,[withScrollingVelocity] CGPoint velocity,[withVisibleItems] NSArray< UIView * > *visibleItems,[getIndexFromView] UIScrollViewGetIndexFromView getIndexFromView,[getViewRectAtIndex] UIScrollViewGetViewRectAtIndex getViewRectAtIndex,[vertical] BOOL vertical,[rtl] BOOL rtl,[factor] CGFloat factor,[offset] CGFloat offset,[callback] UIScrollViewWillSnapToCallback callback);
  public BOOL UIScrollView(Lynx)::consumeDeltaOffset:vertical:(CGPoint delta,[vertical] BOOL vertical);
  public CGPoint UIScrollView(Lynx)::updateContentOffset:vertical:(CGPoint contentOffset,[vertical] BOOL vertical);
  public void UIScrollView(Lynx)::setContentOffset:behavior:duration:interval:progress:complete:(CGPoint contentOffset,[behavior] LynxScrollViewTouchBehavior behavior,[duration] NSTimeInterval duration,[interval] NSTimeInterval interval,[progress] _Nullable UIScrollViewLynxProgressInterception interception,[complete] _Nullable UIScrollViewLynxCompletion callback);
  public void UIScrollView(Lynx)::scrollToTargetContentOffset:behavior:duration:interval:complete:(CGPoint contentOffset,[behavior] LynxScrollViewTouchBehavior behavior,[duration] NSTimeInterval duration,[interval] NSTimeInterval interval,[complete] _Nullable UIScrollViewLynxCompletion callback);
  public void UIScrollView(Lynx)::autoScrollWithRate:behavior:interval:autoStop:vertical:complete:(CGFloat rate,[behavior] LynxScrollViewTouchBehavior behavior,[interval] NSTimeInterval interval,[autoStop] BOOL autoStop,[vertical] BOOL isVertical,[complete] _Nullable UIScrollViewLynxCompletion callback);
  public void UIScrollView(Lynx)::stopScroll();
  public BOOL UIScrollView(Lynx)::autoScrollWillReachToTheBounds();
}

public interface UIScrollView(LynxFadingEdge) {
  public void UIScrollView(LynxFadingEdge)::updateFadingEdgeWithSize:horizontal:(CGFloat size,[horizontal] BOOL horizontal);
}

public interface UIScrollView(LynxGesture) {
  public BOOL UIScrollView(LynxGesture)::respondToScrollViewDidScroll:(LynxGestureConsumer *gestureConsumer);
  public void UIScrollView(LynxGesture)::disableGesturesRecursivelyIfNecessary:(LynxGestureConsumer *gestureConsumer);
  public BOOL UIScrollView(LynxGesture)::stopDeceleratingIfNecessaryWithTargetContentOffset:(inout CGPoint *targetContentOffset);
}

public interface UIScrollView(Nested) {
  public UIScrollView* UIScrollView(Nested)::parentScrollView parentScrollView;
  public NSPointerArray* UIScrollView(Nested)::childrenScrollView childrenScrollView;
  public CGPoint UIScrollView(Nested)::lastPosition lastPosition;
  public BOOL UIScrollView(Nested)::enableNested enableNested;
  public BOOL UIScrollView(Nested)::scrollY scrollY;
  public NSString* UIScrollView(Nested)::name name;
  public BOOL UIScrollView(Nested)::isRTL isRTL;
  public BOOL UIScrollView(Nested)::childScrollViewCanScrollAtPoint:withDirection:(CGPoint point,[withDirection] BOOL isScrollY);
  public BOOL UIScrollView(Nested)::isOverEdge:(BOOL isScrollY);
  public UIScrollView * UIScrollView(Nested)::nearestParentScrollView();
  public void UIScrollView(Nested)::triggerNestedScrollView:(BOOL enableScrollY);
  public void UIScrollView(Nested)::updateChildren();
}

public interface UIView(Lynx) {
  public NSNumber* UIView(Lynx)::lynxSign lynxSign;
  public BOOL UIView(Lynx)::lynxClickable lynxClickable;
  public BOOL UIView(Lynx)::lynxEnableTapGestureSimultaneously lynxEnableTapGestureSimultaneously;
}

public interface UIView(LynxHeroTransition) {
  public LynxHeroViewConfig* UIView(LynxHeroTransition)::lynxHeroConfig lynxHeroConfig;
}

public interface UIViewController(LynxHeroTransition) {
  public LynxHeroViewControllerConfig* UIViewController(LynxHeroTransition)::lynxHeroConfig lynxHeroConfig;
}

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXTERN NSString* const kPropURL kPropURL;
  public FOUNDATION_EXTERN NSString* const kPropRelativePath kPropRelativePath;
  public FOUNDATION_EXTERN NSString* const kPropThreadMode kPropThreadMode;
  public FOUNDATION_EXPORT NSString* const kPropEnableSSR kPropEnableSSR;

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(uint32_t, LynxFeature);

  public std::shared_ptr< lynx::tasm::LynxTemplateBundle > LynxGetRawTemplateBundle(LynxTemplateBundle *bundle);
  public LynxTemplateBundle * ConstructTemplateBundleFromNative(lynx::tasm::LynxTemplateBundle bundle);
  public void LynxSetRawTemplateBundle(LynxTemplateBundle *bundle, lynx::tasm::LynxTemplateBundle *raw_bundle);

  public lynx::lepus::Value LynxConvertToLepusValue(id data, BOOL useBoolLiterals=NO);
  public lynx::lepus::Value * LynxGetLepusValueFromTemplateData(LynxTemplateData *data);
  public std::shared_ptr< lynx::tasm::TemplateData > ConvertLynxTemplateDataToTemplateData(LynxTemplateData *data);

  public FOUNDATION_EXTERN NSString* const kTimingEventPropSSRMetrics kTimingEventPropSSRMetrics;
  public FOUNDATION_EXTERN NSString* const kTimingEventPropSSRRenderPage kTimingEventPropSSRRenderPage;
  public FOUNDATION_EXTERN NSString* const kTimingCreateLynxStart kTimingCreateLynxStart;
  public FOUNDATION_EXTERN NSString* const kTimingCreateLynxEnd kTimingCreateLynxEnd;
  public FOUNDATION_EXTERN NSString* const kTimingPrepareTemplateStart kTimingPrepareTemplateStart;
  public FOUNDATION_EXTERN NSString* const kTimingPrepareTemplateEnd kTimingPrepareTemplateEnd;
  public FOUNDATION_EXTERN NSString* const kTimingContainerInitStart kTimingContainerInitStart;
  public FOUNDATION_EXTERN NSString* const kTimingContainerInitEnd kTimingContainerInitEnd;
  public FOUNDATION_EXTERN NSString* const kTimingOpenTime kTimingOpenTime;

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSUInteger, LynxWhiteSpaceType);
  public typedef NS_ENUM(NSUInteger, LynxTextOverflowType);
  public typedef NS_ENUM(NSUInteger, LynxFontWeightType);
  public typedef NS_ENUM(NSUInteger, LynxFontStyleType);
  public typedef NS_ENUM(NSUInteger, LynxVisibilityType);
  public typedef NS_ENUM(NSUInteger, LynxWordBreakType);
  public typedef NS_ENUM(NSUInteger, LynxImageRenderingType);
  public typedef NS_ENUM(NSUInteger, LynxHyphensType);
  public typedef NS_ENUM(NSUInteger, LynxXAppRegionType);
  public typedef NS_ENUM(NSUInteger, LynxXAnimationColorInterpolationType);

  typedef NSString *(^ LynxBacktraceFunction) (NSString *message, NSUInteger skippedDepth)
  public LYNX_EXTERN void LynxSetBacktraceFunction(LynxBacktraceFunction backtraceFunction);
  public LYNX_EXTERN LynxBacktraceFunction LynxGetBacktraceFunction(void);

  public typedef NS_ENUM(NSInteger, LynxBooleanOption);

  public typedef NS_ENUM(NSUInteger, LynxOverflowType);
  public typedef NS_ENUM(NSUInteger, LynxTimingFunctionType);
  public typedef NS_ENUM(NSUInteger, LynxAnimationDirectionType);
  public typedef NS_ENUM(NSUInteger, LynxAnimationFillModeType);
  public typedef NS_ENUM(NSUInteger, LynxAnimationPlayStateType);
  public typedef NS_ENUM(NSUInteger, LynxTransformType);
  public typedef NS_ENUM(NSUInteger, LynxBoxShadowOption);
  public typedef NS_ENUM(NSUInteger, LynxPlatformLengthUnit);
  public typedef NS_ENUM(NSUInteger, LynxPerspectiveLengthUnit);
  public typedef NS_ENUM(NSUInteger, LynxDirectionType);
  public typedef NS_ENUM(NSInteger, LynxBackgroundClipType);
  public typedef NS_ENUM(NSInteger, LynxBackgroundSizeType);
  public typedef NS_ENUM(NSInteger, LynxBackgroundPositionType);
  public typedef NS_ENUM(NSUInteger, LynxBackgroundRepeatType);
  public typedef NS_ENUM(NSUInteger, LynxBackgroundOriginType);
  public typedef NS_ENUM(NSUInteger, LynxTextAlignType);
  public typedef NS_ENUM(NSUInteger, LynxTextDecorationType);
  public typedef NS_ENUM(NSUInteger, LynxBackgroundImageType);
  public typedef NS_ENUM(NSUInteger, LynxRadialGradientShapeType);
  public typedef NS_ENUM(NSUInteger, LynxRadialGradientSizeType);
  public typedef NS_ENUM(NSInteger, LynxVerticalAlign);
  public typedef NS_ENUM(NSInteger, LynxBorderStyle);
  public typedef NS_ENUM(NSInteger, LynxFlexDirection);
  public typedef NS_ENUM(NSInteger, LynxBorderWidth);
  public typedef NS_ENUM(NSInteger, LynxFilterType);
  public typedef NS_ENUM(NSInteger, LynxBasicShapeType);

  public NS_ASSUME_NONNULL_BEGIN typedef void(^ LoadedBlock) (NSData *_Nullable data, NSError *_Nullable error) LoadedBlock;

  typedef void(^ LynxLogFunction) (LynxLogLevel level, NSString *message)
  typedef LynxLogObserver LynxLogDelegate
  public LynxLogFunction LynxDefaultLogFunction LynxDefaultLogFunction;
  public typedef NS_ENUM(NSInteger, LynxLogLevel);
  public typedef NS_OPTIONS(NSInteger, LynxLogSource);
  public LYNX_EXTERN void InitLynxLog(bool enable_devtools);
  public LYNX_EXTERN void SetDebugLoggingDelegate(LynxLogDelegate *delegate);
  public LYNX_EXTERN NSInteger AddLoggingDelegate(LynxLogDelegate *delegate);
  public LYNX_EXTERN LynxLogDelegate * GetLoggingDelegate(NSInteger delegateId);
  public LYNX_EXTERN void RemoveLoggingDelegate(NSInteger delegateId);
  public LYNX_EXTERN void SetMinimumLoggingLevel(LynxLogLevel minLogLevel);
  public LYNX_EXTERN void SetJSLogsFromExternalChannels(bool isOpen);
  public LYNX_EXTERN LynxLogLevel GetMinimumLoggingLevel(void);
  public LYNX_EXTERN NSInteger LynxSetLogFunction(LynxLogFunction logFunction);
  public LYNX_EXTERN LynxLogFunction LynxGetLogFunction(void);
  public LYNX_EXTERN NSInteger LynxAddLogObserver(LynxLogFunction logFunction, LynxLogLevel minLogLevel);
  public LYNX_EXTERN NSInteger LynxAddLogObserverByModel(LynxLogObserver *observer);
  public LYNX_EXTERN LynxLogObserver * LynxGetLogObserver(NSInteger observerId);
  public LYNX_EXTERN void LynxRemoveLogObserver(NSInteger observerId);
  public LYNX_EXTERN NSArray< LynxLogObserver * > * LynxGetLogObservers(void);
  public LYNX_EXTERN void LynxSetMinLogLevel(LynxLogLevel minLogLevel) __attribute__((deprecated("Use SetMinimumLoggingLevel instead.")));
  public LYNX_EXTERN LynxLogLevel LynxGetMinLogLevel(void);
  public LYNX_EXTERN void _LynxLogInternal(const char *, int32_t, LynxLogLevel, NSString *,...) NS_FORMAT_FUNCTION(4;
  public LYNX_EXTERN void LYNX_EXTERN const char * _GetLastPath(const char *filename, int32_t length);
  public LYNX_EXTERN void _LynxErrorInfoInternal(NSInteger, NSString *,...);
  public LYNX_EXTERN void _LynxErrorWarningInternal(bool, NSInteger, NSString *,...);
  public LYNX_EXTERN void _LynxErrorFatalInternal(bool, NSInteger, NSString *,...);

  public const int kLynxPerformanceIsSrrHydrateIndex kLynxPerformanceIsSrrHydrateIndex;

  typedef void(^ LynxResourceLoadCompletionBlock) (BOOL isSyncCallback, NSData *_Nullable data, NSError *_Nullable error, NSURL *_Nullable resURL)
  typedef void(^ LynxResourceResolveHandler) (NSString *resolvedURL, id fetcher, id params, NSError *_Nullable error)
  typedef void(^ LynxResourceCompletionHandler) (NSData *_Nullable data, NSError *_Nullable error)
  typedef void(^ LynxLocalFileCompletionHandler) (NSURL *_Nullable url, NSError *_Nullable error)
  typedef void(^ LynxResourceLoadCompletedBlock) (LynxResourceResponse *_Nonnull response)
  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxFetchResType);

  public const NSString* LYNX_TARGET_SDK_VERSION_1_5 LYNX_TARGET_SDK_VERSION_1_5;

  public NS_ASSUME_NONNULL_BEGIN typedef void(^ CDPResultCallback) (NSString *result) CDPResultCallback;

  public typedef NS_ENUM(NSInteger, LynxLogBoxLevel);

  public FOUNDATION_EXPORT int32_t const LynxTouchPseudoStateNone LynxTouchPseudoStateNone;
  public FOUNDATION_EXPORT int32_t const LynxTouchPseudoStateHover LynxTouchPseudoStateHover;
  public FOUNDATION_EXPORT int32_t const LynxTouchPseudoStateHoverTransition LynxTouchPseudoStateHoverTransition;
  public FOUNDATION_EXPORT int32_t const LynxTouchPseudoStateActive LynxTouchPseudoStateActive;
  public FOUNDATION_EXPORT int32_t const LynxTouchPseudoStateActiveTransition LynxTouchPseudoStateActiveTransition;
  public FOUNDATION_EXPORT int32_t const LynxTouchPseudoStateFocus LynxTouchPseudoStateFocus;
  public FOUNDATION_EXPORT int32_t const LynxTouchPseudoStateFocusTransition LynxTouchPseudoStateFocusTransition;
  public FOUNDATION_EXPORT int32_t const LynxTouchPseudoStateAll LynxTouchPseudoStateAll;
  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxEventType);

  public enum LynxEventPropStatus
  kLynxEventPropEnable
  kLynxEventPropDisable
  kLynxEventPropUndefined

  public NS_ASSUME_NONNULL_BEGIN NSString* const LynxEventTouchMove LynxEventTouchMove;
  public NSString* const LynxEventTouchStart LynxEventTouchStart;
  public NSString* const LynxEventTouchEnd LynxEventTouchEnd;
  public NSString* const LynxEventTouchCancel LynxEventTouchCancel;
  public NSString* const LynxEventTap LynxEventTap;
  public NSString* const LynxEventLongPress LynxEventLongPress;
  public NSString* const LynxEventClick LynxEventClick;

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT int32_t const kUnknownInstanceId kUnknownInstanceId;
  public FOUNDATION_EXPORT NSString* const kLynxSDKErrorEvent kLynxSDKErrorEvent;

  public typedef NS_ENUM(NSInteger, LynxThreadStrategyForRender);

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSString* const kDefaultComponentID kDefaultComponentID;

  public NS_ASSUME_NONNULL_BEGIN typedef void(^ LynxOpenCardCallback) (NSString *) LynxOpenCardCallback;

  public NS_ASSUME_NONNULL_BEGIN typedef void(^ onComponentLoaded) (NSData *_Nullable data, NSError *_Nullable error) onComponentLoaded;

  public NSString* const SP_KEY_ENABLE_AUTOMATION SP_KEY_ENABLE_AUTOMATION;
  public NSString* const KEY_LYNX_DEBUG KEY_LYNX_DEBUG;
  public NSString* const KEY_DEVTOOL_COMPONENT_ATTACH KEY_DEVTOOL_COMPONENT_ATTACH;
  public NSString* const SP_KEY_ENABLE_DEVTOOL SP_KEY_ENABLE_DEVTOOL;
  public NSString* const SP_KEY_ENABLE_DEVTOOL_FOR_DEBUGGABLE_VIEW SP_KEY_ENABLE_DEVTOOL_FOR_DEBUGGABLE_VIEW;
  public NSString* const SP_KEY_ENABLE_LOGBOX SP_KEY_ENABLE_LOGBOX;
  public NSString* const SP_KEY_ENABLE_HIGHLIGHT_TOUCH SP_KEY_ENABLE_HIGHLIGHT_TOUCH;
  public NSString* const SP_KEY_ENABLE_LAUNCH_RECORD SP_KEY_ENABLE_LAUNCH_RECORD;
  public NSString* const SP_KEY_ENABLE_DOM_TREE SP_KEY_ENABLE_DOM_TREE;
  public NSString* const SP_KEY_ENABLE_LONG_PRESS_MENU SP_KEY_ENABLE_LONG_PRESS_MENU;
  public NSString* const SP_KEY_IGNORE_ERROR_TYPES SP_KEY_IGNORE_ERROR_TYPES;
  public NSString* const SP_KEY_ENABLE_IGNORE_ERROR_CSS SP_KEY_ENABLE_IGNORE_ERROR_CSS;
  public NSString* const SP_KEY_ENABLE_PREVIEW_SCREEN_SHOT SP_KEY_ENABLE_PREVIEW_SCREEN_SHOT;
  public NSString* const SP_KEY_ACTIVATED_CDP_DOMAINS SP_KEY_ACTIVATED_CDP_DOMAINS;
  public NSString* const SP_KEY_ENABLE_CDP_DOMAIN_DOM SP_KEY_ENABLE_CDP_DOMAIN_DOM;
  public NSString* const SP_KEY_ENABLE_CDP_DOMAIN_CSS SP_KEY_ENABLE_CDP_DOMAIN_CSS;
  public NSString* const SP_KEY_ENABLE_CDP_DOMAIN_PAGE SP_KEY_ENABLE_CDP_DOMAIN_PAGE;
  public NSString* const SP_KEY_DEVTOOL_CONNECTED SP_KEY_DEVTOOL_CONNECTED;
  public NSString* const SP_KEY_ENABLE_QUICKJS_DEBUG SP_KEY_ENABLE_QUICKJS_DEBUG;
  public NSString* const SP_KEY_SHOW_DEVTOOL_BADGE SP_KEY_SHOW_DEVTOOL_BADGE;
  public typedef NS_ENUM(uint64_t, LynxEnvKey);

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSString* const LynxErrorDomain LynxErrorDomain;
  public FOUNDATION_EXPORT NSString* const LynxErrorUserInfoKeyMessage LynxErrorUserInfoKeyMessage;
  public FOUNDATION_EXPORT NSString* const LynxErrorUserInfoKeySourceError LynxErrorUserInfoKeySourceError;
  public FOUNDATION_EXPORT NSString* const LynxErrorUserInfoKeyCustomInfo LynxErrorUserInfoKeyCustomInfo;
  public FOUNDATION_EXPORT NSString* const LynxErrorUserInfoKeyStackInfo LynxErrorUserInfoKeyStackInfo;
  public FOUNDATION_EXPORT NSString* const LynxErrorKeyResourceType LynxErrorKeyResourceType;
  public FOUNDATION_EXPORT NSString* const LynxErrorKeyResourceUrl LynxErrorKeyResourceUrl;
  public FOUNDATION_EXPORT NSString* const LynxErrorLevelError LynxErrorLevelError;
  public FOUNDATION_EXPORT NSString* const LynxErrorLevelWarn LynxErrorLevelWarn;
  public FOUNDATION_EXPORT NSString* const LynxErrorSuggestionRefOfficialSite LynxErrorSuggestionRefOfficialSite;

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSInteger const EBLynxSuccess EBLynxSuccess;
  public FOUNDATION_EXPORT NSInteger const EBLynxAppBundleLoad EBLynxAppBundleLoad;
  public FOUNDATION_EXPORT NSInteger const EBLynxAppBundleReload EBLynxAppBundleReload;
  public FOUNDATION_EXPORT NSInteger const EBLynxAppBundleVerify EBLynxAppBundleVerify;
  public FOUNDATION_EXPORT NSInteger const EBLynxBTSRuntimeError EBLynxBTSRuntimeError;
  public FOUNDATION_EXPORT NSInteger const EBLynxBTSPlatformCallJSFunction EBLynxBTSPlatformCallJSFunction;
  public FOUNDATION_EXPORT NSInteger const EBLynxBTSLifecycleListenerError EBLynxBTSLifecycleListenerError;
  public FOUNDATION_EXPORT NSInteger const EBLynxResourceImage EBLynxResourceImage;
  public FOUNDATION_EXPORT NSInteger const EBLynxResourceFont EBLynxResourceFont;
  public FOUNDATION_EXPORT NSInteger const EBLynxResourceExternalResource EBLynxResourceExternalResource;
  public FOUNDATION_EXPORT NSInteger const EBLynxResourceI18n EBLynxResourceI18n;
  public FOUNDATION_EXPORT NSInteger const EBLynxResourceModule EBLynxResourceModule;
  public FOUNDATION_EXPORT NSInteger const EBLynxResourceCustom EBLynxResourceCustom;
  public FOUNDATION_EXPORT NSInteger const EBLynxResourceException EBLynxResourceException;
  public FOUNDATION_EXPORT NSInteger const EBLynxDataFlowUpdate EBLynxDataFlowUpdate;
  public FOUNDATION_EXPORT NSInteger const EBLynxElementAPI EBLynxElementAPI;
  public FOUNDATION_EXPORT NSInteger const EBLynxElementUpdate EBLynxElementUpdate;
  public FOUNDATION_EXPORT NSInteger const EBLynxLayoutInternal EBLynxLayoutInternal;
  public FOUNDATION_EXPORT NSInteger const EBLynxLayoutPerf EBLynxLayoutPerf;
  public FOUNDATION_EXPORT NSInteger const EBLynxLayoutUpdate EBLynxLayoutUpdate;
  public FOUNDATION_EXPORT NSInteger const EBLynxLayoutPlatform EBLynxLayoutPlatform;
  public FOUNDATION_EXPORT NSInteger const EBLynxNativeModulesCommon EBLynxNativeModulesCommon;
  public FOUNDATION_EXPORT NSInteger const EBLynxNativeModulesNetwork EBLynxNativeModulesNetwork;
  public FOUNDATION_EXPORT NSInteger const EBLynxNativeModulesCustomError EBLynxNativeModulesCustomError;
  public FOUNDATION_EXPORT NSInteger const EBLynxNativeModulesException EBLynxNativeModulesException;
  public FOUNDATION_EXPORT NSInteger const EBLynxEventException EBLynxEventException;
  public FOUNDATION_EXPORT NSInteger const EBLynxMTSRuntimeError EBLynxMTSRuntimeError;
  public FOUNDATION_EXPORT NSInteger const EBLynxMTSRendererFunction EBLynxMTSRendererFunction;
  public FOUNDATION_EXPORT NSInteger const EBLynxThreadWrongThread EBLynxThreadWrongThread;
  public FOUNDATION_EXPORT NSInteger const EBLynxCSS EBLynxCSS;
  public FOUNDATION_EXPORT NSInteger const EBLynxCSSComputedCSSValue EBLynxCSSComputedCSSValue;
  public FOUNDATION_EXPORT NSInteger const EBLynxCSSParser EBLynxCSSParser;
  public FOUNDATION_EXPORT NSInteger const EBLynxSSRDecode EBLynxSSRDecode;
  public FOUNDATION_EXPORT NSInteger const EBLynxSSRLoad EBLynxSSRLoad;
  public FOUNDATION_EXPORT NSInteger const EBLynxSSRHydrate EBLynxSSRHydrate;
  public FOUNDATION_EXPORT NSInteger const EBLynxLazyBundleLoad EBLynxLazyBundleLoad;
  public FOUNDATION_EXPORT NSInteger const EBLynxWorkletMTSCallException EBLynxWorkletMTSCallException;
  public FOUNDATION_EXPORT NSInteger const EBLynxWorkletRafCallException EBLynxWorkletRafCallException;
  public FOUNDATION_EXPORT NSInteger const EBLynxWorkletModuleException EBLynxWorkletModuleException;
  public FOUNDATION_EXPORT NSInteger const EBLynxMTSBridgeModule EBLynxMTSBridgeModule;
  public FOUNDATION_EXPORT NSInteger const EBLynxComponentAPI EBLynxComponentAPI;
  public FOUNDATION_EXPORT NSInteger const EBLynxComponentList EBLynxComponentList;
  public FOUNDATION_EXPORT NSInteger const EBLynxComponentImage EBLynxComponentImage;
  public FOUNDATION_EXPORT NSInteger const EBLynxComponentCustom EBLynxComponentCustom;
  public FOUNDATION_EXPORT NSInteger const EBLynxExceptionPlatform EBLynxExceptionPlatform;
  public FOUNDATION_EXPORT NSInteger const EBLynxExceptionJNI EBLynxExceptionJNI;

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSInteger const LynxErrorCodeSuccess LynxErrorCodeSuccess;
  public FOUNDATION_EXPORT NSInteger const LynxErrorCodeLoadTemplate LynxErrorCodeLoadTemplate;
  public FOUNDATION_EXPORT NSInteger const LynxErrorCodeTemplateProvider LynxErrorCodeTemplateProvider;
  public FOUNDATION_EXPORT NSInteger const LynxErrorCodeReloadTemplate LynxErrorCodeReloadTemplate;
  public FOUNDATION_EXPORT NSInteger const LynxErrorCodeJavaScript LynxErrorCodeJavaScript;
  public FOUNDATION_EXPORT NSInteger const LynxErrorCodeForResourceError LynxErrorCodeForResourceError;
  public FOUNDATION_EXPORT NSInteger const LynxErrorCodeLayout LynxErrorCodeLayout;
  public FOUNDATION_EXPORT NSInteger const LynxErrorCodeUpdate LynxErrorCodeUpdate;
  public FOUNDATION_EXPORT NSInteger const LynxErrorCodeException LynxErrorCodeException;

  public typedef NS_ENUM(NSInteger, LynxLoadMode);
  public typedef NS_OPTIONS(NSInteger, LynxLoadOption);

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSInteger const ECLynxSuccess ECLynxSuccess;
  public FOUNDATION_EXPORT NSInteger const ECLynxAppBundleLoadRenderFailed ECLynxAppBundleLoadRenderFailed;
  public FOUNDATION_EXPORT NSInteger const ECLynxAppBundleLoadEnvNotReady ECLynxAppBundleLoadEnvNotReady;
  public FOUNDATION_EXPORT NSInteger const ECLynxAppBundleLoadBadResponse ECLynxAppBundleLoadBadResponse;
  public FOUNDATION_EXPORT NSInteger const ECLynxAppBundleLoadParseFailed ECLynxAppBundleLoadParseFailed;
  public FOUNDATION_EXPORT NSInteger const ECLynxAppBundleLoadBadBundle ECLynxAppBundleLoadBadBundle;
  public FOUNDATION_EXPORT NSInteger const ECLynxAppBundleLoadException ECLynxAppBundleLoadException;
  public FOUNDATION_EXPORT NSInteger const ECLynxAppBundleReloadEarlyReload ECLynxAppBundleReloadEarlyReload;
  public FOUNDATION_EXPORT NSInteger const ECLynxAppBundleVerifyInvalidSignature ECLynxAppBundleVerifyInvalidSignature;
  public FOUNDATION_EXPORT NSInteger const ECLynxBTSRuntimeError ECLynxBTSRuntimeError;
  public FOUNDATION_EXPORT NSInteger const ECLynxBTSRuntimeErrorScriptError ECLynxBTSRuntimeErrorScriptError;
  public FOUNDATION_EXPORT NSInteger const ECLynxBTSRuntimeErrorBytecodeScriptError ECLynxBTSRuntimeErrorBytecodeScriptError;
  public FOUNDATION_EXPORT NSInteger const ECLynxBTSRuntimeErrorBindingsError ECLynxBTSRuntimeErrorBindingsError;
  public FOUNDATION_EXPORT NSInteger const ECLynxBTSPlatformCallJSFunctionTooFrequency ECLynxBTSPlatformCallJSFunctionTooFrequency;
  public FOUNDATION_EXPORT NSInteger const ECLynxBTSLifecycleListenerErrorException ECLynxBTSLifecycleListenerErrorException;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceImageBigImage ECLynxResourceImageBigImage;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceImagePicSource ECLynxResourceImagePicSource;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceImageFromUserOrDesign ECLynxResourceImageFromUserOrDesign;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceImageFromNetworkOrOthers ECLynxResourceImageFromNetworkOrOthers;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceImageException ECLynxResourceImageException;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceFont ECLynxResourceFont;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceFontSrcFormatError ECLynxResourceFontSrcFormatError;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceFontResourceLoadError ECLynxResourceFontResourceLoadError;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceFontBase64ParsingError ECLynxResourceFontBase64ParsingError;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceFontFileFormatNotSupported ECLynxResourceFontFileFormatNotSupported;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceFontRegisterFailed ECLynxResourceFontRegisterFailed;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceExternalResourceRequestFailed ECLynxResourceExternalResourceRequestFailed;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceExternalResourceLocalResourceLoadFail ECLynxResourceExternalResourceLocalResourceLoadFail;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceI18n ECLynxResourceI18n;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceModuleParamsError ECLynxResourceModuleParamsError;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceModuleImgPrefetchHelperNotExist ECLynxResourceModuleImgPrefetchHelperNotExist;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceModuleResourceServiceNotExist ECLynxResourceModuleResourceServiceNotExist;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceCustom ECLynxResourceCustom;
  public FOUNDATION_EXPORT NSInteger const ECLynxResourceException ECLynxResourceException;
  public FOUNDATION_EXPORT NSInteger const ECLynxDataFlowUpdateInvalidProcessor ECLynxDataFlowUpdateInvalidProcessor;
  public FOUNDATION_EXPORT NSInteger const ECLynxDataFlowUpdateInvalidType ECLynxDataFlowUpdateInvalidType;
  public FOUNDATION_EXPORT NSInteger const ECLynxDataFlowUpdateException ECLynxDataFlowUpdateException;
  public FOUNDATION_EXPORT NSInteger const ECLynxElementAPIFatal ECLynxElementAPIFatal;
  public FOUNDATION_EXPORT NSInteger const ECLynxElementAPIError ECLynxElementAPIError;
  public FOUNDATION_EXPORT NSInteger const ECLynxElementUpdateNodeIsNull ECLynxElementUpdateNodeIsNull;
  public FOUNDATION_EXPORT NSInteger const ECLynxLayoutInternal ECLynxLayoutInternal;
  public FOUNDATION_EXPORT NSInteger const ECLynxLayoutPerfInfiniteLoop ECLynxLayoutPerfInfiniteLoop;
  public FOUNDATION_EXPORT NSInteger const ECLynxLayoutUpdateUINotFound ECLynxLayoutUpdateUINotFound;
  public FOUNDATION_EXPORT NSInteger const ECLynxLayoutPlatformNodeNull ECLynxLayoutPlatformNodeNull;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesCommonModuleNotFound ECLynxNativeModulesCommonModuleNotFound;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesCommonFunctionNotFound ECLynxNativeModulesCommonFunctionNotFound;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesCommonWrongParamNum ECLynxNativeModulesCommonWrongParamNum;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesCommonWrongParamType ECLynxNativeModulesCommonWrongParamType;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesCommonAuthorizationError ECLynxNativeModulesCommonAuthorizationError;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesCommonSystemAuthorizationError ECLynxNativeModulesCommonSystemAuthorizationError;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesCommonReturnError ECLynxNativeModulesCommonReturnError;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesCommonDeprecated ECLynxNativeModulesCommonDeprecated;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesNetworkBadResponse ECLynxNativeModulesNetworkBadResponse;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesCustomError ECLynxNativeModulesCustomError;
  public FOUNDATION_EXPORT NSInteger const ECLynxNativeModulesException ECLynxNativeModulesException;
  public FOUNDATION_EXPORT NSInteger const ECLynxEventException ECLynxEventException;
  public FOUNDATION_EXPORT NSInteger const ECLynxMTSRuntimeError ECLynxMTSRuntimeError;
  public FOUNDATION_EXPORT NSInteger const ECLynxMTSRendererFunctionFatal ECLynxMTSRendererFunctionFatal;
  public FOUNDATION_EXPORT NSInteger const ECLynxMTSRendererFunctionError ECLynxMTSRendererFunctionError;
  public FOUNDATION_EXPORT NSInteger const ECLynxThreadWrongThreadDestroyError ECLynxThreadWrongThreadDestroyError;
  public FOUNDATION_EXPORT NSInteger const ECLynxThreadWrongThreadSyncFlushError ECLynxThreadWrongThreadSyncFlushError;
  public FOUNDATION_EXPORT NSInteger const ECLynxCSS ECLynxCSS;
  public FOUNDATION_EXPORT NSInteger const ECLynxCSSUnknownProperty ECLynxCSSUnknownProperty;
  public FOUNDATION_EXPORT NSInteger const ECLynxCSSUnsupportedValue ECLynxCSSUnsupportedValue;
  public FOUNDATION_EXPORT NSInteger const ECLynxCSSComputedCSSValueUnknownSetter ECLynxCSSComputedCSSValueUnknownSetter;
  public FOUNDATION_EXPORT NSInteger const ECLynxCSSComputedCSSValueUnknownGetter ECLynxCSSComputedCSSValueUnknownGetter;
  public FOUNDATION_EXPORT NSInteger const ECLynxCSSComputedCSSValueUnsupportedInheritance ECLynxCSSComputedCSSValueUnsupportedInheritance;
  public FOUNDATION_EXPORT NSInteger const ECLynxCSSParser ECLynxCSSParser;
  public FOUNDATION_EXPORT NSInteger const ECLynxSSRDecode ECLynxSSRDecode;
  public FOUNDATION_EXPORT NSInteger const ECLynxSSRDecodeApiVersionNotSupported ECLynxSSRDecodeApiVersionNotSupported;
  public FOUNDATION_EXPORT NSInteger const ECLynxSSRDecodeScript ECLynxSSRDecodeScript;
  public FOUNDATION_EXPORT NSInteger const ECLynxSSRLoadUninitialized ECLynxSSRLoadUninitialized;
  public FOUNDATION_EXPORT NSInteger const ECLynxSSRHydrateDomDeviateFromSsrResult ECLynxSSRHydrateDomDeviateFromSsrResult;
  public FOUNDATION_EXPORT NSInteger const ECLynxLazyBundleLoadBadResponse ECLynxLazyBundleLoadBadResponse;
  public FOUNDATION_EXPORT NSInteger const ECLynxLazyBundleLoadEmptyFile ECLynxLazyBundleLoadEmptyFile;
  public FOUNDATION_EXPORT NSInteger const ECLynxLazyBundleLoadDecodeFailed ECLynxLazyBundleLoadDecodeFailed;
  public FOUNDATION_EXPORT NSInteger const ECLynxLazyBundleLoadBadBundle ECLynxLazyBundleLoadBadBundle;
  public FOUNDATION_EXPORT NSInteger const ECLynxWorkletMTSCallException ECLynxWorkletMTSCallException;
  public FOUNDATION_EXPORT NSInteger const ECLynxWorkletRafCallException ECLynxWorkletRafCallException;
  public FOUNDATION_EXPORT NSInteger const ECLynxWorkletModuleException ECLynxWorkletModuleException;
  public FOUNDATION_EXPORT NSInteger const ECLynxMTSBridgeModuleWrongParam ECLynxMTSBridgeModuleWrongParam;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentAPIDeprecated ECLynxComponentAPIDeprecated;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentListIllegalItemKey ECLynxComponentListIllegalItemKey;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentListDuplicatedCell ECLynxComponentListDuplicatedCell;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentListCellNotFound ECLynxComponentListCellNotFound;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentListDynamicChangeOrientation ECLynxComponentListDynamicChangeOrientation;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentListInvalidPropsArg ECLynxComponentListInvalidPropsArg;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentListChildComponentNotExist ECLynxComponentListChildComponentNotExist;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentListUnsupportedThreadStrategy ECLynxComponentListUnsupportedThreadStrategy;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentListDuplicateItemKey ECLynxComponentListDuplicateItemKey;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentImageUnsupportedProp ECLynxComponentImageUnsupportedProp;
  public FOUNDATION_EXPORT NSInteger const ECLynxComponentCustom ECLynxComponentCustom;
  public FOUNDATION_EXPORT NSInteger const ECLynxExceptionPlatform ECLynxExceptionPlatform;
  public FOUNDATION_EXPORT NSInteger const ECLynxExceptionJNI ECLynxExceptionJNI;
  public typedef NS_ENUM(NSInteger, ELynxLevel);
  public typedef NS_ENUM(NSInteger, ELynxConsumer);

  typedef void(^ LynxTemplateLoadBlock) (id data, NSError *error)

  public typedef NS_ENUM(NSInteger, LynxPipelineOrigin);

  public typedef NS_ENUM(NSInteger, LynxViewSizeMode);
  public typedef NS_ENUM(NSInteger, LynxThreadStrategyForRender);

  typedef void(^ LynxPromiseResolveBlock) (id result)
  typedef void(^ LynxPromiseRejectBlock) (NSString *code, NSString *message)
  typedef BOOL(^ LynxMethodBlock) (NSString *method, NSString *module, NSString *invoke_session, NSInvocation *inv)
  typedef NSDictionary *_Nullable(^ LynxMethodSessionBlock) (NSString *method, NSString *module, NSString *invoke_session, NSString *name_space)
  public NS_ASSUME_NONNULL_BEGIN typedef void(^ LynxCallbackBlock) (id result) LynxCallbackBlock;

  typedef LynxView *(^ LynxViewReCreateBlock) (LynxRoute *)
  typedef void(^ LynxViewEvictedBlock) (LynxView *)

  typedef void(^ LynxGenericResourcePathCompletionBlock) (NSString *_Nullable path, NSError *_Nullable error)
  public NS_ASSUME_NONNULL_BEGIN typedef void(^ LynxGenericResourceCompletionBlock) (NSData *_Nullable data, NSError *_Nullable error) LynxGenericResourceCompletionBlock;

  public NS_ASSUME_NONNULL_BEGIN typedef void(^ LynxMediaResourceCompletionBlock) (UIImage *_Nullable uiImage, NSError *_Nullable error) LynxMediaResourceCompletionBlock;
  public typedef NS_ENUM(NSInteger, LynxResourceOptionalBool);

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_IMAGE LYNX_PROVIDER_TYPE_IMAGE;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_FONT LYNX_PROVIDER_TYPE_FONT;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_LOTTIE LYNX_PROVIDER_TYPE_LOTTIE;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_VIDEO LYNX_PROVIDER_TYPE_VIDEO;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_SVG LYNX_PROVIDER_TYPE_SVG;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_TEMPLATE LYNX_PROVIDER_TYPE_TEMPLATE;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_LYNX_CORE_JS LYNX_PROVIDER_TYPE_LYNX_CORE_JS;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_DYNAMIC_COMPONENT LYNX_PROVIDER_TYPE_DYNAMIC_COMPONENT;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_I18N_TEXT LYNX_PROVIDER_TYPE_I18N_TEXT;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_THEME LYNX_PROVIDER_TYPE_THEME;
  public FOUNDATION_EXPORT NSString* const LYNX_PROVIDER_TYPE_EXTERNAL_JS LYNX_PROVIDER_TYPE_EXTERNAL_JS;

  public NS_ASSUME_NONNULL_BEGIN typedef void(^ LynxResourceLoadBlock) (LynxResourceResponse *response) LynxResourceLoadBlock;

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxResourceRequestAsyncMode);
  public typedef NS_ENUM(NSInteger, LynxResourceRequestType);

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSInteger const LynxResourceResponseCodeSuccess LynxResourceResponseCodeSuccess;
  public FOUNDATION_EXPORT NSInteger const LynxResourceResponseCodeFailed LynxResourceResponseCodeFailed;

  typedef void(^ LynxSSRResourceCompletionBlock) (NSData *_Nullable data, NSError *_Nullable error)
  public NS_ASSUME_NONNULL_BEGIN typedef void(^ LynxTemplateResourceCompletionBlock) (LynxTemplateResource *_Nullable data, NSError *_Nullable error) LynxTemplateResourceCompletionBlock;

  typedef void(^ LynxHttpCallback) (LynxHttpResponse *result)

  typedef typedefNS_ASSUME_NONNULL_BEGIN struct napi_env__* napi_env

  public enum LynxContextTagType
  LynxContextTagLastLynxURL
  LynxContextTagLastLynxAsyncComponentURL

  public NSUInteger const kLynxServiceTypeMonitor kLynxServiceTypeMonitor;
  public NSUInteger const kLynxServiceHttp kLynxServiceHttp;
  public NSUInteger const kLynxServiceTrail kLynxServiceTrail;
  public NSUInteger const kLynxServiceImage kLynxServiceImage;
  public NSUInteger const kLynxServiceEventReporter kLynxServiceEventReporter;
  public NSUInteger const kLynxServiceModule kLynxServiceModule;
  public NSUInteger const kLynxServiceLog kLynxServiceLog;
  public NSUInteger const kLynxServiceI18n kLynxServiceI18n;
  public NSUInteger const kLynxServiceSystemInvoke kLynxServiceSystemInvoke;
  public NSUInteger const kLynxServiceResource kLynxServiceResource;
  public NSUInteger const kLynxServiceSecurity kLynxServiceSecurity;
  public NSUInteger const kLynxServiceDevTool kLynxServiceDevTool;
  public NSUInteger const kLynxServiceExtension kLynxServiceExtension;
  public typedef NS_OPTIONS(NSUInteger, LynxServiceScope);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxServiceResourceScene);

  typedef void(^ LynxServiceResourceCompletionHandler) (id< LynxServiceResourceResponseProtocol > __nullable response, NSError *__nullable error)

  public typedef NS_ENUM(NSInteger, LynxTASMType);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSUInteger, LynxSizeValueType);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(UInt8, LynxFPSRecordState);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSUInteger, LynxGestureFlingTriggerState);

  public NS_ASSUME_NONNULL_BEGIN const int LYNX_STATE_INIT LYNX_STATE_INIT;
  public const int LYNX_STATE_BEGIN LYNX_STATE_BEGIN;
  public const int LYNX_STATE_ACTIVE LYNX_STATE_ACTIVE;
  public const int LYNX_STATE_FAIL LYNX_STATE_FAIL;
  public const int LYNX_STATE_END LYNX_STATE_END;
  public const int LYNX_STATE_UNDETERMINED LYNX_STATE_UNDETERMINED;

  public typedef NS_ENUM(NSInteger, LynxBooleanOption);

  typedef void(^ DidAnimationStop) (CAAnimation *__nullable animation, BOOL finished)
  public DidAnimationStart didStart didStart;
  public DidAnimationStop didStop didStop;
  public instancetype initWithDidStart:didStop:(DidAnimationStart __nullable start,[didStop] DidAnimationStop stop);
  public void forceStop();
  public void sendAnimationEvent:eventName:eventParams:(LynxUI *ui,[eventName] NSString *eventName,[eventParams] NSDictionary *params);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_OPTIONS(NSUInteger, LynxAnimationProp);

  public NSString* const DUP_CONTENT_ANI_PREFIX DUP_CONTENT_ANI_PREFIX;
  public NSString* const kAnimationEventStart kAnimationEventStart;
  public NSString* const kAnimationEventEnd kAnimationEventEnd;
  public NSString* const kAnimationEventCancel kAnimationEventCancel;
  public CABasicAnimation * createBasicAnimation:from:to:info:(NSString *path,[from] id fromValue,[to] id toValue,[info] LynxAnimationInfo *info);
  public void applyAnimationProperties:withInfo:forLayer:(CAAnimation *animation,[withInfo] LynxAnimationInfo *info,[forLayer] CALayer *layer);
  public void removeAnimation:withName:(LynxUI *ui,[withName] NSString *animationKey);
  public void attachTo:animation:forKey:(LynxUI *ui,[animation] CAAnimation *animation,[forKey] NSString *animationName);
  public void attachOpacityTo:animation:forKey:(LynxUI *ui,[animation] CAAnimation *animation,[forKey] NSString *animationName);
  public void addContentAnimationDelegateTo:forTargetLayer:withContent:withProp:(CABasicAnimation *contentAnimation,[forTargetLayer] CALayer *targetLayer,[withContent] UIImage *content,[withProp] LynxAnimationProp prop);
  public void addPathAnimationDelegateTo:forTargetLayer:withPath:withProp:(CABasicAnimation *pathAnimation,[forTargetLayer] CAShapeLayer *targetLayer,[withPath] CGPathRef path,[withProp] LynxAnimationProp prop);
  public end CFTimeInterval getSyncedTimestamp(void);

  public void LynxPathAddRoundedRect(CGMutablePathRef path, CGRect bounds, LynxCornerInsets ci);

  typedef struct _LynxBorderUnitValue LynxBorderUnitValue
  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxBorderValueUnit);
  public typedef NS_ENUM(NSInteger, LynxBorderPosition);
  public static bool isBorderUnitEqual(LynxBorderUnitValue lhs, LynxBorderUnitValue rhs);
  public static bool isBorderUnitEqualA(LynxBorderUnitValue lhs, LynxBorderUnitValue rhs, LynxPlatformLength *lLength, LynxPlatformLength *rLength);

  public const LynxBorderRadii LynxBorderRadiiZero LynxBorderRadiiZero;
  public NS_ASSUME_NONNULL_BEGIN NSString * NSStringFromLynxBorderRadii(LynxBorderRadii *radii);
  public typedef NS_ENUM(NSInteger, LynxAnimOpts);
  public typedef NS_ENUM(uint8_t, LynxBgTypes);
  public typedef NS_ENUM(uint8_t, LynxBgShapeLayerProp);
  public static bool _noneZeroBUV(LynxBorderUnitValue unit);
  public static bool LynxHasBorderRadii(LynxBorderRadii radii);

  public const CGFloat LYNX_BORDER_THREAHOLD LYNX_BORDER_THREAHOLD;
  public static CGRect LynxGetBoundsAutoAdjust(CGRect bounds);
  public static BOOL LynxBorderInsetsAreEqual(UIEdgeInsets borderInsets);
  public static CGFloat LynxBorderUnitValToFloatA(LynxBorderUnitValue val, CGFloat reference, LynxPlatformLength *calc);
  public static CGFloat LynxBorderUnitValToFloat(LynxBorderUnitValue val, CGFloat percentBase);
  public static BOOL LynxBorderColorsAreEqual(LynxBorderColors borderColors);
  public static BOOL LynxBorderStylesAreEqual(LynxBorderStyles borderStyles);
  public static void LynxEllipseGetIntersectionsWithLine(CGRect ellipseBounds, CGPoint lineStart, CGPoint lineEnd, CGPoint intersections[2]);
  public static LynxCornerInsetPoints LynxCalculateCornerInsetPoints(UIEdgeInsets borderInsets, LynxCornerInsets cornerInsets, CGSize size);
  public static CGColorRef LynxCreateDarkenColor(CGColorRef color, bool darken);
  public static CGSize LynxRoundViewSize(CGSize viewSize);

  public static CGContextRef LynxUIGraphicsBeginImageContext(CGSize size, CGColorRef backgroundColor, BOOL hasCornerRadii, BOOL drawToEdge, BOOL *hasOldCtx, LynxBackgroundClipType clipType);
  public static void LynxDrawSolidInsetOrOutsetBorder(CGContextRef ctx, LynxBorderStyle borderStyle, CGSize size, LynxBorderRadii cornerRadii, UIEdgeInsets borderInsets, LynxBorderColors borderColors, BOOL drawToEdge, BOOL hasCornerRadii, CGPathRef path, CGPathRef insetPath, const LynxCornerInsets cornerInsets);
  public static void LynxStrokeDashedOrDottedBorderLine(CGContextRef ctx, bool isDotted, CGColorRef borderColor, CGPoint startPoint, CGPoint endPoint, float borderLength, float borderWidth);
  public static void LynxSaveAndClipQuadrilateralFromPoints(CGContextRef ctx, const CGPoint *pointsForClip);
  public static void LynxStrokeDashDottedCenterPath(CGContextRef ctx, BOOL isDotted, CGPathRef path, CGColorRef borderColor, CGFloat borderWidthForEffect, CGFloat borderWidthForStroke);
  public static void LynxDrawDashedOrDottedRectWithSameColor(CGContextRef ctx, LynxBorderStyle borderStyle, CGSize size, UIEdgeInsets borderInsets, CGColorRef borderColor);
  public static void LynxDrawDashedOrDottedRoundRectWithSameColor(CGContextRef ctx, LynxBorderStyle borderStyle, CGSize size, LynxBorderRadii cornerRadii, UIEdgeInsets borderInsets, LynxBorderColors borderColors, BOOL drawToEdge, BOOL hasCornerRadii, CGPathRef insetPath, const LynxCornerInsets cornerInsets);
  public static LynxRenderBorderStyle LynxToRenderBorderStyle(LynxBorderStyle style);
  public static void LynxDrawBorderSideDoubleGrooveOrRidge(CGContextRef ctx, LynxRenderBorderSideInfo *const info, CGPathRef *_centerInsetPathArr);
  public static void LynxInitBorderCenterPaths(CGPathRef *_centerInsetPath, CGRect rect, LynxBorderStyles borderStyles, UIEdgeInsets borderInsets, LynxBorderRadii cornerRadii, BOOL drawRoundRect);
  public static void LynxDrawBorderSide(CGContextRef ctx, LynxRenderBorderSideInfo *const info, bool drawRoundRect, CGPathRef *centerInsetPathArr);
  public static void LynxDrawBorders(CGContextRef ctx, LynxBorderStyles borderStyles, CGSize size, LynxBorderRadii cornerRadii, UIEdgeInsets borderInsets, LynxBorderColors borderColors, BOOL drawToEdge, BOOL hasCornerRadii, CGPathRef path, CGPathRef insetPath, const LynxCornerInsets cornerInsets);
  public static void LynxDoDrawOutlineSubRect(CGContextRef ctx, LynxBorderStyle style, UIColor *color, float width, CGRect rect);

  typedef struct _LynxRenderBorderSideData LynxRenderBorderSideInfo
  public typedef NS_ENUM(NSInteger, LynxRenderBorderStyle);
  public typedef NS_ENUM(NSInteger, LynxBorderPathId);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSUInteger, LynxTransformRotationType);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSUInteger, LynxFontSrcType);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxLinearGradientDirection);
  public typedef NS_ENUM(NSInteger, LynxRadialCenterType);
  public BOOL LynxSameLynxGradient(LynxGradient *_Nullable left, LynxGradient *_Nullable right);

  typedef void(^ LynxImageLoadCompletionBlock) (UIImage *_Nullable image, NSError *_Nullable error, NSURL *_Nullable imageURL)
  typedef void(^ LynxCDNResourceLoadCompletionBlock) (NSData *_Nullable data, NSError *_Nullable error, NSURL *_Nullable dataPathURL)
  public FOUNDATION_EXPORT NSString* const LynxImageFetcherContextKeyUI LynxImageFetcherContextKeyUI;
  public FOUNDATION_EXPORT NSString* const LynxImageFetcherContextKeyLynxView LynxImageFetcherContextKeyLynxView;
  public FOUNDATION_EXPORT NSString* const LynxImageFetcherContextKeyDownsampling LynxImageFetcherContextKeyDownsampling;
  public FOUNDATION_EXPORT NSString* const LynxImageRequestOptions LynxImageRequestOptions;
  public FOUNDATION_EXPORT NSString* const LynxImageRequestContextModuleExtraData LynxImageRequestContextModuleExtraData;
  public FOUNDATION_EXPORT NSString* const LynxImageSkipRedirection LynxImageSkipRedirection;
  public FOUNDATION_EXPORT NSString* const LynxImageFixNewImageDownsampling LynxImageFixNewImageDownsampling;
  public FOUNDATION_EXPORT NSString* const LynxImageAdditionalCustomInfo LynxImageAdditionalCustomInfo;
  public FOUNDATION_EXPORT NSString* const LynxImageEnableSR LynxImageEnableSR;
  public FOUNDATION_EXPORT NSString* const LynxImageCacheChoice LynxImageCacheChoice;
  public FOUNDATION_EXPORT NSString* const LynxImageRequestPriority LynxImageRequestPriority;
  public FOUNDATION_EXPORT NSString* const LynxImagePlaceholderHashConfig LynxImagePlaceholderHashConfig;

  public NSAttributedStringKey const LynxTextColorGradientKey LynxTextColorGradientKey;
  public NSAttributedStringKey const LynxInlineBackgroundKey LynxInlineBackgroundKey;
  public NSAttributedStringKey const LynxWordBreakKey LynxWordBreakKey;

  public enum LynxUIMethodErrorCode
  kUIMethodSuccess
  kUIMethodUnknown
  kUIMethodNodeNotFound
  kUIMethodMethodNotFound
  kUIMethodParamInvalid
  kUIMethodSelectorNotSupported
  kUIMethodNoUiForNode
  kUIMethodInvalidStateError
  kUIMethodOperationError
  typedef void(^ LynxUIMethodCallbackBlock) (int code, id _Nullable data)

  public enum EVENT_TYPE
  TOUCH_EVENT
  CUSTOM_EVENT

  typedef BOOL(^ onLynxEvent) (LynxEvent *event)
  public typedef NS_ENUM(NSInteger, LynxInnerEventType);

  public NSString* const _Nonnull ON_TOUCHES_DOWN ON_TOUCHES_DOWN;
  public NSString* const _Nonnull ON_TOUCHES_MOVE ON_TOUCHES_MOVE;
  public NSString* const _Nonnull ON_TOUCHES_UP ON_TOUCHES_UP;
  public NSString* const _Nonnull ON_TOUCHES_CANCEL ON_TOUCHES_CANCEL;
  public NSString* const _Nonnull ON_BEGIN ON_BEGIN;
  public NSString* const _Nonnull ON_START ON_START;
  public NSString* const _Nonnull ON_UPDATE ON_UPDATE;
  public NSString* const _Nonnull ON_END ON_END;
  public NS_ASSUME_NONNULL_BEGIN typedef NS_OPTIONS(NSInteger, LynxGestureHandlerOption);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSUInteger, LynxGestureTypeDarwin);

  public NS_ASSUME_NONNULL_BEGIN typedef void(^ LynxViewAnimFinishCallback) (void) LynxViewAnimFinishCallback;

  public typedef NS_ENUM(NSInteger, LynxBackgroundJsRuntimeType);

  public BOOL LynxImageFetchherSupportsProcessor(id< LynxImageFetcher > fetcher);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_OPTIONS(NSInteger, LynxRequestOptions);

  public typedef NS_ENUM(NSInteger, LynxImageRequestType);

  typedef struct MeasureResult MeasureResult

  typedef void(^ LynxOnLayoutBlock) (void)

  public typedef NS_ENUM(NSInteger, LynxMeasureMode);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxShadowNodeType);

  public NS_ASSUME_NONNULL_BEGIN NSAttributedStringKey const LynxInlineViewAttributedStringKey LynxInlineViewAttributedStringKey;
  public NSAttributedStringKey const LynxInlineTextShadowNodeSignKey LynxInlineTextShadowNodeSignKey;
  public NSAttributedStringKey const LynxUsedFontMetricKey LynxUsedFontMetricKey;
  public NSAttributedStringKey const LynxVerticalAlignKey LynxVerticalAlignKey;

  public const NSInteger LynxNumberNotSet LynxNumberNotSet;
  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxOverflow);
  public BOOL LynxSameMeasureMode(LynxMeasureMode left, LynxMeasureMode right);

  public NS_ASSUME_NONNULL_BEGIN BOOL layoutManagerIsTruncated(NSLayoutManager *layoutManager);

  public typedef NS_ENUM(NSInteger, LynxLayoutModelType);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxListLayoutUpdateType);
  public typedef NS_ENUM(NSUInteger, LynxListLayoutType);

  public typedef NS_ENUM(NSUInteger, LynxListScrollStatus);
  public typedef NS_ENUM(NSInteger, LynxListScrollDirection);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxAnchorVisibility);

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSInteger const LynxCollectionInvalidNumberOfItems LynxCollectionInvalidNumberOfItems;
  public FOUNDATION_EXPORT NSInteger const LynxCollectionInvalidNumberOfColumns LynxCollectionInvalidNumberOfColumns;
  public typedef NS_ENUM(NSUInteger, InvalidationType);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSUInteger, LynxCollectionViewLayoutType);
  public typedef NS_ENUM(NSUInteger, LynxCollectionViewLayoutDirection);

  public const CGFloat kInvalidSnapFactor kInvalidSnapFactor;
  public typedef NS_ENUM(NSUInteger, LynxCollectionCellUpdateAnimationType);
  public typedef NS_ENUM(NSInteger, LynxBounceForbiddenDirection);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxListLayoutOrientation);

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSString* const kLynxListNodeAppearEvent kLynxListNodeAppearEvent;
  public FOUNDATION_EXPORT NSString* const kLynxListNodeDisappearEvent kLynxListNodeDisappearEvent;

  public typedef NS_ENUM(NSInteger, LynxListDebugInfoLevel);

  public typedef NS_ENUM(NSUInteger, LynxListScrollState);
  public typedef NS_ENUM(NSUInteger, LynxListScrollPosition);

  typedef void(^ attachLynxPageUI) (__weak NSObject *_Nonnull ui)

  public LynxBasicShape *_Nullable LBSCreateBasicShapeFromArray(NSArray< NSNumber * > *array);
  public CGPathRef LBSCreatePathFromBasicShape(LynxBasicShape *shape, CGSize viewport);
  public LynxBasicShape *_Nullable LBSCreateBasicShapeFromPathData(NSString *data);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxGestureState);

  public NS_ASSUME_NONNULL_BEGIN FOUNDATION_EXPORT NSString* const LynxEventScroll LynxEventScroll;
  public FOUNDATION_EXPORT NSString* const LynxEventScrollEnd LynxEventScrollEnd;
  public FOUNDATION_EXPORT NSString* const LynxEventScrollToUpper LynxEventScrollToUpper;
  public FOUNDATION_EXPORT NSString* const LynxEventScrollToUpperEdge LynxEventScrollToUpperEdge;
  public FOUNDATION_EXPORT NSString* const LynxEventScrollToLower LynxEventScrollToLower;
  public FOUNDATION_EXPORT NSString* const LynxEventScrollToLowerEdge LynxEventScrollToLowerEdge;
  public FOUNDATION_EXPORT NSString* const LynxEventScrollToNormalState LynxEventScrollToNormalState;
  public FOUNDATION_EXPORT NSString* const LynxEventContentSizeChange LynxEventContentSizeChange;
  public FOUNDATION_EXPORT NSString* const LynxEventScrollStateChange LynxEventScrollStateChange;
  public FOUNDATION_EXPORT NSString* const LynxEventScrollToBounce LynxEventScrollToBounce;
  public FOUNDATION_EXPORT NSString* const LynxScrollViewInitialScrollOffset LynxScrollViewInitialScrollOffset;
  public FOUNDATION_EXPORT NSString* const LynxScrollViewInitialScrollIndex LynxScrollViewInitialScrollIndex;
  public FOUNDATION_EXPORT NSString* const LynxEventStickyTop LynxEventStickyTop;
  public FOUNDATION_EXPORT NSString* const LynxEventStickyBottom LynxEventStickyBottom;
  public FOUNDATION_EXPORT NSString* const LynxEventSnap LynxEventSnap;

  typedef void(^ lynx_async_operation_completion_block_t) (id _Nullable value, BOOL canceled)
  typedef BOOL(^ lynx_iscancelled_block_t) (void)
  typedef void(^ lynx_async_display_completion_block_t) (UIImage *image)
  typedef UIImage *_Nullable(^ lynx_async_get_background_image_block_t) (void)
  public NS_ASSUME_NONNULL_BEGIN typedef id< NSObject > _Nullable(^ lynx_async_operation_block_t) (void) lynx_async_operation_block_t;
  public FOUNDATION_EXPORT short const OVERFLOW_XY_VAL OVERFLOW_XY_VAL;
  public FOUNDATION_EXPORT short const OVERFLOW_HIDDEN_VAL OVERFLOW_HIDDEN_VAL;

  public enum LynxPropStatus
  kLynxPropEnable
  kLynxPropDisable
  kLynxPropUndefined
  typedef struct TransOffset TransOffset
  typedef void(^ LynxNodeReadyBlock) (LynxUI *)

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSUInteger, LynxBounceViewDirection);

  public NS_ASSUME_NONNULL_BEGIN UIKIT_EXTERN NSNotificationName const LynxImpressionWillManualExposureNotification LynxImpressionWillManualExposureNotification;
  public UIKIT_EXTERN NSNotificationName const LynxImpressionLynxViewIDNotificationKey LynxImpressionLynxViewIDNotificationKey;
  public UIKIT_EXTERN NSNotificationName const LynxImpressionStatusNotificationKey LynxImpressionStatusNotificationKey;
  public UIKIT_EXTERN NSNotificationName const LynxImpressionForceImpressionBoolKey LynxImpressionForceImpressionBoolKey;

  public typedef NS_ENUM(NSInteger, HoverPosition);

  typedef NSInteger(^ UIScrollViewGetIndexFromView) (UIView *view)
  typedef CGRect(^ UIScrollViewGetViewRectAtIndex) (NSInteger index)
  typedef void(^ UIScrollViewWillSnapToCallback) (NSInteger position, CGPoint offset)
  typedef BOOL(^ UIScrollViewLynxCompletion) (BOOL scrollEnabledAtStart, BOOL completed)
  typedef CGPoint(^ UIScrollViewLynxProgressInterception) (double timeProgress, double distProgress, CGPoint contentOffset)
  typedef double(^ UIScrollViewLynxTimingFunction) (double input)
  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSUInteger, LynxScrollViewTouchBehavior);

  public NS_ASSUME_NONNULL_BEGIN typedef NS_ENUM(NSInteger, LynxGestureConsumeStatus);

  public NS_ASSUME_NONNULL_BEGIN NSString* const KEY_LYNX_ENABLE_MULTI_ASYNC_THREAD KEY_LYNX_ENABLE_MULTI_ASYNC_THREAD;
  public NSString* const KEY_LYNX_PLATFORM_CONFIG KEY_LYNX_PLATFORM_CONFIG;
  public NSString* const KEY_LYNX_RUNTIME_TYPE KEY_LYNX_RUNTIME_TYPE;
  public NSString* const KEY_LYNX_ENABLE_BYTECODE KEY_LYNX_ENABLE_BYTECODE;
  public NSString* const KEY_LYNX_BYTECODE_URL KEY_LYNX_BYTECODE_URL;
  public NSString* const KEY_LYNX_ENABLE_VSYNC_ALIGNED_MESSAGE_LOOP KEY_LYNX_ENABLE_VSYNC_ALIGNED_MESSAGE_LOOP;

  typedef struct LBSPathConsumer LBSPathConsumer
  public CGPathRef LBSCreatePathFromData(const char *data);

  public enum LBSPathConsumerType
  kLBSPathConsumerTypeUnknown
  kLBSPathConsumerTypeCoreGraphics
  kLBSPathConsumerTypeString
  typedef enum LBSPathConsumerType LBSPathConsumerType
  typedef struct LBSPathConsumer LBSPathConsumer
  public void LBSParsePathWithConsumer(const char *data, LBSPathConsumer *consumer);

  typedef void(^ LynxPropsDidUpdateBlockReadyBlock) (LynxUI *)

  public enum TagSupportedState
  LynxRootTag
  LynxSupportedTag
  LynxUnsupportedTag

