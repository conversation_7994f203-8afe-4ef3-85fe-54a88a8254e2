// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

package com.lynx.jsbridge.jsi;

import android.support.annotation.Keep;
import android.support.annotation.RestrictTo;
import com.lynx.tasm.base.CalledByNative;

/**
 * An internal interface used at runtime to assist in reading fields of `LynxJSIObject`.
 * It should by implemented by `AbsLynxJSIObjectDescriptor` and generated by
 * `LynxJSPropertyProcessor`. Do not implement this interface manually.
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Keep
public interface ILynxJSIObjectDescriptor {
  /**
   * get the ClassName of origin JSIObject instance, used to get methods of JSIObject by reflection.
   */
  String getClassName();

  /**
   * get the names of JSProperty fields, used to getPropertyNames in JS.
   */
  String[] getFields();

  /**
   * get the descriptor info of JSProperty field, used to get property value in JS by reflection.
   * This method is concurrent.
   */
  @CalledByNative String[] getLynxObjectDescriptorInfo(String fieldName);
}
