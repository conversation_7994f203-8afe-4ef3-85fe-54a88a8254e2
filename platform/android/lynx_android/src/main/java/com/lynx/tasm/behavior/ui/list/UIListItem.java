// Copyright 2022 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
package com.lynx.tasm.behavior.ui.list;

import com.lynx.tasm.behavior.LynxContext;
import com.lynx.tasm.behavior.ui.view.UIComponent;

// TODO(hujing.1): separate UIListItem with UIComponent
public class UIListItem extends UIComponent {
  public UIListItem(LynxContext context) {
    super(context);
  }
}
