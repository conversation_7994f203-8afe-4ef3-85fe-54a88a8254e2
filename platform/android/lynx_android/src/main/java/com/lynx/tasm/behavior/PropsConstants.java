// Copyright 2019 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

package com.lynx.tasm.behavior;

public class PropsConstants {
  public static final String FLATTEN = "flatten";
  public static final String OVERFLOW = "overflow";
  public static final String BACKGROUND = "background";
  public static final String BACKGROUND_COLOR = "background-color";
  public static final String BACKGROUND_IMAGE = "background-image";
  public static final String BACKGROUND_ORIGIN = "background-origin";
  public static final String BACKGROUND_POSITION = "background-position";
  public static final String BACKGROUND_REPEAT = "background-repeat";
  public static final String BACKGROUND_SIZE = "background-size";
  public static final String MASK_IMAGE = "mask-image";
  public static final String MASK_ORIGIN = "mask-origin";
  public static final String MASK_POSITION = "mask-position";
  public static final String MASK_REPEAT = "mask-repeat";
  public static final String MASK_SIZE = "mask-size";
  public static final String MASK_CLIP = "mask-clip";
  public static final String COLOR = "color";
  public static final String TEXT_SHADOW = "text-shadow";
  public static final String FONT_SIZE = "font-size";
  public static final String ENABLE_FONT_SCALING = "enable-font-scaling";
  public static final String FONT_WEIGHT = "font-weight";
  public static final String FONT_STYLE = "font-style";
  public static final String FONT_FAMILY = "font-family";
  public static final String LINE_HEIGHT = "line-height";
  public static final String LETTER_SPACING = "letter-spacing";
  public static final String LINE_SPACING = "line-spacing";
  public static final String WHITE_SPACE = "white-space";
  public static final String INCLUDE_FONT_PADDING = "include-font-padding";
  public static final String TEXT_OVERFLOW = "text-overflow";
  public static final String TEXT_MAXLINE = "text-maxline";
  public static final String TEXT_MAXLENGTH = "text-maxlength";
  public static final String SKIP_REDIRECTION = "skip-redirection";
  public static final String SKIP_PLACEHOLDER_REDIRECTION = "skip-placeholder-redirection";
  public static final String SRC = "src";
  public static final String PLACEHOLDER = "placeholder";
  public static final String MODE = "mode";
  public static final String BLUR_RADIUS = "blur-radius";
  public static final String AUTO_SIZE = "auto-size";
  public static final String TEXT_ALIGN = "text-align";
  public static final String TEXT_DECORATION = "text-decoration";
  public static final String TEXT_STROKE_WIDTH = "text-stroke-width";
  public static final String TEXT_STROKE_COLOR = "text-stroke-color";
  public static final String TEXT_FAKE_BOLD = "text-fake-bold";
  public static final String OPACITY = "opacity";
  public static final String VISIBILITY = "visibility";
  public static final String BORDER_WIDTH = "border-width";
  public static final String BORDER_LEFT_WIDTH = "border-left-width";
  public static final String BORDER_TOP_WIDTH = "border-top-width";
  public static final String BORDER_RIGHT_WIDTH = "border-right-width";
  public static final String BORDER_BOTTOM_WIDTH = "border-bottom-width";
  public static final String BORDER_RADIUS = "border-radius";
  public static final String BORDER_TOP_LEFT_RADIUS = "border-top-left-radius";
  public static final String BORDER_TOP_RIGHT_RADIUS = "border-top-right-radius";
  public static final String BORDER_BOTTOM_LEFT_RADIUS = "border-bottom-left-radius";
  public static final String BORDER_BOTTOM_RIGHT_RADIUS = "border-bottom-right-radius";
  public static final String BORDER_COLOR = "border-color";
  public static final String BORDER_LEFT_COLOR = "border-left-color";
  public static final String BORDER_RIGHT_COLOR = "border-right-color";
  public static final String BORDER_TOP_COLOR = "border-top-color";
  public static final String BORDER_BOTTOM_COLOR = "border-bottom-color";
  public static final String BORDER_STYLE = "border-style";
  public static final String BORDER_LEFT_STYLE = "border-left-style";
  public static final String BORDER_RIGHT_STYLE = "border-right-style";
  public static final String BORDER_TOP_STYLE = "border-top-style";
  public static final String BORDER_BOTTOM_STYLE = "border-bottom-style";
  public static final String BORDER = "border";
  public static final String BORDER_LEFT = "border-left";
  public static final String BORDER_RIGHT = "border-right";
  public static final String BORDER_TOP = "border-top";
  public static final String BORDER_BOTTOM = "border-bottom";
  public static final String TRANSFORM = "transform";
  public static final String TRANSLATE_X = "translateX";
  public static final String TRANSLATE_Y = "translateY";
  public static final String TRANSLATE_Z = "translateZ";
  public static final String SCALE_X = "scaleX";
  public static final String SCALE_Y = "scaleY";
  public static final String SCALE_Z = "scaleZ";
  public static final String ROTATE = "rotate";
  public static final String ROTATE_X = "rotateX";
  public static final String ROTATE_Y = "rotateY";
  public static final String ROTATE_Z = "rotateZ";
  public static final String SKEW_X = "skewX";
  public static final String SKEW_Y = "skewY";
  public static final String ANIMATION = "animation";
  public static final String NUMBER_OF_LINES = "numberOfLines";
  public static final String WORD_BREAK_STRATEGY = "word-break";
  public static final String ALLOW_FONT_SCALING = "allowFontScaling";
  public static final String BOX_SHADOW = "box-shadow";
  public static final String TRANSFORM_ORIGIN = "transform-origin";
  public static final String NAME = "name";
  public static final String ID_SELECTOR = "idSelector";
  public static final String REACT_REF_ID = "react-ref";
  public static final String OVERFLOW_X = "overflow-x";
  public static final String OVERFLOW_Y = "overflow-y";
  public static final String USER_INTERACTION_ENABLED = "user-interaction-enabled";
  public static final String NATIVE_INTERACTION_ENABLED = "native-interaction-enabled";
  public static final String TEST_TAG = "lynx-test-tag";
  public static final String BACKGROUND_CLIP = "background-clip";
  public static final String OUTLINE = "outline";
  public static final String OUTLINE_COLOR = "outline-color";
  public static final String OUTLINE_STYLE = "outline-style";
  public static final String OUTLINE_WIDTH = "outline-width";
  public static final String VERTICAL_ALIGN = "vertical-align";
  public static final String REPEAT = "repeat";
  public static final String CLIP_TO_RADIUS = "clip-radius";
  public static final String COVER_START = "cover-start";
  public static final String CARET_COLOR = "caret-color";
  public static final String FOCUSABLE = "focusable";
  public static final String IGNORE_FOCUS = "ignore-focus";
  public static final String ACCESSIBILITY_LABEL = "accessibility-label";
  public static final String ACCESSIBILITY_ELEMENT = "accessibility-element";
  public static final String ACCESSIBILITY_ENABLE_TAP = "accessibility-enable-tap";
  public static final String ACCESSIBILITY_ELEMENTS = "accessibility-elements";
  public static final String ACCESSIBILITY_ID = "a11y-id";
  public static final String ACCESSIBILITY_ELEMENTS_A11Y = "accessibility-elements-a11y";
  public static final String ACCESSIBILITY_EXCLUSIVE_FOCUS = "accessibility-exclusive-focus";
  public static final String ACCESSIBILITY_ELEMENTS_HIDDEN = "accessibility-elements-hidden";
  public static final String ACCESSIBILITY_ACTIONS = "accessibility-actions";
  public static final String CONSUME_HOVER_EVENT = "android-consume-hover-event";
  public static final String ACCESSIBILITY_TRAITS = "accessibility-traits";
  public static final String ACCESSIBILITY_VALUE = "accessibility-value";
  public static final String ACCESSIBILITY_STATUS = "accessibility-status";
  public static final String ACCESSIBILITY_HEADING = "accessibility-heading";

  public static final String ACCESSIBILITY_ROLE_DESCRIPTION = "accessibility-role-description";

  public static final String ANDROID_ACCESSIBILITY_KEEP_FOCUSED =
      "android-accessibility-keep-focused";
  public static final String SHARED_ELEMENT = "shared-element";
  public static final String ENTER_TRANSITION_NAME = "enter-transition-name";
  public static final String EXIT_TRANSITION_NAME = "exit-transition-name";
  public static final String RESUME_TRANSITION_NAME = "resume-transition-name";
  public static final String PAUSE_TRANSITION_NAME = "pause-transition-name";
  public static final String CAP_INSETS = "capInsets";
  public static final String CAP_INSETS_BACKUP = "cap-insets";
  public static final String CAP_INSETS_SCALE = "cap-insets-scale";
  public static final String LOOP_COUNT = "loop-count";
  public static final String Z_INDEX = "z-index";
  public static final String OVERLAP = "overlap";
  public static final String IMAGE_SUBSAMPLE = "subsample";
  public static final String PRE_FETCH_WIDTH = "prefetch-width";
  public static final String PRE_FETCH_HEIGHT = "prefetch-height";
  public static final String LOCAL_CACHE = "local-cache";
  public static final String ITEM_KEY = "item-key";
  public static final String DISABLE_DEFAULT_PLACEHOLDER = "disable-default-placeholder";
  public static final String FRESCO_VISIBLE = "fresco-visible";
  public static final String FRESCO_ATTACH = "fresco-attach";
  public static final String IAMGE_CONFIG = "image-config";
  public static final String ENABLE_DETACHED_CLEAR = "enable-detach-clear";
  public static final String FRESCO_NINE_PATCH = "fresco-nine-patch";
  public static final String POSITION = "position";
  public static final String ENABLE_SCROLL_MONITOR = "enable-scroll-monitor";
  public static final String SCROLL_MONITOR_TAG = "scroll-monitor-tag";
  public static final String DRIECTION = "direction";
  public static final String DATASET = "dataset";
  public static final String CONSUME_ANDROID_EVENTS = "consume-android-events";
  public static final String FIX_FRESCO_BUG = "fix-fresco-bug";
  public static final String INTERSECTION_OBSERVERS = "intersection-observers";
  public static final String CONSUME_SLIDE_EVENT = "consume-slide-event";
  public static final String BLOCK_NATIVE_EVENT = "block-native-event";
  public static final String BLOCK_NATIVE_EVENT_AREAS = "block-native-event-areas";
  public static final String ENABLE_TOUCH_PSEUDO_PROPAGATION = "enable-touch-pseudo-propagation";
  public static final String FILTER = "filter";
  public static final String EXPOSURE_ID = "exposure-id";
  public static final String EXPOSURE_SCENE = "exposure-scene";
  public static final String EXPOSURE_SCREEN_MARGIN_TOP = "exposure-screen-margin-top";
  public static final String EXPOSURE_SCREEN_MARGIN_BOTTOM = "exposure-screen-margin-bottom";
  public static final String EXPOSURE_SCREEN_MARGIN_LEFT = "exposure-screen-margin-left";
  public static final String EXPOSURE_SCREEN_MARGIN_RIGHT = "exposure-screen-margin-right";
  public static final String ENABLE_EXPOSURE_UI_MARGIN = "enable-exposure-ui-margin";
  public static final String EXPOSURE_UI_MARGIN_TOP = "exposure-ui-margin-top";
  public static final String EXPOSURE_UI_MARGIN_BOTTOM = "exposure-ui-margin-bottom";
  public static final String EXPOSURE_UI_MARGIN_LEFT = "exposure-ui-margin-left";
  public static final String EXPOSURE_UI_MARGIN_RIGHT = "exposure-ui-margin-right";
  public static final String EXPOSURE_AREA = "exposure-area";
  public static final String SUSPENDABLE = "suspendable";
  public static final String PERSPECTIVE = "perspective";
  public static final String RENDER_TO_HARDWARE_TEXTURE = "hardware-layer";
  public static final String BITMAP_GRADIENT = "bitmap-gradient";
  public static final String TEXT_INDENT = "text-indent";
  public static final String ANDROID_EMOJI_COMPAT = "android-emoji-compat";
  public static final String TRANSFORM_ORDER = "transform-order";
  public static final String EVENT_THROUGH = "event-through";
  public static final String DISABLE_DEFAULT_RESIZE = "disable-default-resize";
  public static final String DEFER_SRC_INVALIDATION = "defer-src-invalidation";
  public static final String AUTO_PLAY = "autoplay";
  public static final String ANDROID_IMAGE_SIMPLE_KEY = "android-simple-cache-key";
  public static final String TINT_COLOR = "tint-color";
  public static final String ENABLE_REUSE_ANIMATION_STATE = "enable-reuse-animation-state";
  public static final String LIST = "list";
  public static final String HYPHENS = "hyphens";
  public static final String TEXT = "text";

  // When using the boundingClientRect and requestUIInfo methods on iOS, the transform property is
  // taken into account when computing the area, but on Android it is not.
  //
  // Based on developer feedback, considering the transform property is expected behavior and also
  // more in line with W3C standards. Therefore, based on LynxUIHelper and ViewHelper, in order to
  // align with the behavior on iOS and to avoid breaking the performance of existing businesses,
  // front-end developers can pass {androidEnableTransformProps:true} when using the
  // boundingClientRect and requestUIInfo methods on Android to enable consideration of the Android
  // transform property when computing the area.
  //
  // Here is an example of how to use ReactLynx, the TTML syntax is similar:
  //
  // ```jsx
  // export default class extends ReactLynx.ReactComponent {
  //  // React-style ref is also supported in ReactLynx
  //  handleTap = () => {
  //    this.createSelectorQuery()
  //      .select('#video')
  //      .invoke({
  //        method: 'boundingClientRect',
  //        params: {
  //          androidEnableTransformProps: true
  //        },
  //        success: function (res) {
  //          // res is the boundingClientRect data
  //        },
  //        fail: function (res) {
  //          if (res.code === ErrorCode.NODE_NOT_FOUND) {
  //            // error handler, can use res.data get more details
  //          }
  //        },
  //      })
  //      .exec();
  //  };
  //
  //  render() {
  //    return <view id='video' bindtap={this.handleTap} />;
  //  }
  //}
  //```
  //
  // Since androidEnableTransformProps is a parameter used in the front-end JS API rather than a
  // LynxUI attribute, use lowercase and camel case naming instead of hyphenated naming.
  public static final String ANDROID_ENABLE_TRANSFORM_PROPS = "androidEnableTransformProps";
  public static final String IMAGE_TRANSITION_STYLE = "image-transition-style";
  public static final String X_AUTO_FONT_SIZE = "-x-auto-font-size";
  public static final String X_AUTO_FONT_SIZE_PRESET_SIZES = "-x-auto-font-size-preset-sizes";
  public static final String EXTRA_LOAD_INFO = "extra-load-info";
  public static final String HIT_SLOP = "hit-slop";
  public static final String HIT_SLOP_TOP = "top";
  public static final String HIT_SLOP_BOTTOM = "bottom";
  public static final String HIT_SLOP_LEFT = "left";
  public static final String HIT_SLOP_RIGHT = "right";
  public static final String IMAGE_RENDERING = "image-rendering";

  public static final String ENABLE_RESOURCE_HINT = "enable-resource-hint";

  public static final String ENABLE_CUSTOM_GIF_DECODER = "enable-custom-gif-decoder";

  public static final String ENABLE_IMAGE_ASYNC_REQUEST = "async-request";
  public static final String IMAGE_PRIORITY = "fetch-priority";

  public static final String IMAGE_CACHE_CHOICE = "cache-choice";

  public static final String IMAGE_PLACE_HOLDER_HASH_CONFIG = "placeholder-hash-config";
}
