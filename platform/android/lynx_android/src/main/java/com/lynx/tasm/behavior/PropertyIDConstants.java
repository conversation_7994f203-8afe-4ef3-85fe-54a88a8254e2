// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

package com.lynx.tasm.behavior;

// AUTO INSERT, DON'T CHANGE IT!
public final class PropertyIDConstants {
  public static final String[] PROPERTY_CONSTANT = new String[] {
      "AUTO_INSERTED_BEGIN",
      //-------------------

      "top",
      "left",
      "right",
      "bottom",
      "position",
      "box-sizing",
      "background-color",
      "border-left-color",
      "border-right-color",
      "border-top-color",
      "border-bottom-color",
      "border-radius",
      "border-top-left-radius",
      "border-bottom-left-radius",
      "border-top-right-radius",
      "border-bottom-right-radius",
      "border-width",
      "border-left-width",
      "border-right-width",
      "border-top-width",
      "border-bottom-width",
      "color",
      "opacity",
      "display",
      "overflow",
      "height",
      "width",
      "max-width",
      "min-width",
      "max-height",
      "min-height",
      "padding",
      "padding-left",
      "padding-right",
      "padding-top",
      "padding-bottom",
      "margin",
      "margin-left",
      "margin-right",
      "margin-top",
      "margin-bottom",
      "white-space",
      "letter-spacing",
      "text-align",
      "line-height",
      "text-overflow",
      "font-size",
      "font-weight",
      "flex",
      "flex-grow",
      "flex-shrink",
      "flex-basis",
      "flex-direction",
      "flex-wrap",
      "align-items",
      "align-self",
      "align-content",
      "justify-content",
      "background",
      "border-color",
      "font-family",
      "font-style",
      "transform",
      "animation",
      "animation-name",
      "animation-duration",
      "animation-timing-function",
      "animation-delay",
      "animation-iteration-count",
      "animation-direction",
      "animation-fill-mode",
      "animation-play-state",
      "line-spacing",
      "border-style",
      "order",
      "box-shadow",
      "transform-origin",
      "linear-orientation",
      "linear-weight-sum",
      "linear-weight",
      "linear-gravity",
      "linear-layout-gravity",
      "layout-animation-create-duration",
      "layout-animation-create-timing-function",
      "layout-animation-create-delay",
      "layout-animation-create-property",
      "layout-animation-delete-duration",
      "layout-animation-delete-timing-function",
      "layout-animation-delete-delay",
      "layout-animation-delete-property",
      "layout-animation-update-duration",
      "layout-animation-update-timing-function",
      "layout-animation-update-delay",
      "adapt-font-size",
      "aspect-ratio",
      "text-decoration",
      "text-shadow",
      "background-image",
      "background-position",
      "background-origin",
      "background-repeat",
      "background-size",
      "border",
      "visibility",
      "border-right",
      "border-left",
      "border-top",
      "border-bottom",
      "transition",
      "transition-property",
      "transition-duration",
      "transition-delay",
      "transition-timing-function",
      "content",
      "border-left-style",
      "border-right-style",
      "border-top-style",
      "border-bottom-style",
      "implicit-animation",
      "overflow-x",
      "overflow-y",
      "word-break",
      "background-clip",
      "outline",
      "outline-color",
      "outline-style",
      "outline-width",
      "vertical-align",
      "caret-color",
      "direction",
      "relative-id",
      "relative-align-top",
      "relative-align-right",
      "relative-align-bottom",
      "relative-align-left",
      "relative-top-of",
      "relative-right-of",
      "relative-bottom-of",
      "relative-left-of",
      "relative-layout-once",
      "relative-center",
      "enter-transition-name",
      "exit-transition-name",
      "pause-transition-name",
      "resume-transition-name",
      "flex-flow",
      "z-index",
      "text-decoration-color",
      "linear-cross-gravity",
      "margin-inline-start",
      "margin-inline-end",
      "padding-inline-start",
      "padding-inline-end",
      "border-inline-start-color",
      "border-inline-end-color",
      "border-inline-start-width",
      "border-inline-end-width",
      "border-inline-start-style",
      "border-inline-end-style",
      "border-start-start-radius",
      "border-end-start-radius",
      "border-start-end-radius",
      "border-end-end-radius",
      "relative-align-inline-start",
      "relative-align-inline-end",
      "relative-inline-start-of",
      "relative-inline-end-of",
      "inset-inline-start",
      "inset-inline-end",
      "mask-image",
      "grid-template-columns",
      "grid-template-rows",
      "grid-auto-columns",
      "grid-auto-rows",
      "grid-column-span",
      "grid-row-span",
      "grid-column-start",
      "grid-column-end",
      "grid-row-start",
      "grid-row-end",
      "grid-column-gap",
      "grid-row-gap",
      "justify-items",
      "justify-self",
      "grid-auto-flow",
      "filter",
      "list-main-axis-gap",
      "list-cross-axis-gap",
      "linear-direction",
      "perspective",
      "cursor",
      "text-indent",
      "clip-path",
      "text-stroke",
      "text-stroke-width",
      "text-stroke-color",
      "-x-auto-font-size",
      "-x-auto-font-size-preset-sizes",
      "mask",
      "mask-repeat",
      "mask-position",
      "mask-clip",
      "mask-origin",
      "mask-size",
      "gap",
      "column-gap",
      "row-gap",
      "image-rendering",
      "hyphens",
      "-x-app-region",
      //-------------------
      "AUTO_INSERTED_END",
  };

  public static final int Top = 1;

  public static final int Left = 2;

  public static final int Right = 3;

  public static final int Bottom = 4;

  public static final int Position = 5;

  public static final int BoxSizing = 6;

  public static final int BackgroundColor = 7;

  public static final int BorderLeftColor = 8;

  public static final int BorderRightColor = 9;

  public static final int BorderTopColor = 10;

  public static final int BorderBottomColor = 11;

  public static final int BorderRadius = 12;

  public static final int BorderTopLeftRadius = 13;

  public static final int BorderBottomLeftRadius = 14;

  public static final int BorderTopRightRadius = 15;

  public static final int BorderBottomRightRadius = 16;

  public static final int BorderWidth = 17;

  public static final int BorderLeftWidth = 18;

  public static final int BorderRightWidth = 19;

  public static final int BorderTopWidth = 20;

  public static final int BorderBottomWidth = 21;

  public static final int Color = 22;

  public static final int Opacity = 23;

  public static final int Display = 24;

  public static final int Overflow = 25;

  public static final int Height = 26;

  public static final int Width = 27;

  public static final int MaxWidth = 28;

  public static final int MinWidth = 29;

  public static final int MaxHeight = 30;

  public static final int MinHeight = 31;

  public static final int Padding = 32;

  public static final int PaddingLeft = 33;

  public static final int PaddingRight = 34;

  public static final int PaddingTop = 35;

  public static final int PaddingBottom = 36;

  public static final int Margin = 37;

  public static final int MarginLeft = 38;

  public static final int MarginRight = 39;

  public static final int MarginTop = 40;

  public static final int MarginBottom = 41;

  public static final int WhiteSpace = 42;

  public static final int LetterSpacing = 43;

  public static final int TextAlign = 44;

  public static final int LineHeight = 45;

  public static final int TextOverflow = 46;

  public static final int FontSize = 47;

  public static final int FontWeight = 48;

  public static final int Flex = 49;

  public static final int FlexGrow = 50;

  public static final int FlexShrink = 51;

  public static final int FlexBasis = 52;

  public static final int FlexDirection = 53;

  public static final int FlexWrap = 54;

  public static final int AlignItems = 55;

  public static final int AlignSelf = 56;

  public static final int AlignContent = 57;

  public static final int JustifyContent = 58;

  public static final int Background = 59;

  public static final int BorderColor = 60;

  public static final int FontFamily = 61;

  public static final int FontStyle = 62;

  public static final int Transform = 63;

  public static final int Animation = 64;

  public static final int AnimationName = 65;

  public static final int AnimationDuration = 66;

  public static final int AnimationTimingFunction = 67;

  public static final int AnimationDelay = 68;

  public static final int AnimationIterationCount = 69;

  public static final int AnimationDirection = 70;

  public static final int AnimationFillMode = 71;

  public static final int AnimationPlayState = 72;

  public static final int LineSpacing = 73;

  public static final int BorderStyle = 74;

  public static final int Order = 75;

  public static final int BoxShadow = 76;

  public static final int TransformOrigin = 77;

  public static final int LinearOrientation = 78;

  public static final int LinearWeightSum = 79;

  public static final int LinearWeight = 80;

  public static final int LinearGravity = 81;

  public static final int LinearLayoutGravity = 82;

  public static final int LayoutAnimationCreateDuration = 83;

  public static final int LayoutAnimationCreateTimingFunction = 84;

  public static final int LayoutAnimationCreateDelay = 85;

  public static final int LayoutAnimationCreateProperty = 86;

  public static final int LayoutAnimationDeleteDuration = 87;

  public static final int LayoutAnimationDeleteTimingFunction = 88;

  public static final int LayoutAnimationDeleteDelay = 89;

  public static final int LayoutAnimationDeleteProperty = 90;

  public static final int LayoutAnimationUpdateDuration = 91;

  public static final int LayoutAnimationUpdateTimingFunction = 92;

  public static final int LayoutAnimationUpdateDelay = 93;

  public static final int AdaptFontSize = 94;

  public static final int AspectRatio = 95;

  public static final int TextDecoration = 96;

  public static final int TextShadow = 97;

  public static final int BackgroundImage = 98;

  public static final int BackgroundPosition = 99;

  public static final int BackgroundOrigin = 100;

  public static final int BackgroundRepeat = 101;

  public static final int BackgroundSize = 102;

  public static final int Border = 103;

  public static final int Visibility = 104;

  public static final int BorderRight = 105;

  public static final int BorderLeft = 106;

  public static final int BorderTop = 107;

  public static final int BorderBottom = 108;

  public static final int Transition = 109;

  public static final int TransitionProperty = 110;

  public static final int TransitionDuration = 111;

  public static final int TransitionDelay = 112;

  public static final int TransitionTimingFunction = 113;

  public static final int Content = 114;

  public static final int BorderLeftStyle = 115;

  public static final int BorderRightStyle = 116;

  public static final int BorderTopStyle = 117;

  public static final int BorderBottomStyle = 118;

  public static final int ImplicitAnimation = 119;

  public static final int OverflowX = 120;

  public static final int OverflowY = 121;

  public static final int WordBreak = 122;

  public static final int BackgroundClip = 123;

  public static final int Outline = 124;

  public static final int OutlineColor = 125;

  public static final int OutlineStyle = 126;

  public static final int OutlineWidth = 127;

  public static final int VerticalAlign = 128;

  public static final int CaretColor = 129;

  public static final int Direction = 130;

  public static final int RelativeId = 131;

  public static final int RelativeAlignTop = 132;

  public static final int RelativeAlignRight = 133;

  public static final int RelativeAlignBottom = 134;

  public static final int RelativeAlignLeft = 135;

  public static final int RelativeTopOf = 136;

  public static final int RelativeRightOf = 137;

  public static final int RelativeBottomOf = 138;

  public static final int RelativeLeftOf = 139;

  public static final int RelativeLayoutOnce = 140;

  public static final int RelativeCenter = 141;

  public static final int EnterTransitionName = 142;

  public static final int ExitTransitionName = 143;

  public static final int PauseTransitionName = 144;

  public static final int ResumeTransitionName = 145;

  public static final int FlexFlow = 146;

  public static final int ZIndex = 147;

  public static final int TextDecorationColor = 148;

  public static final int LinearCrossGravity = 149;

  public static final int MarginInlineStart = 150;

  public static final int MarginInlineEnd = 151;

  public static final int PaddingInlineStart = 152;

  public static final int PaddingInlineEnd = 153;

  public static final int BorderInlineStartColor = 154;

  public static final int BorderInlineEndColor = 155;

  public static final int BorderInlineStartWidth = 156;

  public static final int BorderInlineEndWidth = 157;

  public static final int BorderInlineStartStyle = 158;

  public static final int BorderInlineEndStyle = 159;

  public static final int BorderStartStartRadius = 160;

  public static final int BorderEndStartRadius = 161;

  public static final int BorderStartEndRadius = 162;

  public static final int BorderEndEndRadius = 163;

  public static final int RelativeAlignInlineStart = 164;

  public static final int RelativeAlignInlineEnd = 165;

  public static final int RelativeInlineStartOf = 166;

  public static final int RelativeInlineEndOf = 167;

  public static final int InsetInlineStart = 168;

  public static final int InsetInlineEnd = 169;

  public static final int MaskImage = 170;

  public static final int GridTemplateColumns = 171;

  public static final int GridTemplateRows = 172;

  public static final int GridAutoColumns = 173;

  public static final int GridAutoRows = 174;

  public static final int GridColumnSpan = 175;

  public static final int GridRowSpan = 176;

  public static final int GridColumnStart = 177;

  public static final int GridColumnEnd = 178;

  public static final int GridRowStart = 179;

  public static final int GridRowEnd = 180;

  public static final int GridColumnGap = 181;

  public static final int GridRowGap = 182;

  public static final int JustifyItems = 183;

  public static final int JustifySelf = 184;

  public static final int GridAutoFlow = 185;

  public static final int Filter = 186;

  public static final int ListMainAxisGap = 187;

  public static final int ListCrossAxisGap = 188;

  public static final int LinearDirection = 189;

  public static final int Perspective = 190;

  public static final int Cursor = 191;

  public static final int TextIndent = 192;

  public static final int ClipPath = 193;

  public static final int TextStroke = 194;

  public static final int TextStrokeWidth = 195;

  public static final int TextStrokeColor = 196;

  public static final int XAutoFontSize = 197;

  public static final int XAutoFontSizePresetSizes = 198;

  public static final int Mask = 199;

  public static final int MaskRepeat = 200;

  public static final int MaskPosition = 201;

  public static final int MaskClip = 202;

  public static final int MaskOrigin = 203;

  public static final int MaskSize = 204;

  public static final int Gap = 205;

  public static final int ColumnGap = 206;

  public static final int RowGap = 207;

  public static final int ImageRendering = 208;

  public static final int Hyphens = 209;

  public static final int XAppRegion = 210;
}
// AUTO INSERT, DON'T CHANGE IT!
