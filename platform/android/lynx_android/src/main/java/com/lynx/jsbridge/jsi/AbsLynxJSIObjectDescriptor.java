// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
package com.lynx.jsbridge.jsi;

import android.support.annotation.Keep;
import android.support.annotation.RestrictTo;
import java.util.concurrent.ConcurrentHashMap;

/**
 * An internal abstract class used at runtime to assist in reading fields of `LynxJSIObject`.
 * It should by generated by `LynxJSPropertyProcessor`.
 * Do not extend this class manually.
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Keep
public abstract class AbsLynxJSIObjectDescriptor implements ILynxJSIObjectDescriptor {
  private volatile String[] mFields = null;
  private volatile ConcurrentHashMap<String, LynxJSPropertyDescriptor> mFieldInfos = null;

  /**
   * get the map of JSPropertyDescriptor, this method will be generated by LynxJSPropertyProcessor
   */
  protected abstract ConcurrentHashMap<String, LynxJSPropertyDescriptor> createFieldInfos();

  /**
   * ClassName of origin JSIObject instance, this method will be generated by
   * LynxJSPropertyProcessor
   */
  @Override public abstract String getClassName();

  /**
   * get fields from JSPropertyDescriptor map
   */
  @Override
  public String[] getFields() {
    ensureFieldInfos();
    if (mFields != null) {
      return mFields;
    }
    synchronized (this) {
      if (mFields == null) {
        mFields = mFieldInfos.keySet().toArray(new String[0]);
      }
      return mFields;
    }
  }

  /**
   * get descriptor info from JSPropertyDescriptor map
   */
  @Override
  public String[] getLynxObjectDescriptorInfo(String fieldName) {
    ensureFieldInfos();
    LynxJSPropertyDescriptor descriptor = mFieldInfos.get(fieldName);
    String[] descriptorInfo = descriptor != null
        ? new String[] {descriptor.fieldName, descriptor.fieldJNIDescriptor}
        : null;
    return descriptorInfo;
  }

  /**
   * lazy create JSPropertyDescriptor map
   */
  private void ensureFieldInfos() {
    if (mFieldInfos != null) {
      return;
    }
    synchronized (this) {
      if (mFieldInfos != null) {
        return;
      }
      mFieldInfos = createFieldInfos();
    }
  }
}
