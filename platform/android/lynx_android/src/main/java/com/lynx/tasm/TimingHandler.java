// Copyright 2021 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

package com.lynx.tasm;

import java.util.HashMap;
import java.util.Map;

public class TimingHandler {
  public static final String CREATE_LYNX_START = "createLynxStart";
  public static final String CREATE_LYNX_END = "createLynxEnd";

  public static final String CREATE_LYNX_ENGIN_START = "createLynxEnginStart";
  public static final String CREATE_LYNX_ENGIN_END = "createLynxEnginEnd";

  public static final String LOAD_CORE_JS_START = "loadCoreJsStart";
  public static final String LOAD_CORE_JS_END = "loadCoreJsEnd";

  public static final String INIT_RUNTIME_START = "initRuntimeStart";
  public static final String INIT_RUNTIME_END = "initRuntimeEnd";

  public static final String LOAD_TEMPLATE_START = "loadTemplateStart";
  public static final String LOAD_TEMPLATE_END = "loadTemplateEnd";

  public static final String LOAD_TEMPLATE_STRING_START = "loadTemplateStringStart";
  public static final String LOAD_TEMPLATE_STRING_END = "loadTemplateStringEnd";

  public static final String LOAD_TEMPLATE_MAP_START = "loadTemplateMapStart";
  public static final String LOAD_TEMPLATE_MAP_END = "loadTemplateMapEnd";

  public static final String LOAD_TEMPLATE_BUNDLE_START = "loadTemplateBundleStart";
  public static final String LOAD_TEMPLATE_BUNDLE_END = "loadTemplateBundleEnd";

  public static final String LOAD_SSR_DATA_START = "loadSSRDataStart";
  public static final String LOAD_SSR_DATA_END = "loadSSRDataEnd";

  public static final String REGISTER_LAZY_BUNDLE_START = "registerLazyBundleStart";
  public static final String REGISTER_LAZY_BUNDLE_END = "registerLazyBundleEnd";

  public static final String PRELOAD_LAZY_BUNDLES_START = "preloadLazyBundlesStart";
  public static final String PRELOAD_LAZY_BUNDLES_END = "preloadLazyBundlesEnd";

  public static final String OPEN_TIME = "openTime";
  public static final String CONTAINER_INIT_START = "containerInitStart";
  public static final String CONTAINER_INIT_END = "containerInitEnd";
  public static final String PREPARE_TEMPLATE_START = "prepareTemplateStart";
  public static final String PREPARE_TEMPLATE_END = "prepareTemplateEnd";

  public static class ExtraTimingInfo {
    public long mOpenTime = 0;
    public long mContainerInitStart = 0;
    public long mContainerInitEnd = 0;
    public long mPrepareTemplateStart = 0;
    public long mPrepareTemplateEnd = 0;

    public Map<String, Long> toMap() {
      HashMap<String, Long> map = new HashMap<>();
      map.put(OPEN_TIME, mOpenTime);
      map.put(CONTAINER_INIT_START, mContainerInitStart);
      map.put(CONTAINER_INIT_END, mContainerInitEnd);
      map.put(PREPARE_TEMPLATE_START, mPrepareTemplateStart);
      map.put(PREPARE_TEMPLATE_END, mPrepareTemplateEnd);
      return map;
    }
  }
}
