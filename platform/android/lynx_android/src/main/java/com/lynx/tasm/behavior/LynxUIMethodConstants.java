// Copyright 2023 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
package com.lynx.tasm.behavior;

public class LynxUIMethodConstants {
  public static final int SUCCESS = 0;
  public static final int UNKNOWN = 1;
  public static final int NODE_NOT_FOUND = 2;
  public static final int METHOD_NOT_FOUND = 3;
  public static final int PARAM_INVALID = 4;
  public static final int SELECTOR_NOT_SUPPORTED = 5;
  public static final int NO_UI_FOR_NODE = 6;
  public static final int INVALID_STATE_ERROR = 7;
  public static final int OPERATION_ERROR = 8;
}
