// Copyright 2023 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
package com.lynx.tasm.animation;

import android.support.annotation.IntDef;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public final class AnimationConstant {
  /************************************* transition *****************************************/
  // transition name
  public static final String TRANSITION = "transition";

  public static final String PROP_STR_NONE = "none";
  public static final String PROP_STR_OPACITY = "opacity";
  public static final String PROP_STR_SCALE_X = "scaleX";
  public static final String PROP_STR_SCALE_Y = "scaleY";
  public static final String PROP_STR_SCALE_X_Y = "scaleXY";
  public static final String PROP_STR_WIDTH = "width";
  public static final String PROP_STR_HEIGHT = "height";
  public static final String PROP_STR_LEFT = "left";
  public static final String PROP_STR_TOP = "top";
  public static final String PROP_STR_RIGHT = "right";
  public static final String PROP_STR_BOTTOM = "bottom";
  public static final String PROP_STR_BACKGROUND_COLOR = "background-color";
  public static final String PROP_STR_VISIBILITY = "visibility";
  public static final String PROP_STR_TRANSFORM = "transform";

  /************************************* layout animation *****************************************/
  // layout animation type
  public static final int LAYOUT_ANIMATION_TYPE_CREATE = 0;
  public static final int LAYOUT_ANIMATION_TYPE_UPDATE = 1;
  public static final int LAYOUT_ANIMATION_TYPE_DELETE = 2;

  @IntDef(
      {LAYOUT_ANIMATION_TYPE_CREATE, LAYOUT_ANIMATION_TYPE_UPDATE, LAYOUT_ANIMATION_TYPE_DELETE})
  @Retention(RetentionPolicy.SOURCE)
  public @interface LayoutAnimationType {}

  // layout-animation name
  // TODO(liyanbo): Deprecated name after parsing refactoring
  public static final String C_DURATION = "layout-animation-create-duration";
  public static final String D_DURATION = "layout-animation-delete-duration";
  public static final String U_DURATION = "layout-animation-update-duration";
  public static final String C_DELAY = "layout-animation-create-delay";
  public static final String D_DELAY = "layout-animation-delete-delay";
  public static final String U_DELAY = "layout-animation-update-delay";
  public static final String C_PROPERTY = "layout-animation-create-property";
  public static final String D_PROPERTY = "layout-animation-delete-property";
  public static final String U_PROPERTY = "layout-animation-update-property";
  public static final String C_TIMING_FUNCTION = "layout-animation-create-timing-function";
  public static final String D_TIMING_FUNCTION = "layout-animation-delete-timing-function";
  public static final String U_TIMING_FUNCTION = "layout-animation-update-timing-function";

  /************************************* interceptor *****************************************/
  public static final int INTERCEPTOR_LINEAR = 0;
  public static final int INTERCEPTOR_EASE_IN = 1;
  public static final int INTERCEPTOR_EASE_OUT = 2;
  public static final int INTERCEPTOR_EASE_IN_OUT = 3;
  public static final int INTERCEPTOR_SQUARE_BEZIER = 4;
  public static final int INTERCEPTOR_CUBIC_BEZIER = 5;
  public static final int INTERCEPTOR_STEPS = 6;

  /************************************* property *****************************************/
  // FIXME(wangyifei.20010605): Using bit to represent a type may not be enough in
  // the future, So instead of using the bit shift operation to represent the
  // type, The new type uses the default values generated by the enumerated class.
  // The value of those properties must be consistent with C++ AnimationPropertyType.
  public static final int PROP_NONE = 0;
  public static final int PROP_OPACITY = 1 << 0;
  public static final int PROP_SCALE_X = 1 << 1;
  public static final int PROP_SCALE_Y = 1 << 2;
  public static final int PROP_SCALE_X_Y = 1 << 3;
  public static final int PROP_WIDTH = 1 << 4;
  public static final int PROP_HEIGHT = 1 << 5;
  public static final int PROP_BACKGROUND_COLOR = 1 << 6;
  public static final int PROP_VISIBILITY = 1 << 7;
  public static final int PROP_LEFT = 1 << 8;
  public static final int PROP_TOP = 1 << 9;
  public static final int PROP_RIGHT = 1 << 10;
  public static final int PROP_BOTTOM = 1 << 11;
  public static final int PROP_TRANSFORM = 1 << 12;
  public static final int PROP_COLOR = 1 << 13;
  public static final int PROP_MAX_WIDTH = 1 << 14;
  public static final int PROP_MIN_WIDTH = 1 << 15;
  public static final int PROP_MAX_HEIGHT = 1 << 16;
  public static final int PROP_MIN_HEIGHT = 1 << 17;
  public static final int TRAN_PROP_ALL = 1 << 18;
  public static final int TRAN_PROP_LEGACY_ALL_1 = PROP_OPACITY | PROP_WIDTH | PROP_HEIGHT
      | PROP_BACKGROUND_COLOR | PROP_VISIBILITY | PROP_LEFT | PROP_TOP | PROP_RIGHT | PROP_BOTTOM
      | PROP_TRANSFORM;
  public static final int TRAN_PROP_LEGACY_ALL_2 = TRAN_PROP_LEGACY_ALL_1 | PROP_COLOR;
  public static final int TRAN_PROP_LEGACY_ALL_3 =
      TRAN_PROP_LEGACY_ALL_2 | PROP_MAX_WIDTH | PROP_MIN_WIDTH | PROP_MAX_HEIGHT | PROP_MIN_HEIGHT;
  /************************************* END *****************************************/

  public static final int[] ALL_PLATFORM_TRANSITION_PROPS_ARR =
      new int[] {PROP_OPACITY, PROP_WIDTH, PROP_HEIGHT, PROP_BACKGROUND_COLOR, PROP_VISIBILITY,
          PROP_LEFT, PROP_TOP, PROP_RIGHT, PROP_BOTTOM, PROP_TRANSFORM};
  public static final int PROP_OF_LAYOUT =
      PROP_WIDTH | PROP_HEIGHT | PROP_LEFT | PROP_RIGHT | PROP_TOP | PROP_BOTTOM;
}
