// Copyright 2025 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

package com.lynx.tasm.core.resource;

/**
 * A type class used to indicate the correct loading of resources by LynxResourceLoader, assigned
 * with LynxResourceType in lynx_resource_loader.h
 * TODO(zhoupeng.z): auto-generated by IDL
 */
class LynxResourceType {
  /**
   * Load lazy bundle on BTS
   */
  static final int LYNX_RESOURCE_TYPE_JS_LAZY_BUNDLE = 7;

  /**
   * Load external js source on BTS
   */
  static final int LYNX_RESOURCE_TYPE_EXTERNAL_JS = 9;

  /**
   * Load load bundle on MTS
   */
  static final int LYNX_RESOURCE_TYPE_TEMPLATE_LAZY_BUNDLE = 10;

  /**
   * Load asset js source on BTS
   */
  static final int LYNX_RESOURCE_TYPE_ASSETS = 11;

  /**
   * Load frame bundle on MTS
   */
  static final int LYNX_RESOURCE_TYPE_FRAME = 15;
}
