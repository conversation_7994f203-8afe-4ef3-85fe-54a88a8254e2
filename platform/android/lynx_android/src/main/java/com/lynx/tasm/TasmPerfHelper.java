package com.lynx.tasm;

import android.util.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TasmPerfHelper {
  public static final String createNode = "create_node";
  public static final String createNodeUI = "create_node_ui";
  public static final String insertNode = "insert_node";
  public static final String removeNode = "remove_node";
  public static final String destroyNode = "destroy_node";
  public static final String loadTemplate = "load_template";
  public static final String measure = "measure";
  public static final String layout = "layout";
  public static final String draw = "draw";
  public static final String updateExtra = "update_extra";
  public static final String  updateLayoutPatching = "update_layout_patching";
  public static final String allUI = "all_ui";
  
  public static Map<String, Long> startTimeMap = new HashMap<>();

  public static Map<String, Long> costTimeMap = new HashMap<>();
  
  public static Map<String, Integer> countMap = new HashMap<>();

  public static List<Integer> createNodeMap = new ArrayList<>();
  
  static {
//    reset();
  }

//  public static void reset() {
////    costTimeMap.put(createNode, 0L);
//    costTimeMap.put(insertNode, 0L);
//    costTimeMap.put(loadTemplate, 0L);
//    costTimeMap.put(measure, 0L);
//    costTimeMap.put(layout, 0L);
//    costTimeMap.put(draw, 0L);
//    costTimeMap.put(updateExtra, 0L);
//    costTimeMap.put(updateLayoutPatching, 0L);
//    costTimeMap.put(allUI, 0L);
//    
////    countMap.put(createNode, 0);
//    countMap.put(insertNode, 0);
//    countMap.put(loadTemplate, 0);
//    countMap.put(measure, 0);
//    countMap.put(layout, 0);
//    countMap.put(draw, 0);
//    countMap.put(updateExtra, 0);
//    countMap.put(updateLayoutPatching, 0);
//    countMap.put(allUI, 0);
//  }

//  public static void begin(String key) {
//    startTimeMap.put(key, System.nanoTime());
//  }
//
//  public static void end(String key) {
//    Long startTime = startTimeMap.get(key);
//    if (startTime == null) {
//      Log.e("MSCLOG", "error " + key);
//      return;
//    }
//    long costTime = System.nanoTime() - startTime;
//    if (loadTemplate.equals(key)) {
//      Log.e("MSCLOG", "loadTemplate " + " cost time: " + costTime / 1000000f);
//    }
//    costTimeMap.put(key, costTimeMap.get(key) + costTime);
//    countMap.put(key, countMap.get(key) + 1);
//  }
//  
  // 有的是多线程的，用begin/end不合适
//  public static void add(String key, long time){
//    
//    
//  }
//
//  public static void pageEnter() {
//    reset();
//  }
//
//  public static void pageExit() {
//    for (Map.Entry<String, Long> entry : costTimeMap.entrySet()) {
//      System.out.println(entry.getKey() + " cost time: " + (entry.getValue() / 1000000f));
//      Log.e("MSCLOG", "pageExit " + entry.getKey() + " count : " + countMap.get(entry.getKey()) + " cost time: " + entry.getValue() / 1000000f);
//    }
//  }

}
