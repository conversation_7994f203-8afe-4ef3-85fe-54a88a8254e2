# Copyright 2024 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.
import subprocess

from core.builder.builder_manager import BuilderManager
from core.container.container import Container
from core.coverage.coverage import Coverage
from core.coverage.coverage_factory import CoverageFactory
from core.target.observer import LogObserver, OwnersObserver
from core.target.target_factory import TargetFactory
from core.utils.emu_env_setup import EmulatorEnv
from core.utils.log import Log
from core.base.result import Ok, Err
from core.base.constants import Constants


class AndroidUTContainer(Container):
    def __init__(self, builder, coverage):
        super().__init__()
        self.targets = []
        self.test_type = "android-ut"
        self.builder_manager: BuilderManager = BuilderManager(builder)
        self.coverage_init_params = coverage
        self.coverage: Coverage = None
        self.emulator = EmulatorEnv()
        self.observers = [LogObserver(), OwnersObserver()]
        self.device_log = "device.log"
        self.log_process = None
        self.use_real_device = False
        self.clean = True

    def before_test(self, targets, filter: str):
        result = self.emulator.prepare_android_emulator(
            use_real_device=self.use_real_device
        )

        if result.is_err():
            return result

        coverage = CoverageFactory(self.coverage_init_params)
        if coverage.is_err():
            return coverage

        self.coverage = coverage.get_value()

        def skip():
            return not self.clean

        result = self.builder_manager.pre_action(skip=skip)
        if result.is_err():
            return result
        for t in targets.keys():
            if filter != "all" and t != filter:
                continue
            result = TargetFactory(self.test_type, targets[t], t)
            if result.is_err():
                return result
            target = result.get_value()
            if not target.enable:
                continue
            self.targets.append(target)

        for target in self.targets:
            result = self.builder_manager.build(target)
            if result.is_err():
                return result
            target.insert_global_info("device_name", self.emulator.device)

        return Ok()

    def after_test(self):
        try:
            if self.emulator.is_root_rule:
                self.coverage.gen_report(self.targets)
            else:
                Log.warning(
                    f"Device has no root permission, skipping coverage generation"
                )
        except Exception as e:
            pass
        finally:
            self.emulator.close()
            self.log_process.kill()
            return Ok()

    def test(self):
        log_file = open(self.device_log, "w")
        self.log_process = subprocess.Popen(
            ["adb", "logcat", "-v", "time"], stdout=log_file, stderr=subprocess.STDOUT
        )
        for target in self.targets:
            result = target.run()
            if result.is_err():
                return result
            target.wait()
            if target.has_error():
                for observer in self.observers:
                    observer.update(target)
                return Err(Constants.TARGET_RUN_ERR, f"{target.name} has error!")
            else:
                Log.success(f"{target.name} success!")
        return Ok()
