{"name": "flex-grow", "id": 50, "type": "number", "default_value": "0", "version": "1.0", "author": "<PERSON>ng<PERSON><PERSON><PERSON>", "consumption_status": "layout-only", "desc": "", "compat_data": {"flex-grow": {"__compat": {"lynx_path": "api/css/properties/flex-grow", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/flex-grow", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}}}}