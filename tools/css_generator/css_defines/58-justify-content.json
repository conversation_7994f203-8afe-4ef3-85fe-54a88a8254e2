{"name": "justify-content", "id": 58, "type": "enum", "default_value": "stretch", "version": "1.0", "author": "<PERSON>ng<PERSON><PERSON><PERSON>", "desc": "", "consumption_status": "layout-only", "values": [{"value": "flex-start", "version": "1.0", "desc": ""}, {"value": "center", "version": "1.0", "desc": ""}, {"value": "flex-end", "version": "1.0", "desc": ""}, {"value": "space-between", "version": "1.0", "desc": ""}, {"value": "space-around", "version": "1.0", "desc": ""}, {"value": "space-evenly", "version": "1.0", "desc": ""}, {"value": "stretch", "version": "2.1", "desc": ""}, {"value": "start", "align-type": "flex-start", "version": "2.1", "desc": ""}, {"value": "end", "align-type": "flex-end", "version": "2.1", "desc": ""}], "compat_data": {"justify-content": {"__compat": {"lynx_path": "api/css/properties/justify-content", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/justify-content", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}, "supported_in_flex_layout": {"__compat": {"description": "Supported in Flex Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}, "nested_value_1": {"__compat": {"description": "<code>flex-start</code>, <code>flex-end</code>, <code>center</code>, <code>space-between</code>, <code>space-around</code>, <code>space-evenly</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}}, "nested_value_2": {"__compat": {"description": "<code>start</code> and <code>end</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.1"}, "ios": {"notes": "", "version_added": "2.1"}, "clay_android": {"version_added": "2.1"}, "clay_macos": {"version_added": "2.1"}, "clay_windows": {"version_added": "2.1"}}}}}, "supported_in_linear_layout": {"__compat": {"description": "Supported in Linear Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "targetSdkVersion 2.2"}, "ios": {"notes": "", "version_added": "targetSdkVersion 2.2"}, "clay_android": {"version_added": "targetSdkVersion 2.2"}, "clay_macos": {"version_added": "targetSdkVersion 2.2"}, "clay_windows": {"version_added": "targetSdkVersion 2.2"}}}, "nested_value_1": {"__compat": {"description": "<code>flex-start</code>, <code>flex-end</code>, <code>center</code>, <code>space-between</code>, <code>start</code>, <code>end</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "targetSdkVersion 2.2"}, "ios": {"notes": "", "version_added": "targetSdkVersion 2.2"}, "clay_android": {"version_added": "targetSdkVersion 2.2"}, "clay_macos": {"version_added": "targetSdkVersion 2.2"}, "clay_windows": {"version_added": "targetSdkVersion 2.2"}}}}}, "supported_in_grid_layout": {"__compat": {"description": "Supported in Grid Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.1"}, "ios": {"notes": "", "version_added": "2.1"}, "clay_android": {"version_added": "2.1"}, "clay_macos": {"version_added": "2.1"}, "clay_windows": {"version_added": "2.1"}}}, "nested_value_1": {"__compat": {"description": "<code>flex-start</code>, <code>flex-end</code>, <code>center</code>, <code>space-between</code>, <code>space-around</code>, <code>space-evenly</code>, <code>start</code>, <code>end</code>, <code>stretch</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.1"}, "ios": {"notes": "", "version_added": "2.1"}, "clay_android": {"version_added": "2.1"}, "clay_macos": {"version_added": "2.1"}, "clay_windows": {"version_added": "2.1"}}}}}, "unsupported_value_1": {"__compat": {"description": "<code>normal</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": false}, "ios": {"notes": "", "version_added": false}, "clay_android": {"version_added": false}, "clay_macos": {"version_added": false}, "clay_windows": {"version_added": false}, "web_lynx": {"version_added": false}}}}, "unsupported_value_2": {"__compat": {"description": "<code>left</code> and <code>right</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": false}, "ios": {"notes": "", "version_added": false}, "clay_android": {"version_added": false}, "clay_macos": {"version_added": false}, "clay_windows": {"version_added": false}, "web_lynx": {"version_added": false}}}}, "unsupported_value_3": {"__compat": {"description": "<code>baseline</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": false}, "ios": {"notes": "", "version_added": false}, "clay_android": {"version_added": false}, "clay_macos": {"version_added": false}, "clay_windows": {"version_added": false}, "web_lynx": {"version_added": false}}}}}}}