{"name": "order", "id": 75, "type": "number", "default_value": "0", "version": "1.0", "author": "<PERSON>ng<PERSON><PERSON><PERSON>", "consumption_status": "layout-only", "desc": "", "compat_data": {"order": {"__compat": {"description": "The order CSS property sets the order to lay out an item.", "lynx_path": "docs/zh/api/css/properties/order", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/order", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}}}}