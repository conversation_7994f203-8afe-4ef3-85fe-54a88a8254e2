{"name": "flex-flow", "id": 146, "type": "complex", "author": "yuan<PERSON><PERSON>", "default_value": "row nowrap", "version": "1.0", "consumption_status": "skip", "desc": "flex and wrap", "compat_data": {"flex-flow": {"__compat": {"lynx_path": "api/css/properties/flex-flow", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/flex-flow", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}}}}