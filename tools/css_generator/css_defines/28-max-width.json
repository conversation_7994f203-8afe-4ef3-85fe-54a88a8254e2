{"name": "max-width", "id": 28, "type": "length", "default_value": "auto", "version": "1.0", "author": "<PERSON>ng<PERSON><PERSON><PERSON>", "consumption_status": "layout-only", "desc": "", "compat_data": {"max-width": {"__compat": {"description": "sets the maximum width of an element.", "lynx_path": "docs/zh/api/css/properties/max-width", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/max-width", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}}}}