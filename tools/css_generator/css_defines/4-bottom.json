{"name": "bottom", "id": 4, "type": "length", "default_value": "auto", "version": "1.0", "author": "<PERSON>ng<PERSON><PERSON><PERSON>", "consumption_status": "layout-only", "desc": "bottom offset", "compat_data": {"bottom": {"__compat": {"lynx_path": "api/css/properties/bottom", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/bottom", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"version_added": "1.0"}, "ios": {"version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}}}}