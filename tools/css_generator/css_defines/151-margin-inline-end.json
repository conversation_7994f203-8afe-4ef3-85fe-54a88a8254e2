{"name": "margin-inline-end", "id": 151, "type": "length", "default_value": "0px", "version": "2.0", "author": "liting.src", "consumption_status": "skip", "desc": "", "compat_data": {"margin-inline-end": {"__compat": {"lynx_path": "api/css/properties/margin-inline-end", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/margin-inline-end", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"version_added": "1.0"}, "ios": {"version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}, "supported_in_flex_layout": {"__compat": {"description": "Supported in Flex Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}, "nested_value_1": {"__compat": {"description": "<code>auto</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}}}, "supported_in_linear_layout": {"__compat": {"description": "Supported in Linear Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}, "nested_value_1": {"__compat": {"description": "<code>auto</code> in cross-axis", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}}, "nested_value_2": {"__compat": {"description": "<code>auto</code> in main-axis", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": false}, "ios": {"notes": "", "version_added": false}, "clay_android": {"version_added": false}, "clay_macos": {"version_added": false}, "clay_windows": {"version_added": false}}}}}, "supported_in_grid_layout": {"__compat": {"description": "Supported in Grid Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.1"}, "ios": {"notes": "", "version_added": "2.1"}, "clay_android": {"version_added": "2.1"}, "clay_macos": {"version_added": "2.1"}, "clay_windows": {"version_added": "2.1"}}}, "nested_value_1": {"__compat": {"description": "<code>auto</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.1"}, "ios": {"notes": "", "version_added": "2.1"}, "clay_android": {"version_added": "2.1"}, "clay_macos": {"version_added": "2.1"}, "clay_windows": {"version_added": "2.1"}}}}}}}}