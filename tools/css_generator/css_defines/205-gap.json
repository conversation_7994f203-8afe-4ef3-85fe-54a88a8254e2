{"name": "gap", "id": 205, "type": "complex", "default_value": "0px", "version": "2.14", "author": "yuan<PERSON><PERSON>", "consumption_status": "layout-only", "desc": "", "compat_data": {"gap": {"__compat": {"lynx_path": "api/css/properties/gap", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/gap", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.14"}, "ios": {"notes": "", "version_added": "2.14"}, "clay_android": {"version_added": "2.14"}, "clay_macos": {"version_added": "2.14"}, "clay_windows": {"version_added": "2.14"}}}, "Flex": {"__compat": {"description": "Supported in Flex Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.14"}, "ios": {"notes": "", "version_added": "2.14"}, "clay_android": {"version_added": "2.14"}, "clay_macos": {"version_added": "2.14"}, "clay_windows": {"version_added": "2.14"}}}}, "Grid": {"__compat": {"description": "Supported in Grid Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.14"}, "ios": {"notes": "", "version_added": "2.14"}, "clay_android": {"version_added": "2.14"}, "clay_macos": {"version_added": "2.14"}, "clay_windows": {"version_added": "2.14"}}}}}}}