{"name": "height", "id": 26, "type": "length", "default_value": "auto", "version": "1.0", "author": "<PERSON>ng<PERSON><PERSON><PERSON>", "consumption_status": "layout-only", "desc": "", "compat_data": {"height": {"__compat": {"lynx_path": "api/css/properties/height", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/height", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"version_added": "1.0"}, "ios": {"version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}, "max-content": {"__compat": {"status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "targetSdkVersion 1.6"}, "ios": {"notes": "", "version_added": "targetSdkVersion 1.6"}, "clay_android": {"version_added": "targetSdkVersion 1.6"}, "clay_macos": {"version_added": "targetSdkVersion 1.6"}, "clay_windows": {"version_added": "targetSdkVersion 1.6"}}}}, "fit-content": {"__compat": {"status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "targetSdkVersion 2.0"}, "ios": {"notes": "", "version_added": "targetSdkVersion 2.0"}, "clay_android": {"version_added": "targetSdkVersion 2.0"}, "clay_macos": {"version_added": "targetSdkVersion 2.0"}, "clay_windows": {"version_added": "targetSdkVersion 2.0"}}}}, "min-content": {"__compat": {"status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": false}, "ios": {"notes": "", "version_added": false}, "clay_android": {"version_added": false}, "clay_macos": {"version_added": false}, "clay_windows": {"version_added": false}}}}}}}