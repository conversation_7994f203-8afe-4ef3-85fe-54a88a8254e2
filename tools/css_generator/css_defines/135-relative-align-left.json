{"name": "relative-align-left", "id": 135, "type": "complex", "default_value": "-1", "version": "1.0", "author": "<PERSON><PERSON><PERSON>", "consumption_status": "layout-only", "desc": "", "compat_data": {"relative-align-left": {"__compat": {"description": "Specifies that the current element is aligned with the left edge of the parent or sibling element corresponding to id.", "lynx_path": "docs/zh/api/css/properties/relative-align-left", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.4"}, "ios": {"notes": "", "version_added": "1.4"}, "clay_android": {"version_added": "1.4"}, "clay_macos": {"version_added": "1.4"}, "clay_windows": {"version_added": "1.4"}}}}}}