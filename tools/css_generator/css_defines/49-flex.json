{"name": "flex", "id": 49, "type": "complex", "default_value": "0", "version": "1.0", "author": "<PERSON>ng<PERSON><PERSON><PERSON>", "consumption_status": "layout-only", "desc": "", "compat_data": {"flex": {"__compat": {"lynx_path": "api/css/properties/flex", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/flex", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "1.0"}, "ios": {"notes": "", "version_added": "1.0"}, "clay_android": {"version_added": "1.0"}, "clay_macos": {"version_added": "1.0"}, "clay_windows": {"version_added": "1.0"}}}, "none": {"__compat": {"status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "targetSdkVersion 2.12"}, "ios": {"notes": "", "version_added": "targetSdkVersion 2.12"}, "clay_android": {"version_added": "targetSdkVersion 2.12"}, "clay_macos": {"version_added": "targetSdkVersion 2.12"}, "clay_windows": {"version_added": "targetSdkVersion 2.12"}}}}}}}