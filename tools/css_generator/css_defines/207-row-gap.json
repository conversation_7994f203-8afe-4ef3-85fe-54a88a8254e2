{"name": "row-gap", "id": 207, "type": "length", "default_value": "0px", "version": "2.14", "author": "yuan<PERSON><PERSON>", "consumption_status": "layout-only", "desc": "", "compat_data": {"row-gap": {"__compat": {"lynx_path": "api/css/properties/row-gap", "description": "<code>row-gap</code>", "mdn_url": "https://developer.mozilla.org/zh-CN/docs/Web/CSS/row-gap", "spec_url": [], "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.14"}, "ios": {"notes": "", "version_added": "2.14"}, "clay_android": {"version_added": "2.14"}, "clay_macos": {"version_added": "2.14"}, "clay_windows": {"version_added": "2.14"}}}, "grid-row-gap": {"__compat": {"description": "<code>grid-row-gap</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.1"}, "ios": {"notes": "", "version_added": "2.1"}, "clay_android": {"version_added": "2.1"}, "clay_macos": {"version_added": "2.1"}, "clay_windows": {"version_added": "2.1"}}}}, "supported_in_flex_layout": {"__compat": {"description": "Supported in Flex Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.14"}, "ios": {"notes": "", "version_added": "2.14"}, "clay_android": {"version_added": "2.14"}, "clay_macos": {"version_added": "2.14"}, "clay_windows": {"version_added": "2.14"}}}, "nested_value_1": {"__compat": {"description": "<code>row-gap</code> and <code>grid-row-gap</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.14"}, "ios": {"notes": "", "version_added": "2.14"}, "clay_android": {"version_added": "2.14"}, "clay_macos": {"version_added": "2.14"}, "clay_windows": {"version_added": "2.14"}}}}}, "supported_in_grid_layout": {"__compat": {"description": "Supported in Grid Layout", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.1"}, "ios": {"notes": "", "version_added": "2.1"}, "clay_android": {"version_added": "2.1"}, "clay_macos": {"version_added": "2.1"}, "clay_windows": {"version_added": "2.1"}}}, "nested_value_1": {"__compat": {"description": "<code>grid-row-gap</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.1"}, "ios": {"notes": "", "version_added": "2.1"}, "clay_android": {"version_added": "2.1"}, "clay_macos": {"version_added": "2.1"}, "clay_windows": {"version_added": "2.1"}}}}, "nested_value_2": {"__compat": {"description": "<code>row-gap</code>", "status": {"deprecated": false, "experimental": false}, "support": {"android": {"notes": "", "version_added": "2.14"}, "ios": {"notes": "", "version_added": "2.14"}, "clay_android": {"version_added": "2.14"}, "clay_macos": {"version_added": "2.14"}, "clay_windows": {"version_added": "2.14"}}}}}}}}