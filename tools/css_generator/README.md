# How to add a property for styling module

## First, make sure you're actually adding a property

Properties are features of an element that you can change the value of, to make it styled differently. (e.g. color,
font-size). If the feature is not related to element's style, or it is strongly coupled with a specific element type (
e.g. "initial-scroll-offset" on <scroll-view>), it is recommended to add it as an attribute. Most times, the style
property should have a prototype in the W3C CSS specification.

If the implementation of the property is non-standardized, or it is under experimental, it should be a prefixed
property.

**-x-** is the vendor prefix used by Lynx to distinguish CSS properties that are specific to Lynx and not part of the
Web standard. They provide Web developers with some layout and styling capabilities specific to Lynx, such as:

```css
.linear {
    /* similar to when Grid layout was -ms-grid, -webkit-grid: */
    display: -x-linear;
    display: linear; /* should also work */
    -x-linear-orientation: horizontal;
}

```

## Start adding property

Once you have decided to add a new property, you can follow the steps below:

1. Add a definition file under the [css_defines](./css_defines) directory. The file name should begin with an incremental
   number
   indicating the ID of the property. We use the ID at runtime to map the property's parser, getter and setter functions
   to avoid
   string comparison. So the ID should be unique, and should not be modified after it is added. The consistency of the
   ID will be checked according to [index file](./property_index.json). The new ID and property name will be automatically
   added to the index file by [css_parser_generator.py](./css_parser_generator.py).


2. Execution python tools/css_generator/css_parser_generator.py


3. Implement the codes in generated files.<br/>
   There are couples of functions can not be automatically implemented by the script. According to the complexity of the
   property's value, you may need to implement the following functions:
    1. [Parser](../../core/renderer/css/parser/background_box_handler.h): The parser function is used to parse the value of the property.
    2. [ComputedCSSStyle](../../core/renderer/css/computed_css_style.cc): The setter and getter functions should be
       manually implemented according to the output of parser. If the property will be consumed by the platform UI
       layer, you have to add it to the macro `FOREACH_PLATFORM_PROPERTY` in the header file.


### Practice

```json

{
    "name": "test",
    "id": 213,
    "type": "complex",
    "default_value": "auto",
    "version": "1.0",
    "author": "wangerpao",
    "consumption_status": "layout-only",
    "desc": "left offset",
    "keywords":["foo","bar","foobar"],
    "values": [
        {
            "value": "test-value",
            "version": "1.0"
        }
    ],
    "links": [
        {
            "url": "<reference docs>",
            "desc": "description of the reference"
        },
        {
            "url":"123"
        }
    ],
    "note": [
        {
            "literal": "this is a note",
            "level": "tip"
        },
        {
            "literal":"This is a warning for user of this property.",
            "level": "warning"
        }
    ],
    "__compat": { 
      "description": "<Description of this compat data entry>",
      "lynx_path": "<path to api reference in lynx website> docs/zh/api/css/properties/left)",
      "mdn_url": "<path to mdn definition> https://developer.mozilla.org/zh-CN/docs/Web/CSS/left", "spec_url": ["<path to w3c specification file>"],
      "status": {
        "deprecated": false,
        "experimental": false
      },
      "support": {
        "android": {
          "version_added": "1.0"
        },
        "ios": {
          "version_added": "1.0"
        }
     }
    }
}
```

Step 1: Make a file named "999-test.json" and copy data above into it.

Step 2: Run "css_parser_generator.py".

The "css_property_id.h" file will be autogenerated under "core/renderer/css". And you will find you newly added property "test" is append to the end of the macro "FOREACH_ALL_PROPERTY", as well as the property enum class "CSSPropertyID".

Step 3: Implement the parser of the value. If the type is one of color, length, time, enum, border-width, border-style, bool, timing-function or animation-property, the parser will be auto-generated. Otherwise you should mannualy add it to
"core/renderer/css/parser" directory.

Add two new file, "test_handler.h" and "test_handler.cc" under the directory. 

```c++

#include "core/renderer/css/parser/handler_defines.h"

namespace lynx {
namespace tasm {
namespace TestHandler {

HANDLER_REGISTER_DECLARE();

}  // namespace TestHandler
}  // namespace tasm

}  // namespace lynx


```

You should register you parsing functions into the 'array' to your proprety id, and implement the parser functions, which converts the input string value into a CSSValue object and put it into the 'output' map.
It is recommended that implement the parser based on CSSStringParser, it already has some basic tokenizers and lexical checks.


```c++
#include "core/renderer/css/parser/test_handler.h"

#include <string>
#include <utility>

#include "base/include/debug/lynx_assert.h"
#include "core/renderer/css/parser/css_string_parser.h"
#include "core/renderer/css/unit_handler.h"
#include "core/renderer/tasm/config.h"

namespace lynx {
namespace tasm {
namespace TestHandler {

HANDLER_IMPL() {
  CSS_HANDLER_FAIL_IF_NOT(input.IsString(), configs.enable_css_strict_mode,
                          TYPE_MUST_BE, CSSProperty::GetPropertyNameCStr(key),
                          STRING_TYPE)

  CSSStringParser parser = CSSStringParser::FromLepusString(input, configs);
  parser.SetIsLegacyParser(configs.enable_legacy_parser);
  output[kPropertyIDTest] = parser.ParseTest();
  return true;
}

HANDLER_REGISTER_IMPL() {
  array[kPropertyIDTest] = &Handle;
}

}  // namespace BackgroundSizeHandler
}  // namespace tasm
}

```

Step 4. Register parser

Add your customized handler by register it at "core/renderer/css/parser/unit_handler.cc".

```c++

UnitHander::UnitHandler() {

  TestHandler::Register(interceptors_);

}

```


Step 5. Implement setter & getter for ComputedCSSStyle.

After you conver the raw string into a CSSValue, in a ComputedCSSValue, it should be a data struct based on primitive types. And you should implement the value conversion from CSSValue, calculate it
if your value is context related (e.g. length value with sp unit is related to root element's font-size.). 


If your proeprty will be consumed by platform layer, add it to macro 'FOREACH_PLATFORM_PROPERTY'. Then you should implement your function in prop_bundle_style_writter, to enable the runtime can put your computed value into prop bundle, and send it to platform layer. 

```C++
#define FOREACH_PLATFORM_PROPERTY(V)     \
V(Test)

bool ComputedCSSValue::SetTest(const tasm::CSSValue& value, bool reset);

// In prop_bundle_style_writter
  static void TestWriterFunc(PropBundle* bundle, CSSPropertyID id,
                                starlight::ComputedCSSStyle* style);

  static constexpr std::array<WriterFunc, kPropertyEnd> kWriter = [] {
    std::array<WriterFunc, kPropertyEnd> writer = {nullptr};
    for (CSSPropertyID id : kPlatformIDs) {
      writer[id] = &DefaultWriterFunc;
    }
    writer[kPropertyIDTest] = &TestWriterFunc;
    return writer;
  }();
};

```





## JSON field reference

