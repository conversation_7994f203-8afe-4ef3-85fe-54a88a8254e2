{"$schema": "http://json-schema.org/schema", "definitions": {"support_block": {"type": "object", "propertyNames": {"enum": ["ios", "android", "clay_android", "clay_macos", "clay_windows", "web_lynx"]}, "additionalProperties": {"$ref": "#/definitions/support_statement"}, "tsType": "Partial<Record<PlatformName, SupportStatement>>"}, "status_block": {"type": "object", "properties": {"experimental": {"type": "boolean", "description": "A boolean value that indicates the general stability of this feature. This value will be true if the feature was implemented in Lynx recently. This value will be false if the feature had been implemented over two years ago in Lynx."}, "deprecated": {"type": "boolean", "description": "A boolean value that indicates whether the feature is no longer recommended. It might be removed in the future or might only be kept for compatibility purposes. Avoid using this functionality."}}, "required": ["experimental", "deprecated"], "additionalProperties": false}, "spec_url_value": {"type": "string", "format": "uri", "pattern": "(^https://[^#]+#.+)|(^https://github.com/WebAssembly/.+)|(^https://registry.khronos.org/webgl/extensions/[^/]+/)"}, "compat_statement": {"type": "object", "properties": {"description": {"type": "string", "description": "A string containing a human-readable description of the feature."}, "mdn_url": {"type": "string", "format": "uri", "description": "An optional URL to the MDN documentation for the feature, used to link from the Lynx doc website."}, "lynx_path": {"type": "string", "pattern": "^[a-zA-Z0-9_\\-/]+$", "description": "A URI that points to the lynx docs page documenting the feature. This URI is left in relative form to allow for converting into both internal and external URIs in the future."}, "spec_url": {"anyOf": [{"$ref": "#/definitions/spec_url_value"}, {"type": "array", "minItems": 0, "items": {"$ref": "#/definitions/spec_url_value"}}], "description": "An optional URL or array of URLs, each of which is for a specific part of a specification in which this feature is defined. Each URL must contain a fragment identifier.", "tsType": "string | string[]"}, "tags": {"description": "An optional array of strings allowing to assign tags to the feature.", "type": "array", "minItems": 1, "tsType": "string[]"}, "source_file": {"type": "string", "description": "The path to the file that defines this feature in lynx-compat-data, relative to the repository root. Useful for guiding potential contributors towards the correct file to edit. This is automatically generated at build time and should never manually be specified."}, "support": {"$ref": "#/definitions/support_block", "description": "The data for the support of each platform, containing a `support_statement` object for each platform identifier with information about versions, prefixes, or alternate names, as well as notes."}, "status": {"$ref": "#/definitions/status_block", "description": "An object containing information about the stability of the feature."}}, "required": ["support"], "additionalProperties": false}, "identifier": {"type": "object", "properties": {"__compat": {"type": "object", "$ref": "#/definitions/compat_statement", "required": ["status"], "description": "A feature is described by an identifier containing the `__compat` property.\n\nIn other words, identifiers without `__compat` aren't necessarily features, but help to nest the features properly.\n\nWhen an identifier has a `__compat` block, it represents its basic support, indicating that a minimal implementation of a functionality is included.\n\nWhat it represents exactly depends of the evolution of the feature over time, both in terms of specifications and of Lynx support.", "tsType": "CompatStatement"}}, "patternProperties": {"^(?!__compat)[a-zA-Z_0-9-$@]*$": {"$ref": "#/definitions/identifier"}}, "additionalProperties": false, "errorMessage": {"additionalProperties": "Feature names can only contain alphanumerical characters or the following symbols: _-$@"}, "tsType": "{[key: string]: Identifier} & {__compat?: CompatStatement};"}, "lynx_extensions_identifier": {"type": "object", "properties": {"__compat": {"$ref": "#/definitions/compat_statement"}}, "patternProperties": {"^(?!__compat)[a-zA-Z_0-9-$@]*$": {"$ref": "#/definitions/lynx_extensions_identifier", "tsType": "Identifier"}}, "additionalProperties": false, "errorMessage": {"additionalProperties": "Feature names can only contain alphanumerical characters or the following symbols: _-$@"}, "tsType": "{[key: string]: Identifier} & {__compat?: CompatStatement}"}, "support_statement": {"anyOf": [{"$ref": "#/definitions/simple_support_statement"}, {"type": "array", "minItems": 2, "items": {"$ref": "#/definitions/simple_support_statement"}}], "tsType": "SimpleSupportStatement | SimpleSupportStatement[]"}, "simple_support_statement": {"type": "object", "properties": {"version_added": {"description": "A string (indicating which Lynx version added this feature), the value true (indicating support added in an unknown version), the value false (indicating the feature is not supported), or the value null (indicating support is unknown).", "anyOf": [{"type": "string", "pattern": "^(≤?(\\d+)(\\.\\d+)*|preview)$"}, {"type": "string", "pattern": "^targetSdkVersion \\d+(\\.\\d+)*$"}, {"type": "boolean", "nullable": true}], "tsType": "VersionValue"}, "version_removed": {"description": "A string, indicating which Lynx version removed this feature, or the value true, indicating that the feature was removed in an unknown version.", "anyOf": [{"type": "string", "pattern": "^(≤?(\\d+)(\\.\\d+)*|preview)$"}, {"const": true}], "tsType": "VersionValue"}, "version_last": {"description": "A string, indicating the last Lynx version that supported this feature, or the value true, indicating that the feature was removed in an unknown version. This is automatically generated.", "anyOf": [{"type": "string", "pattern": "^(≤?(\\d+)(\\.\\d+)*|preview)$"}, {"const": true}], "tsType": "VersionValue"}, "prefix": {"type": "string", "description": "A prefix to add to the sub-feature name (defaults to empty string). If applicable, leading and trailing '-' must be included."}, "alternative_name": {"type": "string", "description": "An alternative name for the feature, for cases where a feature is implemented under an entirely different name and not just prefixed."}, "flags": {"type": "array", "description": "An optional array of objects describing flags that must be configured for Lynx to support this feature.", "minItems": 1, "items": {"$ref": "#/definitions/flag_statement"}, "tsType": "FlagStatement[]"}, "impl_url": {"anyOf": [{"$ref": "#/definitions/impl_url_value"}, {"type": "array", "minItems": 2, "items": {"$ref": "#/definitions/impl_url_value"}}], "description": "An optional changeset/commit URL for the revision which implemented the feature in the Lynx source code, or the URL to the bug tracking the implementation.", "tsType": "string | string[]", "errorMessage": {"pattern": "impl_url must be a link to a Lynx commit or bug URL. URLs must be in shortened form. Note: `npm run fix` may resolve these issues."}}, "partial_implementation": {"const": true, "description": "A boolean value indicating whether or not the implementation of the sub-feature deviates from the Lynx specification in a way that may cause compatibility problems. It defaults to false (no interoperability problems expected). If set to true, it is recommended that you add a note explaining how it diverges from the standard (such as that it implements an old version of the standard, for example)."}, "notes": {"description": "A string or array of strings containing additional information.", "anyOf": [{"type": "string"}, {"type": "array", "minItems": 2, "items": {"type": "string"}}], "tsType": "string | string[]"}}, "required": ["version_added"], "dependencies": {"partial_implementation": {"if": {"properties": {"partial_implementation": {"const": true}}}, "then": {"required": ["notes"]}}}, "additionalProperties": false}}, "title": "css_define", "type": "object", "properties": {"name": {"type": "string", "pattern": "^(?![0-9])(?!(--|-\\d))[a-zA-Z0-9\\u00A0-\\uFFFF_-]*$"}, "id": {"type": "integer"}, "type": {"type": "string"}, "default_value": {"type": "string"}, "version": {"type": "string"}, "author": {"type": "string"}, "consumption_status": {"type": "string", "enum": ["layout-wanted", "layout-only", "skip"]}, "desc": {"type": "string"}, "keywords": {"type": "array", "items": {"type": "string"}}, "values": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string"}, "version": {"type": "string"}, "desc": {"type": "string"}, "align-type": {"type": "string", "enum": ["flex-start", "flex-end", "start", "end"]}}, "required": ["value", "version"]}}, "links": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string"}, "desc": {"type": "string"}}, "required": ["url"]}}, "note": {"type": "array", "items": {"type": "object", "properties": {"literal": {"type": "string"}, "level": {"type": "array", "items": {"type": "string", "enum": ["tip", "info", "warning", "danger"]}}}, "required": ["literal", "level"]}}, "example": {"type": "string"}, "compat_data": {"type": "object", "patternProperties": {"^(?!__compat)(?!lynx_extensions)[a-zA-Z_0-9-$@]*$": {"$ref": "#/definitions/identifier"}, "^lynx_extensions*$": {"$ref": "#/definitions/lynx_extensions_identifier", "tsType": "Identifier"}, "^__compat$": {"$ref": "#/definitions/compat_statement"}}, "additionalProperties": false, "errorMessage": {"additionalProperties": "Feature names can only contain alphanumerical characters or the following symbols: _-$@"}, "maxProperties": 1, "minProperties": 1}}, "required": ["name", "id", "type", "default_value", "version", "author", "consumption_status", "desc"]}