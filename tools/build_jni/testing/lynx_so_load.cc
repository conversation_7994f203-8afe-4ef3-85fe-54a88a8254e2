
// This file is autogenerated.

#include "core/base/android/android_jni.h"

// AUTO_GENERATED_INCLUDE_HEADERS_START
#include "base/include/fml/platform/android/message_loop_android.h"
#include "core/base/android/logging_android.h"
#if TEST_MACRO_
#include "core/runtime/bindings/jsi/modules/android/lynx_promise_impl.h"
#endif
#include "core/runtime/common/utils.h"
#if TEST_MACRO
#include "tools/build_jni/testing/gen/JavaOnlyArray_register_jni.h"
#endif
#include "tools/build_jni/testing/gen/JavaOnlyMap_register_jni.h"
#include "tools/build_jni/testing/gen/LynxBackgroundRuntime_register_jni.h"
#include "tools/build_jni/testing/gen/LynxError_register_jni.h"
#include "tools/build_jni/testing/gen/LynxTemplateRender_register_jni.h"

// AUTO_GENERATED_INCLUDE_HEADERS_END

namespace lynx {

extern "C" JNIEXPORT jint JNI_OnLoad(JavaVM* vm, void* reserved) {
  lynx::base::android::InitVM(vm);
  JNIEnv* env = base::android::AttachCurrentThread();
  // AUTO_GENERATED_REGISTER_METHODS_START
  lynx::base::logging::RegisterJNI(env);
  lynx::fml::MessageLoopAndroid::Register(env);
#if TEST_MACRO
  lynx::jni::RegisterJNIForJavaOnlyArray(env);
#endif
  lynx::jni::RegisterJNIForJavaOnlyMapTest(env);
  lynx::jni::RegisterJNIForLynxBackgroundRuntime(env);
  lynx::jni::RegisterJNIForLynxError(env);
  lynx::jni::RegisterJNIForLynxTemplateRender(env);
  lynx::piper::JSBUtilsMapRegisterJNI(env);
  lynx::piper::JSBUtilsRegisterJNI(env);
#if TEST_MACRO_
  lynx::piper::LynxPromiseImpl::RegisterJNI(env);
#endif

  // AUTO_GENERATED_REGISTER_METHODS_END
  return JNI_VERSION_1_6;
}

}  // namespace lynx
