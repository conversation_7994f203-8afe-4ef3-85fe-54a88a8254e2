# jni register file configs.
jni_register_configs:
  output_path: tools/build_jni/testing/lynx_so_load.cc
  custom_headers:
    - core/base/android/android_jni.h
  namespaces:
    - lynx
  special_cases:
    - header: base/include/fml/platform/android/message_loop_android.h
      method: lynx::fml::MessageLoopAndroid::Register(env);
    - header: core/base/android/logging_android.h
      java: platform/android/lynx_android/src/main/java/com/lynx/tasm/base/LLog.java
      method: lynx::base::logging::RegisterJNI(env);
    - header: core/runtime/common/utils.h
      method: lynx::piper::JSBUtilsRegisterJNI(env);
    - method: lynx::piper::JSBUtilsMapRegisterJNI(env);
    - header: core/runtime/bindings/jsi/modules/android/lynx_promise_impl.h
      method: lynx::piper::LynxPromiseImpl::RegisterJNI(env);
      macro: TEST_MACRO_
# gn file configs.
gn_configs:
  output_path: tools/build_jni/testing/BUILD.gn
  template_name: lynx_source_set
  custom_headers:
    - //core/Lynx.gni
# jni files configs.
jni_class_configs:
  output_dir: tools/build_jni/testing/gen
  jni_classes:
    - java: platform/android/lynx_android/src/main/java/com/lynx/tasm/LynxTemplateRender.java
    - java: platform/android/lynx_android/src/main/java/com/lynx/tasm/LynxBackgroundRuntime.java
    - java: platform/android/lynx_android/src/main/java/com/lynx/react/bridge/JavaOnlyMap.java
      register_method_name: RegisterJNIForJavaOnlyMapTest
    - java: platform/android/lynx_android/src/main/java/com/lynx/react/bridge/JavaOnlyArray.java
      macro: TEST_MACRO
    - java: platform/android/lynx_android/src/main/java/com/lynx/tasm/LynxError.java
