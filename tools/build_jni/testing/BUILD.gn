# This file is autogenerated.

# Copyright 2025 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//core/Lynx.gni")

lynx_source_set("build") {
  sources = [
    "//tools/build_jni/testing/gen/JavaOnlyArray_jni.h",
    "//tools/build_jni/testing/gen/JavaOnlyArray_register_jni.h",
    "//tools/build_jni/testing/gen/JavaOnlyMap_jni.h",
    "//tools/build_jni/testing/gen/JavaOnlyMap_register_jni.h",
    "//tools/build_jni/testing/gen/LynxBackgroundRuntime_jni.h",
    "//tools/build_jni/testing/gen/LynxBackgroundRuntime_register_jni.h",
    "//tools/build_jni/testing/gen/LynxError_jni.h",
    "//tools/build_jni/testing/gen/LynxError_register_jni.h",
    "//tools/build_jni/testing/gen/LynxTemplateRender_jni.h",
    "//tools/build_jni/testing/gen/LynxTemplateRender_register_jni.h",
    "//tools/build_jni/testing/lynx_so_load.cc",
  ]
}
