cpp:
  - enable_lynx_network
  - enable_new_animator_true
  - enable_new_animator_ios
  - enable_new_animator_false
  - enable_new_gesture
  - disable_perf_collector
  - list_new_arch_not_enabled
  - enable_native_list
  - fe_custom_event_bubble_bug
  - fe_custom_event_parameter_bug
  - ui_custom_event_parameter_bug
  - enable_new_animator_default
  - use_radon
  - disable_support_component_js
  - disable_event_refactor
  - disable_multi_touch
  - use_grid_display
  - use_relative_display
  - enable_class_merge
  - use_legacy_lepus_bridge_sync
  - use_legacy_lepus_bridge_async
  - use_native_promise
  - enable_create_ui_async
  - enable_pseudo_selector
  - enable_place_holder_style
  - enable_before_after_pseudo
  - enable_pseudo_not_css
  - enable_high_refresh_rate
  - enable_pseudo_child_css
  - enable_external_class_css
  - list_node
  - disable_parallel_flush_fiber_arch
  - disable_parallel_flush_fiber_radon_arch
  - disable_multi_touch_params_compatible
typescript:
  - bundle_support_load_script
objc:
  - implicit_animation
  - page_rtl
  - page_rtl_with_list
  - disable_new_transform_origin_ios
  - auto_resume_animation
  - layout_animation_create
  - layout_animation_update
  - layout_animation_delete
  - enter_transition_name
  - exit_transition_name
  - pause_transition_name
  - resume_transition_name
  - update_font_scale
  - disable_reuse_animation_state
  - list_ios_recursive_layout
  - list_ios_disable_scroll_anim_during_layout
  - list_ios_use_scroller
  - list_paging_enable
  - disable_touch_refactor
  - set_theme_ios
  - get_theme_ios
  - ui_owner_release_on_child_thread
  - enable_async_list
  - use_legacy_elements
java:
  - hardware_layer
  - new_swiper_not_enabled
  - layout_animation_create_android
  - layout_animation_update_android
  - layout_animation_delete_android
  - enter_transition_name_android
  - exit_transition_name_android
  - pause_transition_name_android
  - resume_transition_name_android
  - update_font_scale
  - disable_reuse_animation_state
  - disable_foldview_stop_touch_stop_fling
  - async_redirect
  - enable_async_request_image
  - set_theme_android
  - get_theme_android
  - enable_async_list
  - use_legacy_elements
