# Copyright 2025 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

declare_args() {
  # regular expression matching to specify targets that should not generate cmake scripts or podspec scripts.
  # run `gn help label_pattern` to get the usage of target_exclude_patterns
  target_exclude_patterns = []
}

template("regitser_custom_target") {
  forward_variables_from(invoker, [ "register_dir" ])

  # write custom_target's name to file which in $root_build_dir/$register_dir
  out_gen_path = "$register_dir"
  current_target_full_name =
      string_replace(rebase_path(":$target_name"), rebase_path("//"), "//")
  current_target_name = string_replace(current_target_full_name, "/:", ":")
  out_file_name =
      string_replace(string_replace(current_target_name, "/", "_"), ":", "_")
  out_file_path =
      rebase_path("$root_build_dir/$out_gen_path/$out_file_name.txt")
  should_register =
      filter_exclude([ "$current_target_name" ], target_exclude_patterns)
  if (should_register == []) {
    lines = []
  } else {
    lines = [ "$current_target_name" ]
  }
  write_file("$out_file_path", lines, "list lines")
}
