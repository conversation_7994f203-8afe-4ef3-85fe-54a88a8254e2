# Automatically generated by gn_to_cmake_script.py
# Please modify configs of compilation in //lynx/tools/gn_tools/test/BUILD.gn

# Set the minimum version of CMAKE that is required
cmake_minimum_required(VERSION 3.4.1)



enable_language(ASM)

set(action_foreach_target 
  l__t__g__t_action_foreach_test
  )

set(${action_foreach_target}__sources
  ${ROOT_PATH}/lynx/tools/gn_tools/test/action_foreach/files/to/dir/sources1.cc
  ${ROOT_PATH}/lynx/tools/gn_tools/test/action_foreach/files/to/dir/sources2.cc
  ${ROOT_PATH}/lynx/tools/gn_tools/test/action_foreach/files/to/dir/sources3.cc
  )

set(${action_foreach_target}__output
  ${ROOT_PATH}/out/gn_cmake_test/gen/action_foreach/files/to/dir/sources1.cc
  ${ROOT_PATH}/out/gn_cmake_test/gen/action_foreach/files/to/dir/sources2.cc
  ${ROOT_PATH}/out/gn_cmake_test/gen/action_foreach/files/to/dir/sources3.cc
  )

add_custom_command(OUTPUT ${${action_foreach_target}__output}
  COMMAND ${CMAKE_COMMAND} -E make_directory "${ROOT_PATH}/out/gn_cmake_test/gen/action_foreach/files/to/dir"
  COMMAND python3 "${ROOT_PATH}/lynx/tools/gn_tools/action_foreach_files.py"
    "--script"
    "${ROOT_PATH}/lynx/tools/gn_tools/action_foreach_test.py"
    "--arguments"
    "@&--sources@&{{source}}@&{{source}}@&--destinations@&${ROOT_PATH}/out/gn_cmake_test/gen/lynx/tools/gn_tools/test/{{source_name_part}}.h@&${ROOT_PATH}/out/gn_cmake_test/gen/lynx/tools/gn_tools/test/{{source_name_part}}.cc"
    "--sources"
    "${${action_foreach_target}__sources}"
    "--target-dir"
    "${ROOT_PATH}/lynx/tools/gn_tools/test"
    "--gn-out-dir"
    "${ROOT_PATH}/out/gn_cmake_test/"
    "--root-dir"
    "${ROOT_PATH}"
  DEPENDS  ${${action_foreach_target}__sources}
  WORKING_DIRECTORY "${ROOT_PATH}/out/gn_cmake_test/"
  COMMENT "action_foreach: ${action_foreach_target}"
  VERBATIM)
add_custom_target(${action_foreach_target} SOURCES ${${action_foreach_target}__sources} DEPENDS ${${action_foreach_target}__output})


add_library(l__t__g__t_cmake_test OBJECT EXCLUDE_FROM_ALL
  ${ROOT_PATH}/lynx/tools/gn_tools/test/gn_test_shared.cc
  ${ROOT_PATH}/lynx/tools/gn_tools/test/gn_test_shared.h
  )

target_compile_definitions(l__t__g__t_cmake_test PRIVATE
  GN_TESTING_DEFINE=1
  USE_OPENSSL=1
  _DEBUG
  _FILE_OFFSET_BITS=64
  _GLIBCXX_DEBUG=1
  _LARGEFILE64_SOURCE
  _LARGEFILE_SOURCE
  _LIBCPP_DISABLE_VISIBILITY_ANNOTATIONS
  _LIBCPP_ENABLE_THREAD_SAFETY_ANNOTATIONS
  __STDC_CONSTANT_MACROS
  __STDC_FORMAT_MACROS
  )

target_include_directories(l__t__g__t_cmake_test PRIVATE
  ${ROOT_PATH}/lynx/tools/gn_tools/test/
  ${ROOT_PATH}/
  ${ROOT_PATH}/lynx/
  ${ROOT_PATH}/out/gn_cmake_test/gen/
  )

target_compile_options(l__t__g__t_cmake_test PRIVATE
  $<$<COMPILE_LANGUAGE:ASM>: "SHELL:-fno-strict-aliasing -fstack-protector --param=ssp-buffer-size=4 -m64 -march=x86-64 -fPIC -pipe -pthread -fcolor-diagnostics" >
  )

target_compile_options(l__t__g__t_cmake_test PRIVATE
  $<$<COMPILE_LANGUAGE:C>: "SHELL:-fvisibility=hidden -fvisibility-inlines-hidden -fno-strict-aliasing -fstack-protector --param=ssp-buffer-size=4 -m64 -march=x86-64 -fPIC -pipe -pthread -fcolor-diagnostics -Wall -Wextra -Wendif-labels -Werror -Wno-missing-field-initializers -Wno-unused-parameter -Wno-vla-extension -Wno-unused-but-set-parameter -Wno-unused-but-set-variable -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-deprecated-copy -Wno-psabi -Wno-deprecated-non-prototype -Wno-enum-constexpr-conversion -Wno-unqualified-std-cast-call -Wno-non-c-typedef-for-linkage -Wno-range-loop-construct -fvisibility=hidden -Wstring-conversion -Wnewline-eof -O0 -g2" >
  )

target_compile_options(l__t__g__t_cmake_test PRIVATE
  $<$<COMPILE_LANGUAGE:CXX>: "SHELL:-fvisibility=hidden -fvisibility-inlines-hidden -fno-strict-aliasing -fstack-protector --param=ssp-buffer-size=4 -m64 -march=x86-64 -fPIC -pipe -pthread -fcolor-diagnostics -Wall -Wextra -Wendif-labels -Werror -Wno-missing-field-initializers -Wno-unused-parameter -Wno-vla-extension -Wno-unused-but-set-parameter -Wno-unused-but-set-variable -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-deprecated-copy -Wno-psabi -Wno-deprecated-non-prototype -Wno-enum-constexpr-conversion -Wno-unqualified-std-cast-call -Wno-non-c-typedef-for-linkage -Wno-range-loop-construct -fvisibility=hidden -Wstring-conversion -Wnewline-eof -O0 -g2 -fvisibility-inlines-hidden -std=c++17 -fno-rtti -stdlib=libstdc++" >
  )

add_dependencies(l__t__g__t_cmake_test
  l__t__g__t_action_foreach_test
  l__t__g__t_copy_test_dirs_to_dir
  l__t__g__t_copy_test_file_to_file
  l__t__g__t_copy_test_files_to_dir
  l__t__g__t_gn_test_action
  )

set(copy_target 
  l__t__g__t_copy_test_dirs_to_dir
  )

set(${copy_target}__sources
  ${ROOT_PATH}/lynx/tools/gn_tools/test/copy/dirs/to/dir/sources1
  ${ROOT_PATH}/lynx/tools/gn_tools/test/copy/dirs/to/dir/sources2
  )

set(${copy_target}__output
  ${ROOT_PATH}/out/gn_cmake_test/gen/lynx/tools/gn_tools/test/sources1
  ${ROOT_PATH}/out/gn_cmake_test/gen/lynx/tools/gn_tools/test/sources2
  )

add_custom_command(OUTPUT ${${copy_target}__output}
  COMMAND ${CMAKE_COMMAND} -E make_directory "${ROOT_PATH}/out/gn_cmake_test/gen/lynx/tools/gn_tools/test"
  COMMAND python3 "${ROOT_PATH}/lynx/tools/gn_tools/copy_files.py"
    "--sources"
    "${ROOT_PATH}/lynx/tools/gn_tools/test/copy/dirs/to/dir/sources1"
    "${ROOT_PATH}/lynx/tools/gn_tools/test/copy/dirs/to/dir/sources2"
    "--destinations"
    "${ROOT_PATH}/out/gn_cmake_test/gen/lynx/tools/gn_tools/test/sources1"
    "${ROOT_PATH}/out/gn_cmake_test/gen/lynx/tools/gn_tools/test/sources2"
  DEPENDS  ${${copy_target}__sources}
  WORKING_DIRECTORY "${ROOT_PATH}/out/gn_cmake_test/"
  COMMENT "copy: ${copy_target}"
  VERBATIM)
add_custom_target(${copy_target} SOURCES ${${copy_target}__sources} DEPENDS ${${copy_target}__output})

add_dependencies("${copy_target}"
  "l__t__g__t_copy_test_files_to_dir")


set(copy_target 
  l__t__g__t_copy_test_file_to_file
  )

set(${copy_target}__sources 
  ${ROOT_PATH}/lynx/tools/gn_tools/test/copy/file/to/file/sources.cc
  )

set(${copy_target}__output 
  ${ROOT_PATH}/out/gn_cmake_test/gen/copy/file/to/file/destination.cc
  )

add_custom_command(OUTPUT ${${copy_target}__output}
  COMMAND ${CMAKE_COMMAND} -E make_directory "${ROOT_PATH}/out/gn_cmake_test/gen/copy/file/to/file"
  COMMAND python3 "${ROOT_PATH}/lynx/tools/gn_tools/copy_files.py"
    "--sources"
    "${ROOT_PATH}/lynx/tools/gn_tools/test/copy/file/to/file/sources.cc"
    "--destinations"
    "${ROOT_PATH}/out/gn_cmake_test/gen/copy/file/to/file/destination.cc"
  DEPENDS  ${${copy_target}__sources}
  WORKING_DIRECTORY "${ROOT_PATH}/out/gn_cmake_test/"
  COMMENT "copy: ${copy_target}"
  VERBATIM)
add_custom_target(${copy_target} SOURCES ${${copy_target}__sources} DEPENDS ${${copy_target}__output})


set(copy_target 
  l__t__g__t_copy_test_files_to_dir
  )

set(${copy_target}__sources
  ${ROOT_PATH}/lynx/tools/gn_tools/test/copy/files/to/dir/sources1.cc
  ${ROOT_PATH}/lynx/tools/gn_tools/test/copy/files/to/dir/sources2.cc
  ${ROOT_PATH}/lynx/tools/gn_tools/test/copy/files/to/dir/sources3.cc
  )

set(${copy_target}__output
  ${ROOT_PATH}/out/gn_cmake_test/gen/copy/files/to/dir/sources1.cc
  ${ROOT_PATH}/out/gn_cmake_test/gen/copy/files/to/dir/sources2.cc
  ${ROOT_PATH}/out/gn_cmake_test/gen/copy/files/to/dir/sources3.cc
  )

add_custom_command(OUTPUT ${${copy_target}__output}
  COMMAND ${CMAKE_COMMAND} -E make_directory "${ROOT_PATH}/out/gn_cmake_test/gen/copy/files/to/dir"
  COMMAND python3 "${ROOT_PATH}/lynx/tools/gn_tools/copy_files.py"
    "--sources"
    "${ROOT_PATH}/lynx/tools/gn_tools/test/copy/files/to/dir/sources1.cc"
    "${ROOT_PATH}/lynx/tools/gn_tools/test/copy/files/to/dir/sources2.cc"
    "${ROOT_PATH}/lynx/tools/gn_tools/test/copy/files/to/dir/sources3.cc"
    "--destinations"
    "${ROOT_PATH}/out/gn_cmake_test/gen/copy/files/to/dir/sources1.cc"
    "${ROOT_PATH}/out/gn_cmake_test/gen/copy/files/to/dir/sources2.cc"
    "${ROOT_PATH}/out/gn_cmake_test/gen/copy/files/to/dir/sources3.cc"
  DEPENDS  ${${copy_target}__sources}
  WORKING_DIRECTORY "${ROOT_PATH}/out/gn_cmake_test/"
  COMMENT "copy: ${copy_target}"
  VERBATIM)
add_custom_target(${copy_target} SOURCES ${${copy_target}__sources} DEPENDS ${${copy_target}__output})


set(action_target 
  l__t__g__t_gn_test_action
  )

set(${action_target}__sources 
  
  )

set(${action_target}__output 
  ${ROOT_PATH}/out/gn_cmake_test/gen/lynx/tools/gn_tools/test/action.txt
  )

add_custom_command(OUTPUT ${${action_target}__output}
  COMMAND ${CMAKE_COMMAND} -E make_directory "${ROOT_PATH}/out/gn_cmake_test/gen/lynx/tools/gn_tools/test"
  COMMAND python3 "${ROOT_PATH}/lynx/tools/gn_tools/test/gn_test_action.py"
    "--enable-foo"
    "--file-list={{response_file_name}}"
  DEPENDS  ${${action_target}__sources}
  WORKING_DIRECTORY "${ROOT_PATH}/out/gn_cmake_test/"
  COMMENT "action: ${action_target}"
  VERBATIM)
add_custom_target(${action_target} SOURCES ${${action_target}__sources} DEPENDS ${${action_target}__output})


add_library(l__t__g__t_gn_test_source OBJECT EXCLUDE_FROM_ALL
  ${ROOT_PATH}/lynx/tools/gn_tools/test/gn_test_source.cc
  ${ROOT_PATH}/lynx/tools/gn_tools/test/gn_test_source.h
  )

target_compile_definitions(l__t__g__t_gn_test_source PRIVATE
  USE_OPENSSL=1
  _DEBUG
  _FILE_OFFSET_BITS=64
  _GLIBCXX_DEBUG=1
  _LARGEFILE64_SOURCE
  _LARGEFILE_SOURCE
  _LIBCPP_DISABLE_VISIBILITY_ANNOTATIONS
  _LIBCPP_ENABLE_THREAD_SAFETY_ANNOTATIONS
  __STDC_CONSTANT_MACROS
  __STDC_FORMAT_MACROS
  )

target_include_directories(l__t__g__t_gn_test_source PRIVATE
  ${ROOT_PATH}/lynx/tools/gn_tools/test/
  ${ROOT_PATH}/
  ${ROOT_PATH}/lynx/
  ${ROOT_PATH}/out/gn_cmake_test/gen/
  )

target_compile_options(l__t__g__t_gn_test_source PRIVATE
  $<$<COMPILE_LANGUAGE:ASM>: "SHELL:-fno-strict-aliasing -fstack-protector --param=ssp-buffer-size=4 -m64 -march=x86-64 -fPIC -pipe -pthread -fcolor-diagnostics" >
  )

target_compile_options(l__t__g__t_gn_test_source PRIVATE
  $<$<COMPILE_LANGUAGE:C>: "SHELL:-fno-strict-aliasing -fstack-protector --param=ssp-buffer-size=4 -m64 -march=x86-64 -fPIC -pipe -pthread -fcolor-diagnostics -Wall -Wextra -Wendif-labels -Werror -Wno-missing-field-initializers -Wno-unused-parameter -Wno-vla-extension -Wno-unused-but-set-parameter -Wno-unused-but-set-variable -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-deprecated-copy -Wno-psabi -Wno-deprecated-non-prototype -Wno-enum-constexpr-conversion -Wno-unqualified-std-cast-call -Wno-non-c-typedef-for-linkage -Wno-range-loop-construct -fvisibility=hidden -Wstring-conversion -Wnewline-eof -O0 -g2" >
  )

target_compile_options(l__t__g__t_gn_test_source PRIVATE
  $<$<COMPILE_LANGUAGE:CXX>: "SHELL:-fno-strict-aliasing -fstack-protector --param=ssp-buffer-size=4 -m64 -march=x86-64 -fPIC -pipe -pthread -fcolor-diagnostics -Wall -Wextra -Wendif-labels -Werror -Wno-missing-field-initializers -Wno-unused-parameter -Wno-vla-extension -Wno-unused-but-set-parameter -Wno-unused-but-set-variable -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-deprecated-copy -Wno-psabi -Wno-deprecated-non-prototype -Wno-enum-constexpr-conversion -Wno-unqualified-std-cast-call -Wno-non-c-typedef-for-linkage -Wno-range-loop-construct -fvisibility=hidden -Wstring-conversion -Wnewline-eof -O0 -g2 -fvisibility-inlines-hidden -std=c++17 -fno-rtti -stdlib=libstdc++" >
  )

add_dependencies(l__t__g__t_gn_test_source
  l__t__g__t_action_foreach_test
  l__t__g__t_copy_test_dirs_to_dir
  l__t__g__t_copy_test_file_to_file
  l__t__g__t_copy_test_files_to_dir
  l__t__g__t_gn_test_action
  )


# Main target
add_library(gn_test SHARED
  
  $<TARGET_OBJECTS:l__t__g__t_cmake_test>
  
  $<TARGET_OBJECTS:l__t__g__t_gn_test_source>
  )

add_dependencies(gn_test
  l__t__g__t_action_foreach_test
  l__t__g__t_copy_test_dirs_to_dir
  l__t__g__t_copy_test_file_to_file
  l__t__g__t_copy_test_files_to_dir
  l__t__g__t_gn_test_action
  )

set(l__t__g__t_gn_test_sub_shared_search_path 
  ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
  )

string(REPLACE 
"\\" 
"/" 
l__t__g__t_gn_test_sub_shared_search_path 
${l__t__g__t_gn_test_sub_shared_search_path}) 

string(REPLACE 
${ROOT_PATH}/lynx/tools/gn_tools/test 
${ROOT_PATH}/lynx/tools/gn_tools/test 
l__t__g__t_gn_test_sub_shared_search_path 
${l__t__g__t_gn_test_sub_shared_search_path}) 

target_link_directories(gn_test PUBLIC  
  ${l__t__g__t_gn_test_sub_shared_search_path}
  )


# subdirectory
add_subdirectory(${ROOT_PATH}/lynx/tools/gn_tools/test 
  gn_test_cmake
  )

target_link_libraries(gn_test
  android
  dl
  gcc
  gn_test_sub
  jnigraphics
  log
  )

# Compiler and Linker flags
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--exclude-libs,ALL,--gc-sections -Wl,--fatal-warnings -m64 -fPIC -Wl,-z,noexecstack -Wl,-z,now -Wl,-z,relro -Wl,-z,defs -pthread -Wl,--undefined-version -stdlib=libstdc++ ")

