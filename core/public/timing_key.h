// Copyright 2023 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#ifndef CORE_PUBLIC_TIMING_KEY_H_
#define CORE_PUBLIC_TIMING_KEY_H_

#include <string>

namespace lynx {
namespace tasm {

// TODO (kechenglong): move Timing<PERSON>ey to timing.h
using TimingKey = std::string;

}  // namespace tasm
}  // namespace lynx

#endif  // CORE_PUBLIC_TIMING_KEY_H_
