# Copyright 2023 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//${lynx_dir}/build_overrides/services_files.gni")
import("//lynx/core/Lynx.gni")

feature_count_shared_sources = [
                                 "feature.h",
                                 "feature_counter.h",
                                 "global_feature_counter.cc",
                                 "global_feature_counter.h",
                               ] + feature_count_extend_shared_sources

if (is_android) {
  feature_count_shared_sources += [ "android/feature_counter_android.cc" ]
}

lynx_core_source_set("feature_count") {
  sources = feature_count_shared_sources
  deps = [
    "//lynx/base/src:base_log_headers",
    "//lynx/core/services/trace:service_trace",
  ]
}
