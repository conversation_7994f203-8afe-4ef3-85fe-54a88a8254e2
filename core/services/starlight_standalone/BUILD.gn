# Copyright 2023 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("starlight.gni")

config("starlight_config") {
  include_dirs = [
    ".",
    "//core",
    "core",
    "//lynx/third_party",
    "//lynx/core",
  ]
}

group("starlight_standalone") {
  deps = [ "core:starlight_native" ]
  if (is_android) {
    deps += [ ":starlight_standalone_android" ]
  } else {
    deps += [
      "//lynx/core/base",
      "//lynx/core/renderer/css",
      "//lynx/core/renderer/starlight",
      "//lynx/core/runtime/vm/lepus",
    ]
  }
}

starlight_source_set("starlight_standalone_android") {
  sources = [
    "android/starlight_android.cc",
    "android/starlight_android.h",
  ]
  deps = [
    "//lynx/base/src:base",
    "//lynx/base/src:base_log",
  ]
}
