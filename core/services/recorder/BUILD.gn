# Copyright 2023 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/core/Lynx.gni")
import("//lynx/testing/test.gni")

# recorder_shared_sources
recorder_shared_sources = [
  "lynxview_init_recorder.cc",
  "lynxview_init_recorder.h",
  "native_module_recorder.cc",
  "native_module_recorder.h",
  "recorder_constants.h",
  "template_assembler_recorder.cc",
  "template_assembler_recorder.h",
  "testbench_base_recorder.cc",
  "testbench_base_recorder.h",
]

if (is_ios) {
  recorder_shared_sources += [
    "//lynx/platform/darwin/ios/lynx/recorder/LynxRecorder.h",
    "//lynx/platform/darwin/ios/lynx/recorder/LynxRecorder.mm",
    "ios/template_assembler_recorder_darwin.h",
    "ios/template_assembler_recorder_darwin.mm",
  ]
}

# recorder_shared_sources end

lynx_core_source_set("recorder") {
  sources = recorder_shared_sources

  deps = [
    "//lynx/base/src:base",
    "//lynx/base/src:base_log_headers",
    "//lynx/core/base:base",
    "//lynx/core/renderer:tasm",
    "//lynx/core/renderer/data:data",
    "//lynx/core/renderer/dom:dom",
    "//lynx/core/renderer/dom:renderer_dom",
    "//lynx/core/runtime",
    "//lynx/core/runtime/bindings/lepus",
    "//lynx/core/runtime/vm/lepus",
    "//lynx/third_party/modp_b64:modp_b64",
    "//lynx/third_party/rapidjson:rapidjson",
  ]
  deps += [ "//third_party/zlib" ]
}

unittest_set("record_testset") {
  configs = [ "//lynx/core:lynx_public_config" ]

  sources = [
    "template_assembler_recorder_unittest.cc",
    "testbench_base_recorder_unittest.cc",
  ]
  deps = [
    ":recorder",
    "//lynx/third_party/rapidjson:rapidjson",
  ]
}

unittest_exec("record_unittest_exec") {
  sources = []
  deps = [ ":record_testset" ]
}

group("record_unit_test") {
  testonly = true

  deps = [
    ":record_testset",
    ":record_unittest_exec",
  ]
}
