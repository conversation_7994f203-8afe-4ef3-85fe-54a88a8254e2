// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
#ifndef CORE_SERVICES_TIMING_HANDLER_TIMING_CONSTANTS_H_
#define CORE_SERVICES_TIMING_HANDLER_TIMING_CONSTANTS_H_

namespace lynx {
namespace tasm {
namespace timing {

static constexpr const char kPipelineOrigin[] = "pipelineOrigin";
/*
 * PerformanceObserver Constants
 * All constants in this file are generated by the Timing API and are used
 * by the PerformanceObserver to facilitate external understanding of the
 * runtime performance of LynxSDK.
 * If you need to add, delete, or modify the following constants, please
 * ensure that the related PerformanceEntry YAML has been updated accordingly.
 */

// onPerformance event name. The global event name that needs to be listened
// to in order to receive PerformanceObserver callbacks in the frontend
// framework.
static constexpr const char kPerformanceRuntimeCallback[] =
    "lynx.performance.onPerformanceEvent";

// The following area will store constants used by different PerformanceEntry,
// categorized by entryType and name. common entryType and name constants
static constexpr const char kEntryType[] = "entryType";
static constexpr const char kEntryName[] = "name";
/*
 * PerformanceEntries of type "init" will block tracking data based on the
 * following structure.
 * {
 *   kEntryType: "init",
 *   kEntryName: "entryName"
 *   attributes: Timestamp
 *   ...
 * }
 */
static constexpr const char kEntryTypeInit[] = "init";
// ------------------ name.container ------------------
static constexpr const char kEntryNameContainer[] = "container";
static constexpr const char kOpenTime[] = "openTime";
static constexpr const char kContainerInitStart[] = "containerInitStart";
static constexpr const char kContainerInitEnd[] = "containerInitEnd";
static constexpr const char kPrepareTemplateStart[] = "prepareTemplateStart";
static constexpr const char kPrepareTemplateEnd[] = "prepareTemplateEnd";
// ------------------ name.lynxview ------------------
static constexpr const char kEntryNameLynxView[] = "lynxview";
static constexpr const char kCreateLynxStart[] = "createLynxStart";
static constexpr const char kCreateLynxEnd[] = "createLynxEnd";
// ------------------ name.backgroundRuntime ------------------
static constexpr const char kEntryNameBackgroundRuntime[] = "backgroundRuntime";
static constexpr const char kLoadCoreStart[] = "loadCoreStart";
static constexpr const char kLoadCoreEnd[] = "loadCoreEnd";

/*
 * PerformanceEntries of type "metric" will block tracking data based on the
 * following structure.
 * {
 *   kEntryTypeMetric,
 *   kEntryName,
 *   attributes: performanceMetric {
 *     kName,
 *     kDuration,
 *     kStartTimestampName,
 *     kStartTimestamp,
 *     kEndTimestampName,
 *     kEndTimestamp
 *   }
 *   ...
 * }
 */
static constexpr const char kEntryTypeMetric[] = "metric";
// ------------------ performanceMetric ------------------
static constexpr const char kName[] = "name";
static constexpr const char kDuration[] = "duration";
static constexpr const char kStartTimestampName[] = "startTimestampName";
static constexpr const char kStartTimestamp[] = "startTimestamp";
static constexpr const char kEndTimestampName[] = "endTimestampName";
static constexpr const char kEndTimestamp[] = "endTimestamp";
// ------------------ name.fcp ------------------
static constexpr const char kEntryNameFCP[] = "fcp";
static constexpr const char kFCP[] = "fcp";
static constexpr const char kLynxFCP[] = "lynxFcp";
static constexpr const char kTotalFCP[] = "totalFcp";
// ------------------ name.actualFmp ------------------
static constexpr const char kEntryNameActualFMP[] = "actualFmp";
static constexpr const char kActualFMP[] = "actualFmp";
static constexpr const char kLynxActualFMP[] = "lynxActualFmp";
static constexpr const char kTotalActualFMP[] = "totalActualFmp";

/*
 * PerformanceEntries of type "pipeline" will block tracking data based on the
 * following structure. Note: Since the SDK cannot clearly identify what markers
 * different versions of various DSLs will input, those data will be stored
 * directly as a Map in kFrameworkRenderingTiming. Note: Unlike Metric and Init,
 * the Pipeline type does not have a specific EntryName. The EntryName will be
 * determined based on the 'origin' stored in the PipelineOption.
 * {
 *   kEntryTypePipeline,
 *   kEntryName,
 *   kFrameworkRenderingTiming: {
 *      timestamp marked by frontend frameworks.
 *   },
 *   attributes,
 *   ...
 * }
 */
static constexpr const char kEntryTypePipeline[] = "pipeline";
// ------------------ name.pipelineOptions.origin ------------------
// origin list
static constexpr const char kUpdateTriggeredByBts[] = "updateTriggeredByBts";
static constexpr const char kUpdateTriggeredByNative[] =
    "updateTriggeredByNative";
static constexpr const char kUpdateGlobalProps[] = "updateGlobalProps";
static constexpr const char kSetNativeProps[] = "setNativeProps";
static constexpr const char kLoadBundle[] = "loadBundle";
static constexpr const char kReloadBundleFromNative[] =
    "reloadBundleFromNative";
static constexpr const char kReloadBundleFromBts[] = "reloadBundleFromBts";
// props
static constexpr const char kIdentifier[] = "identifier";
static constexpr const char kPipelineStart[] = "pipelineStart";
static constexpr const char kPipelineEnd[] = "pipelineEnd";
static constexpr const char kMtsRenderStart[] = "mtsRenderStart";  // render
static constexpr const char kMtsRenderEnd[] = "mtsRenderEnd";
static constexpr const char kResolveStart[] = "resolveStart";  // resolve
static constexpr const char kResolveEnd[] = "resolveEnd";
static constexpr const char kLayoutStart[] = "layoutStart";  // layout
static constexpr const char kLayoutEnd[] = "layoutEnd";
static constexpr const char kPaintingUiOperationExecuteStart[] =
    "paintingUiOperationExecuteStart";  // execute ui-op
static constexpr const char kPaintingUiOperationExecuteEnd[] =
    "paintingUiOperationExecuteEnd";
static constexpr const char kLayoutUiOperationExecuteStart[] =
    "layoutUiOperationExecuteStart";
static constexpr const char kLayoutUiOperationExecuteEnd[] =
    "layoutUiOperationExecuteEnd";
static constexpr const char kPaintEnd[] = "paintEnd";  // paint
static constexpr const char kFrameworkRenderingTiming[] =
    "frameworkRenderingTiming";
// ------------------ frameworkRenderingTiming ------------------
static constexpr const char kFrameworkDsl[] = "dsl";
static constexpr const char kFrameworkStage[] = "stage";
// LoadBundleEntry is a special type of pipeline entry that possesses all the
// fields of a PipelineEntry, in addition to the following extra fields.
static constexpr const char kEntryNameLoadBundle[] = "loadBundle";
static constexpr const char kVmExecuteStart[] = "vmExecuteStart";
static constexpr const char kVmExecuteEnd[] = "vmExecuteEnd";
static constexpr const char kDataProcessorStart[] = "dataProcessorStart";
static constexpr const char kDataProcessorEnd[] = "dataProcessorEnd";
static constexpr const char kSetInitDataStart[] = "setInitDataStart";
static constexpr const char kSetInitDataEnd[] = "setInitDataEnd";
static constexpr const char kLoadBundleStart[] = "loadBundleStart";
static constexpr const char kLoadBundleEnd[] = "loadBundleEnd";
static constexpr const char kParseStart[] = "parseStart";
static constexpr const char kParseEnd[] = "parseEnd";
static constexpr const char kLoadBackgroundStart[] = "loadBackgroundStart";
static constexpr const char kLoadBackgroundEnd[] = "loadBackgroundEnd";

// ================== UNSPECIFIED ==================
// This setion contains constants that are not yet used by any specific
// PerformanceEntry.
static constexpr const char kTemplateBundleParseStart[] =
    "templateBundleParseStart";
static constexpr const char kTemplateBundleParseEnd[] =
    "templateBundleParseEnd";
// ================== UNSPECIFIED ==================

}  // namespace timing
}  // namespace tasm
}  // namespace lynx

#endif  // CORE_SERVICES_TIMING_HANDLER_TIMING_CONSTANTS_H_
