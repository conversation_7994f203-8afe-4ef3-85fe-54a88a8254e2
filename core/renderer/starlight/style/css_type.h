// Copyright 2017 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#ifndef CORE_RENDERER_STARLIGHT_STYLE_CSS_TYPE_H_
#define CORE_RENDERER_STARLIGHT_STYLE_CSS_TYPE_H_

#include <limits.h>
#include <stdint.h>

#include "core/renderer/starlight/style/auto_gen_css_type.h"

namespace lynx {
namespace starlight {

// align-self and align-items types
enum class FlexAlignType : uint8_t {
  kAuto = 0,
  kStretch,
  kFlexStart,
  kFlexEnd,
  kCenter,
  kBaseline,
  kStart,
  kEnd
};

enum class JustifyType : uint8_t { kAuto = 0, kStretch, kStart, kEnd, kCenter };

enum class OverflowType : uint8_t {
  kVisible = 0,
  kHidden,
  kScroll,
};

// Length enumerable types
enum class LengthValueType : uint8_t {
  kAuto = 0,
  kPercentage,
  kCalc,
  kRpx,
  kPx,
  kRem,
  kEm,
  kVw,
  kVh,
  kNumber,
  kPPx,
  kMaxContent,
  kFitContent,
};

enum class BorderStyleType : uint8_t {
  kSolid,
  kDashed,
  kDotted,
  kDouble,
  kGroove,
  kRidge,
  kInset,
  kOutset,
  kHide,
  kNone,
  kUndefined = SCHAR_MAX,  // default no border style
};

enum class BorderWidthType : uint8_t {
  kThin = 1,
  kMedium = 3,
  kThick = 5,
};

enum class TimingFunctionType : uint8_t {
  kLinear = 0,
  kEaseIn,
  kEaseOut,
  kEaseInEaseOut,
  kSquareBezier,
  kCubicBezier,
  kSteps,
};

enum class StepsType : uint8_t {
  kInvalid = 0,
  kStart,
  kEnd,
  kJumpBoth,
  kJumpNone
};

// FIXME(wangyifei.20010605): Using bit to represent a type may not be enough in
// the future, So instead of using the bit shift operation to represent the
// type, The new type uses the default values generated by the enumerated class.
enum class AnimationPropertyType : uint32_t {
  kNone = 0,
  kOpacity = 1 << 0,
  kScaleX = 1 << 1,
  kScaleY = 1 << 2,
  kScaleXY = 1 << 3,
  kWidth = 1 << 4,
  kHeight = 1 << 5,
  kBackgroundColor = 1 << 6,
  kVisibility = 1 << 7,
  kLeft = 1 << 8,
  kTop = 1 << 9,
  kRight = 1 << 10,
  kBottom = 1 << 11,
  kTransform = 1 << 12,
  kColor = 1 << 13,
  kMaxWidth = 1 << 14,
  kMinWidth = 1 << 15,
  kMaxHeight = 1 << 16,
  kMinHeight = 1 << 17,
  kPaddingLeft,
  kPaddingRight,
  kPaddingTop,
  kPaddingBottom,
  kMarginLeft,
  kMarginRight,
  kMarginTop,
  kMarginBottom,
  kBorderLeftWidth,
  kBorderRightWidth,
  kBorderTopWidth,
  kBorderBottomWidth,
  kBorderTopColor,
  kBorderLeftColor,
  kBorderRightColor,
  kBorderBottomColor,
  kFlexBasis,
  kFlexGrow,
  kBorderWidth,
  kBorderColor,
  kMargin,
  kPadding,
  kFilter,
  kBoxShadow,
  kAll = 1 << 18,
  kLegacyAll_1 = kOpacity | kWidth | kHeight | kBackgroundColor | kVisibility |
                 kLeft | kTop | kRight | kBottom | kTransform,
  kLegacyAll_2 = kLegacyAll_1 | kColor,
  kLegacyAll_3 = kLegacyAll_2 | kMaxWidth | kMinWidth | kMaxHeight | kMinHeight,
};

#define ALL_LAYOUT_ANIMATION_PROPERTY                       \
  starlight::AnimationPropertyType::kWidth,                 \
      starlight::AnimationPropertyType::kHeight,            \
      starlight::AnimationPropertyType::kTop,               \
      starlight::AnimationPropertyType::kLeft,              \
      starlight::AnimationPropertyType::kRight,             \
      starlight::AnimationPropertyType::kBottom,            \
      starlight::AnimationPropertyType::kBorderLeftWidth,   \
      starlight::AnimationPropertyType::kBorderRightWidth,  \
      starlight::AnimationPropertyType::kBorderTopWidth,    \
      starlight::AnimationPropertyType::kBorderBottomWidth, \
      starlight::AnimationPropertyType::kPaddingLeft,       \
      starlight::AnimationPropertyType::kPaddingRight,      \
      starlight::AnimationPropertyType::kPaddingTop,        \
      starlight::AnimationPropertyType::kPaddingBottom,     \
      starlight::AnimationPropertyType::kMarginLeft,        \
      starlight::AnimationPropertyType::kMarginRight,       \
      starlight::AnimationPropertyType::kMarginTop,         \
      starlight::AnimationPropertyType::kMarginBottom,      \
      starlight::AnimationPropertyType::kMaxWidth,          \
      starlight::AnimationPropertyType::kMinWidth,          \
      starlight::AnimationPropertyType::kMaxHeight,         \
      starlight::AnimationPropertyType::kMinHeight,         \
      starlight::AnimationPropertyType::kFlexBasis

enum class TransformType : uint32_t {
  kNone = 0,
  kTranslate = 1,
  kTranslateX = 1 << 1,
  kTranslateY = 1 << 2,
  kTranslateZ = 1 << 3,
  kTranslate3d = 1 << 4,
  kRotate = 1 << 5,
  kRotateX = 1 << 6,
  kRotateY = 1 << 7,
  kRotateZ = 1 << 8,
  kScale = 1 << 9,
  kScaleX = 1 << 10,
  kScaleY = 1 << 11,
  kSkew = 1 << 12,
  kSkewX = 1 << 13,
  kSkewY = 1 << 14,
  kMatrix = 1 << 15,
  kMatrix3d = 1 << 16
};

enum class TextDecorationType : uint16_t {
  // text-decoration-line
  kNone = 0,
  kUnderLine = 1 << 0,
  kLineThrough = 1 << 1,
  // text-decoration-style
  kSolid = 1 << 2,
  kDouble = 1 << 3,
  kDotted = 1 << 4,
  kDashed = 1 << 5,
  kWavy = 1 << 6,
  // No idea why the f the color enum is needed.
  kColor = 1 << 7
};

enum class FontFaceSrcType : uint8_t {
  kUrl = 1,
  kLocal = 2,
};

enum LayoutAnimationType : uint8_t { kCreate = 0, kUpdate, kDelete };

enum class ShadowOption : uint8_t {
  kNone = 0,
  kInset = 1,
  kInitial = 2,
  kInherit = 3
};

enum class VerticalAlignType : uint8_t {
  kDefault = 0,
  kBaseline = 1,
  kSub = 2,
  kSuper = 3,
  kTop = 4,
  kTextTop = 5,
  kMiddle = 6,
  kBottom = 7,
  kTextBottom = 8,
  kLength = 9,
  kPercent = 10,
  kCenter = 11
};

enum class ContentType : uint8_t {
  kInvalid = 0,
  kPlain = 1,
  kImage,
  kText,
  kLayer
};

enum class PlatformLengthUnit : uint8_t {
  NUMBER = 0,
  PERCENTAGE = 1,
  CALC = 2
};

enum class PerspectiveLengthUnit : uint8_t {
  NUMBER = 0,
  VW = 1,
  VH = 2,
  DEFAULT = 3,
  PX = 4
};

enum class BackgroundImageType : uint8_t {
  kNone = 0,
  kUrl,
  kLinearGradient,
  kRadialGradient
};

enum class CursorType : uint8_t {
  kUrl = 0,
  kKeyword,
};

enum class BackgroundOriginType : uint8_t {
  kPaddingBox = 0,  // version:1.0
  kBorderBox = 1,   // version:1.0
  kContentBox = 2,  // version:1.0
};

enum class BackgroundRepeatType : uint8_t {
  kRepeat = 0,    // version:1.0
  kNoRepeat = 1,  // version:1.0
  kRepeatX = 2,   // version:1.0
  kRepeatY = 3,   // version:1.0
  kRound = 4,     // version:1.0
  kSpace = 5,     // version:1.0
};

enum class BackgroundPositionType : uint8_t {
  kTop = (1 << 5),
  kRight,
  kBottom,
  kLeft,
  kCenter,
};

enum class BackgroundSizeType : uint8_t {
  kAuto = (1 << 5),
  kCover,
  kContain,
};

enum class BackgroundClipType : uint8_t {
  kPaddingBox = 0,  // version:1.0
  kBorderBox = 1,   // version:1.0
  kContentBox = 2,  // version:1.0
  kText = 3,        // version:1.0
};

enum class RadialGradientShapeType : uint8_t {
  kEllipse = 0,
  kCircle,
};

enum class RadialGradientSizeType : uint8_t {
  kFarthestCorner = 0,
  kFarthestSide,
  kClosestCorner,
  kClosestSide,
  kLength,
};

enum class AnimationDirectionType : uint8_t {
  kNormal = 0,            // version:1.0
  kReverse = 1,           // version:1.0
  kAlternate = 2,         // version:1.0
  kAlternateReverse = 3,  // version:1.0
};

enum class AnimationFillModeType : uint8_t {
  kNone = 0,       // version:1.0
  kForwards = 1,   // version:1.0
  kBackwards = 2,  // version:1.0
  kBoth = 3,       // version:1.0
};

enum class AnimationPlayStateType : uint8_t {
  kPaused = 0,   // version:1.0
  kRunning = 1,  // version:1.0
};

enum class GridAutoFlowType : uint8_t {
  kRow = 0,          // version:2.1
  kColumn = 1,       // version:2.1
  kDense = 2,        // version:2.1
  kRowDense = 3,     // version:2.1
  kColumnDense = 4,  // version:2.1
};

// Reserved numbers for relative align type
class RelativeAlignType {
 public:
  static constexpr int kNone = -1;
  static constexpr int kParent = 0;

 private:
  RelativeAlignType() {}
};

enum class FilterType : uint8_t {
  kNone = 0,
  kGrayscale = 1,
  kBlur = 2,
};

enum class BasicShapeType : uint8_t {
  kUnknown = 0,
  kCircle = 1,
  kEllipse = 2,
  kPath = 3,
  kSuperEllipse = 4,
  kInset = 5,
};

enum class LinearGradientDirection : uint8_t {
  kNone = 0,
  kTop = 1,
  kBottom = 2,
  kLeft = 3,
  kRight = 4,
  kTopRight = 5,
  kTopLeft = 6,
  kBottomRight = 7,
  kBottomLeft = 8,
  kAngle = 9,
};

enum class OffsetRotateType : uint32_t {
  kAuto = (1 << 10),
  kAngle,
};

}  // namespace starlight
}  // namespace lynx

#endif  // CORE_RENDERER_STARLIGHT_STYLE_CSS_TYPE_H_
