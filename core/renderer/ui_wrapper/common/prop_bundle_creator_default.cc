// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "core/renderer/ui_wrapper/common/prop_bundle_creator_default.h"

namespace lynx {
namespace tasm {

std::unique_ptr<PropBundle> PropBundleCreatorDefault::CreatePropBundle() {
  return nullptr;
}

}  // namespace tasm
}  // namespace lynx
