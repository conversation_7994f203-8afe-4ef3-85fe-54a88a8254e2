// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#ifndef CORE_RENDERER_UI_WRAPPER_LAYOUT_NO_NEEDED_LAYOUT_LIST_H_
#define CORE_RENDERER_UI_WRAPPER_LAYOUT_NO_NEEDED_LAYOUT_LIST_H_

namespace lynx {
namespace tasm {
// These constant var are used to store node tag names and props names without
// creating a platform layer Layoutnode begin: image component
static constexpr const char* const kImageComponent = "image";
static constexpr const char* const kAutoSizeAttribute = "auto-size";
// end
}  // namespace tasm
}  // namespace lynx

#endif  // CORE_RENDERER_UI_WRAPPER_LAYOUT_NO_NEEDED_LAYOUT_LIST_H_
