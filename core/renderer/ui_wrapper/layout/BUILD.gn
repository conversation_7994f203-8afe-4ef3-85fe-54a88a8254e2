# Copyright 2024 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/core/Lynx.gni")
import("//lynx/testing/test.gni")

# ui_wrapper_layout_shared_sources
ui_wrapper_layout_shared_sources = [
  "layout_context.cc",
  "layout_context.h",
  "layout_context_data.h",
  "layout_node.cc",
  "layout_node.h",
  "list_node.cc",
  "list_node.h",
  "no_needed_layout_list.h",
]

if (!enable_unittests) {
  if (is_android) {
    ui_wrapper_layout_shared_sources += [
      "android/layout_context_android.cc",
      "android/layout_context_android.h",
      "android/layout_node_android.cc",
      "android/layout_node_android.h",
      "android/layout_node_manager_android.cc",
      "android/layout_node_manager_android.h",
    ]
  } else if (is_ios) {
    ui_wrapper_layout_shared_sources += [
      "ios/layout_context_darwin.h",
      "ios/layout_context_darwin.mm",
    ]
  }
}

lynx_core_source_set("layout") {
  sources = ui_wrapper_layout_shared_sources

  deps = [
    "//lynx/core/public",
    "//lynx/core/renderer/starlight:starlight",
    "//lynx/core/renderer/ui_wrapper/common:common",
    "//lynx/core/value_wrapper:value_wrapper",
  ]
}
