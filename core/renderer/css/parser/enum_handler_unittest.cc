// Copyright 2019 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "core/renderer/css/parser/enum_handler.h"

#include "core/renderer/css/unit_handler.h"
#include "core/renderer/starlight/style/css_type.h"
#include "third_party/googletest/googletest/include/gtest/gtest.h"

namespace lynx {
namespace tasm {
namespace test {
// enum handler test case.
// enum handler is auto generated by tools/css_generator/css_parser_generator.py
// so just test only one can cover all enum type handler.
TEST(Enum<PERSON><PERSON><PERSON>, Handler) {
  auto id = CSSPropertyID::kPropertyIDAlignContent;
  StyleMap output;
  CSSParserConfigs configs;

  auto impl = lepus::Value("align");
  bool ret;

  ret = UnitHandler::Process(id, impl, output, configs);
  EXPECT_FALSE(ret);
  EXPECT_TRUE(output.empty());

  impl = lepus::Value("flex-start");
  ret = UnitHandler::Process(id, impl, output, configs);
  EXPECT_TRUE(ret);
  EXPECT_FALSE(output.empty());
  EXPECT_FALSE(output.find(id) == output.end());
  EXPECT_TRUE(output[id].GetValue().IsInt32());
  EXPECT_EQ((starlight::AlignContentType)output[id].GetValue().Number(),
            starlight::AlignContentType::kFlexStart);

  impl = lepus::Value("flex-end");
  UnitHandler::Process(id, impl, output, configs);
  EXPECT_EQ((starlight::AlignContentType)output[id].GetValue().Number(),
            starlight::AlignContentType::kFlexEnd);

  impl = lepus::Value("center");
  UnitHandler::Process(id, impl, output, configs);
  EXPECT_EQ((starlight::AlignContentType)output[id].GetValue().Number(),
            starlight::AlignContentType::kCenter);

  impl = lepus::Value("stretch");
  UnitHandler::Process(id, impl, output, configs);
  EXPECT_EQ((starlight::AlignContentType)output[id].GetValue().Number(),
            starlight::AlignContentType::kStretch);

  impl = lepus::Value("space-between");
  UnitHandler::Process(id, impl, output, configs);
  EXPECT_EQ((starlight::AlignContentType)output[id].GetValue().Number(),
            starlight::AlignContentType::kSpaceBetween);

  impl = lepus::Value("space-around");
  UnitHandler::Process(id, impl, output, configs);
  EXPECT_EQ((starlight::AlignContentType)output[id].GetValue().Number(),
            starlight::AlignContentType::kSpaceAround);

  // other cases see before @link{process}.
}
}  // namespace test

}  // namespace tasm
}  // namespace lynx
