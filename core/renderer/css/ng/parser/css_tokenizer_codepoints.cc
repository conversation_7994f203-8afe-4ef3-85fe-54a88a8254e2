// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Copyright 2022 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

// Auto-generated by make_css_tokenizer_codepoints.py

const CSSTokenizer::CodePoint CSSTokenizer::kCodePoints[128] = {
    &CSSTokenizer::EndOfFile,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    &CSSTokenizer::WhiteSpace,
    &CSSTokenizer::WhiteSpace,
    0,
    &CSSTokenizer::WhiteSpace,
    &CSSTokenizer::WhiteSpace,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    &CSSTokenizer::WhiteSpace,
    0,
    &CSSTokenizer::StringStart,
    &CSSTokenizer::Hash,
    &CSSTokenizer::DollarSign,
    0,
    0,
    &CSSTokenizer::StringStart,
    &CSSTokenizer::LeftParenthesis,
    &CSSTokenizer::RightParenthesis,
    &CSSTokenizer::Asterisk,
    &CSSTokenizer::PlusOrFullStop,
    &CSSTokenizer::Comma,
    &CSSTokenizer::HyphenMinus,
    &CSSTokenizer::PlusOrFullStop,
    &CSSTokenizer::Solidus,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::AsciiDigit,
    &CSSTokenizer::Colon,
    &CSSTokenizer::SemiColon,
    &CSSTokenizer::LessThan,
    0,
    0,
    0,
    &CSSTokenizer::CommercialAt,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::LetterU,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::LeftBracket,
    &CSSTokenizer::ReverseSolidus,
    &CSSTokenizer::RightBracket,
    &CSSTokenizer::CircumflexAccent,
    &CSSTokenizer::NameStart,
    0,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::LetterU,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::NameStart,
    &CSSTokenizer::LeftBrace,
    &CSSTokenizer::VerticalLine,
    &CSSTokenizer::RightBrace,
    &CSSTokenizer::Tilde,
    0,
};
const unsigned codePointsNumber = 128;
