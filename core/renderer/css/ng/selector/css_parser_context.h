// Copyright 2022 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
// Copyright 2016 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef CORE_RENDERER_CSS_NG_SELECTOR_CSS_PARSER_CONTEXT_H_
#define CORE_RENDERER_CSS_NG_SELECTOR_CSS_PARSER_CONTEXT_H_

namespace lynx {
namespace css {

class CSSParserContext final {};
}  // namespace css
}  // namespace lynx

#endif  // CORE_RENDERER_CSS_NG_SELECTOR_CSS_PARSER_CONTEXT_H_
