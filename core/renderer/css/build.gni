# Copyright 2020 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/config.gni")

lynx_css_core_sources = [
  "css_color.cc",
  "css_color.h",
  "css_content_data.cc",
  "css_content_data.h",
  "css_debug_msg.h",
  "css_font_face_token.cc",
  "css_font_face_token.h",
  "css_fragment.cc",
  "css_fragment.h",
  "css_fragment_decorator.cc",
  "css_fragment_decorator.h",
  "css_keyframes_token.cc",
  "css_keyframes_token.h",
  "css_keywords.cc",
  "css_keywords.h",
  "css_parser_token.cc",
  "css_parser_token.h",
  "css_property.cc",
  "css_property.h",
  "css_property_id.h",
  "css_selector_constants.cc",
  "css_selector_constants.h",
  "css_sheet.cc",
  "css_sheet.h",
  "css_style_sheet_manager.cc",
  "css_style_sheet_manager.h",
  "css_utils.cc",
  "css_utils.h",
  "css_value.cc",
  "css_value.h",
  "css_variable_handler.cc",
  "css_variable_handler.h",
  "ng/css_ng_utils.cc",
  "ng/css_ng_utils.h",
  "ng/invalidation/invalidation_set.cc",
  "ng/invalidation/invalidation_set.h",
  "ng/invalidation/invalidation_set_feature.h",
  "ng/invalidation/rule_invalidation_set.cc",
  "ng/invalidation/rule_invalidation_set.h",
  "ng/matcher/selector_matcher.cc",
  "ng/matcher/selector_matcher.h",
  "ng/selector/lynx_css_selector.cc",
  "ng/selector/lynx_css_selector.h",
  "ng/selector/lynx_css_selector_extra_data.cc",
  "ng/selector/lynx_css_selector_extra_data.h",
  "ng/selector/lynx_css_selector_list.cc",
  "ng/selector/lynx_css_selector_list.h",
  "ng/style/rule_data.h",
  "ng/style/rule_set.cc",
  "ng/style/rule_set.h",
  "ng/style/style_rule.h",
  "parser/animation_direction_handler.cc",
  "parser/animation_direction_handler.h",
  "parser/animation_fill_mode_handler.cc",
  "parser/animation_fill_mode_handler.h",
  "parser/animation_iteration_count_handler.cc",
  "parser/animation_iteration_count_handler.h",
  "parser/animation_name_handler.cc",
  "parser/animation_name_handler.h",
  "parser/animation_play_state_handler.cc",
  "parser/animation_play_state_handler.h",
  "parser/animation_property_handler.cc",
  "parser/animation_property_handler.h",
  "parser/animation_shorthand_handler.cc",
  "parser/animation_shorthand_handler.h",
  "parser/aspect_ratio_handler.cc",
  "parser/aspect_ratio_handler.h",
  "parser/auto_font_size_handler.cc",
  "parser/auto_font_size_handler.h",
  "parser/auto_font_size_preset_sizes_handler.cc",
  "parser/auto_font_size_preset_sizes_handler.h",
  "parser/background_box_handler.cc",
  "parser/background_box_handler.h",
  "parser/background_clip_handler.cc",
  "parser/background_clip_handler.h",
  "parser/background_image_handler.cc",
  "parser/background_image_handler.h",
  "parser/background_position_handler.cc",
  "parser/background_position_handler.h",
  "parser/background_repeat_handler.cc",
  "parser/background_repeat_handler.h",
  "parser/background_shorthand_handler.cc",
  "parser/background_shorthand_handler.h",
  "parser/background_size_handler.cc",
  "parser/background_size_handler.h",
  "parser/bool_handler.cc",
  "parser/bool_handler.h",
  "parser/border_handler.cc",
  "parser/border_handler.h",
  "parser/border_radius_handler.cc",
  "parser/border_radius_handler.h",
  "parser/border_style_handler.cc",
  "parser/border_style_handler.h",
  "parser/border_width_handler.cc",
  "parser/border_width_handler.h",
  "parser/clip_path_handler.cc",
  "parser/clip_path_handler.h",
  "parser/color_handler.cc",
  "parser/color_handler.h",
  "parser/css_parser_configs.h",
  "parser/css_string_parser.cc",
  "parser/css_string_parser.h",
  "parser/css_string_scanner.cc",
  "parser/css_string_scanner.h",
  "parser/cursor_handler.cc",
  "parser/cursor_handler.h",
  "parser/enum_handler.cc",
  "parser/enum_handler.h",
  "parser/filter_handler.cc",
  "parser/filter_handler.h",
  "parser/flex_flow_handler.cc",
  "parser/flex_flow_handler.h",
  "parser/flex_handler.cc",
  "parser/flex_handler.h",
  "parser/font_length_handler.cc",
  "parser/font_length_handler.h",
  "parser/four_sides_shorthand_handler.cc",
  "parser/four_sides_shorthand_handler.h",
  "parser/gap_handler.cc",
  "parser/gap_handler.h",
  "parser/grid_position_handler.cc",
  "parser/grid_position_handler.h",
  "parser/grid_template_handler.cc",
  "parser/grid_template_handler.h",
  "parser/handler_defines.h",
  "parser/length_handler.cc",
  "parser/length_handler.h",
  "parser/list_gap_handler.cc",
  "parser/list_gap_handler.h",
  "parser/mask_shorthand_handler.cc",
  "parser/mask_shorthand_handler.h",
  "parser/number_handler.cc",
  "parser/number_handler.h",
  "parser/offset_rotate_handler.cc",
  "parser/offset_rotate_handler.h",
  "parser/relative_align_handler.cc",
  "parser/relative_align_handler.h",
  "parser/shadow_handler.cc",
  "parser/shadow_handler.h",
  "parser/string_handler.cc",
  "parser/string_handler.h",
  "parser/text_decoration_handler.cc",
  "parser/text_decoration_handler.h",
  "parser/text_stroke_handler.cc",
  "parser/text_stroke_handler.h",
  "parser/time_handler.cc",
  "parser/time_handler.h",
  "parser/timing_function_handler.cc",
  "parser/timing_function_handler.h",
  "parser/transform_handler.cc",
  "parser/transform_handler.h",
  "parser/transform_origin_handler.cc",
  "parser/transform_origin_handler.h",
  "parser/transition_shorthand_handler.cc",
  "parser/transition_shorthand_handler.h",
  "parser/vertical_align_handler.cc",
  "parser/vertical_align_handler.h",
  "shared_css_fragment.cc",
  "shared_css_fragment.h",
  "style_node.cc",
  "style_node.h",
  "unit_handler.cc",
  "unit_handler.h",
]

if (!enable_unittests) {
  lynx_css_core_sources += [ "ng/selector/lynx_css_selector_empty.cc" ]
}

if (is_android) {
  lynx_css_core_sources += [
    "android/css_color_utils.cc",
    "android/css_gradient_utils.cc",
  ]
}

lynx_css_core_sources_path = get_path_info(lynx_css_core_sources, "abspath")

lynx_css_runtime_sources = [
  "computed_css_style.cc",
  "computed_css_style.h",
  "css_style_utils.cc",
  "css_style_utils.h",
  "measure_context.cc",
  "measure_context.h",
]
lynx_css_runtime_sources_path =
    get_path_info(lynx_css_runtime_sources, "abspath")

lynx_css_dom_sources = [
  "dynamic_css_configs.h",
  "dynamic_css_styles_manager.cc",
  "dynamic_css_styles_manager.h",
  "dynamic_direction_styles_manager.cc",
  "dynamic_direction_styles_manager.h",
  "select_element_token.cc",
  "select_element_token.h",
]
lynx_css_dom_sources_path = get_path_info(lynx_css_dom_sources, "abspath")

lynx_css_parser_sources = [
  "ng/parser/css_parser_idioms.cc",
  "ng/parser/css_parser_idioms.h",
  "ng/parser/css_parser_token.cc",
  "ng/parser/css_parser_token.h",
  "ng/parser/css_parser_token_range.cc",
  "ng/parser/css_parser_token_stream.cc",
  "ng/parser/css_tokenizer.cc",
  "ng/parser/css_tokenizer.h",
  "ng/parser/css_tokenizer_input_stream.cc",
  "ng/parser/css_tokenizer_input_stream.h",
  "ng/parser/string_to_number.cc",
  "ng/selector/css_parser_context.h",
  "ng/selector/css_selector_parser.cc",
  "ng/selector/css_selector_parser.h",
  "ng/selector/lynx_css_selector_impl.cc",
]
lynx_css_parser_sources_path = get_path_info(lynx_css_parser_sources, "abspath")

lynx_css_decoder_sources = [
  "auto_gen_css_decoder.cc",
  "auto_gen_css_decoder.h",
  "css_decoder.cc",
  "css_decoder.h",
]
lynx_css_decoder_sources_path =
    get_path_info(lynx_css_decoder_sources, "abspath")

lynx_css_extra_sources = [
  "css_fragment_decorator.cc",
  "css_fragment_decorator.h",
  "css_variable_handler.cc",
  "css_variable_handler.h",
  "ng/selector/lynx_css_selector_empty.cc",
]

lynx_css_extra_sources_path = get_path_info(lynx_css_extra_sources, "abspath")
