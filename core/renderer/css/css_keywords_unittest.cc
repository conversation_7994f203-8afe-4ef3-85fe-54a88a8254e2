// Copyright 2023 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "core/renderer/css/css_keywords.h"

#include <map>

#include "third_party/googletest/googletest/include/gtest/gtest.h"

namespace lynx {
namespace tasm {

TEST(CSSKeywords, TokenTypeCheck) {
  static const std::map<std::string, TokenType> ret{
      {"rgb", TokenType::RGB},
      {"rgba", TokenType::RGBA},
      {"hsl", TokenType::HSL},
      {"hsla", TokenType::HSLA},
      {"url", TokenType::URL},
      {"none", TokenType::NONE},
      {"to", TokenType::TO},
      {"left", TokenType::LEFT},
      {"top", TokenType::TOP},
      {"right", TokenType::RIGHT},
      {"bottom", TokenType::BOTTOM},
      {"center", TokenType::CENTER},
      {"px", TokenType::PX},
      {"rpx", TokenType::RPX},
      {"rem", TokenType::REM},
      {"em", TokenType::EM},
      {"vw", TokenType::VW},
      {"vh", TokenType::VH},
      {"sp", TokenType::SP},
      {"ppx", TokenType::PPX},
      {"fr", TokenType::FR},
      {"max-content", TokenType::MAX_CONTENT},
      {"deg", TokenType::DEG},
      {"grad", TokenType::GRAD},
      {"rad", TokenType::RAD},
      {"turn", TokenType::TURN},
      {"auto", TokenType::AUTO},
      {"cover", TokenType::COVER},
      {"contain", TokenType::CONTAIN},
      {"repeat-x", TokenType::REPEAT_X},
      {"repeat-y", TokenType::REPEAT_Y},
      {"repeat", TokenType::REPEAT},
      {"no-repeat", TokenType::NO_REPEAT},
      {"space", TokenType::SPACE},
      {"round", TokenType::ROUND},
      {"border-box", TokenType::BORDER_BOX},
      {"padding-box", TokenType::PADDING_BOX},
      {"content-box", TokenType::CONTENT_BOX},
      {"text", TokenType::TEXT},
      {"linear-gradient", TokenType::LINEAR_GRADIENT},
      {"radial-gradient", TokenType::RADIAL_GRADIENT},
      {"closest-side", TokenType::CLOSEST_SIDE},
      {"closest-corner", TokenType::CLOSEST_CORNER},
      {"farthest-side", TokenType::FARTHEST_SIDE},
      {"farthest-corner", TokenType::FARTHEST_CORNER},
      {"ellipse", TokenType::ELLIPSE},
      {"circle", TokenType::CIRCLE},
      {"polygon", TokenType::POLYGON},
      {"at", TokenType::AT},
      {"data", TokenType::DATA},
      {"thin", TokenType::THIN},
      {"medium", TokenType::MEDIUM},
      {"thick", TokenType::THICK},
      {"hidden", TokenType::HIDDEN},
      {"dotted", TokenType::DOTTED},
      {"dashed", TokenType::DASHED},
      {"solid", TokenType::SOLID},
      {"double", TokenType::DOUBLE},
      {"groove", TokenType::GROOVE},
      {"ridge", TokenType::RIDGE},
      {"inset", TokenType::INSET},
      {"outset", TokenType::OUTSET},
      {"underline", TokenType::UNDERLINE},
      {"line-through", TokenType::LINE_THROUGH},
      {"wavy", TokenType::WAVY},
      {"format", TokenType::FORMAT},
      {"local", TokenType::LOCAL},
      {"normal", TokenType::NORMAL},
      {"bold", TokenType::BOLD},
      {"tobottom", TokenType::TOBOTTOM},
      {"toleft", TokenType::TOLEFT},
      {"toright", TokenType::TORIGHT},
      {"totop", TokenType::TOTOP},
      {"path", TokenType::PATH},
      {"super-ellipse", TokenType::SUPER_ELLIPSE},
      {"calc", TokenType::CALC},
      {"env", TokenType::ENV},
      {"grayscale", TokenType::GRAYSCALE},
      {"blur", TokenType::BLUR},
      {"fit-content", TokenType::FIT_CONTENT},
      {"transparent", TokenType::TRANSPARENT},
      {"aliceblue", TokenType::ALICEBLUE},
      {"antiquewhite", TokenType::ANTIQUEWHITE},
      {"aqua", TokenType::AQUA},
      {"aquamarine", TokenType::AQUAMARINE},
      {"azure", TokenType::AZURE},
      {"beige", TokenType::BEIGE},
      {"bisque", TokenType::BISQUE},
      {"black", TokenType::BLACK},
      {"blanchedalmond", TokenType::BLANCHEDALMOND},
      {"blue", TokenType::BLUE},
      {"blueviolet", TokenType::BLUEVIOLET},
      {"brown", TokenType::BROWN},
      {"burlywood", TokenType::BURLYWOOD},
      {"cadetblue", TokenType::CADETBLUE},
      {"chartreuse", TokenType::CHARTREUSE},
      {"chocolate", TokenType::CHOCOLATE},
      {"coral", TokenType::CORAL},
      {"cornflowerblue", TokenType::CORNFLOWERBLUE},
      {"cornsilk", TokenType::CORNSILK},
      {"crimson", TokenType::CRIMSON},
      {"cyan", TokenType::CYAN},
      {"darkblue", TokenType::DARKBLUE},
      {"darkcyan", TokenType::DARKCYAN},
      {"darkgoldenrod", TokenType::DARKGOLDENROD},
      {"darkgray", TokenType::DARKGRAY},
      {"darkgreen", TokenType::DARKGREEN},
      {"darkgrey", TokenType::DARKGREY},
      {"darkkhaki", TokenType::DARKKHAKI},
      {"darkmagenta", TokenType::DARKMAGENTA},
      {"darkolivegreen", TokenType::DARKOLIVEGREEN},
      {"darkorange", TokenType::DARKORANGE},
      {"darkorchid", TokenType::DARKORCHID},
      {"darkred", TokenType::DARKRED},
      {"darksalmon", TokenType::DARKSALMON},
      {"darkseagreen", TokenType::DARKSEAGREEN},
      {"darkslateblue", TokenType::DARKSLATEBLUE},
      {"darkslategray", TokenType::DARKSLATEGRAY},
      {"darkslategrey", TokenType::DARKSLATEGREY},
      {"darkturquoise", TokenType::DARKTURQUOISE},
      {"darkviolet", TokenType::DARKVIOLET},
      {"deeppink", TokenType::DEEPPINK},
      {"deepskyblue", TokenType::DEEPSKYBLUE},
      {"dimgray", TokenType::DIMGRAY},
      {"dimgrey", TokenType::DIMGREY},
      {"dodgerblue", TokenType::DODGERBLUE},
      {"firebrick", TokenType::FIREBRICK},
      {"floralwhite", TokenType::FLORALWHITE},
      {"forestgreen", TokenType::FORESTGREEN},
      {"fuchsia", TokenType::FUCHSIA},
      {"gainsboro", TokenType::GAINSBORO},
      {"ghostwhite", TokenType::GHOSTWHITE},
      {"gold", TokenType::GOLD},
      {"goldenrod", TokenType::GOLDENROD},
      {"gray", TokenType::GRAY},
      {"green", TokenType::GREEN},
      {"greenyellow", TokenType::GREENYELLOW},
      {"grey", TokenType::GREY},
      {"honeydew", TokenType::HONEYDEW},
      {"hotpink", TokenType::HOTPINK},
      {"indianred", TokenType::INDIANRED},
      {"indigo", TokenType::INDIGO},
      {"ivory", TokenType::IVORY},
      {"khaki", TokenType::KHAKI},
      {"lavender", TokenType::LAVENDER},
      {"lavenderblush", TokenType::LAVENDERBLUSH},
      {"lawngreen", TokenType::LAWNGREEN},
      {"lemonchiffon", TokenType::LEMONCHIFFON},
      {"lightblue", TokenType::LIGHTBLUE},
      {"lightcoral", TokenType::LIGHTCORAL},
      {"lightcyan", TokenType::LIGHTCYAN},
      {"lightgoldenrodyellow", TokenType::LIGHTGOLDENRODYELLOW},
      {"lightgray", TokenType::LIGHTGRAY},
      {"lightgreen", TokenType::LIGHTGREEN},
      {"lightgrey", TokenType::LIGHTGREY},
      {"lightpink", TokenType::LIGHTPINK},
      {"lightsalmon", TokenType::LIGHTSALMON},
      {"lightseagreen", TokenType::LIGHTSEAGREEN},
      {"lightskyblue", TokenType::LIGHTSKYBLUE},
      {"lightslategray", TokenType::LIGHTSLATEGRAY},
      {"lightslategrey", TokenType::LIGHTSLATEGREY},
      {"lightsteelblue", TokenType::LIGHTSTEELBLUE},
      {"lightyellow", TokenType::LIGHTYELLOW},
      {"lime", TokenType::LIME},
      {"limegreen", TokenType::LIMEGREEN},
      {"linen", TokenType::LINEN},
      {"magenta", TokenType::MAGENTA},
      {"maroon", TokenType::MAROON},
      {"mediumaquamarine", TokenType::MEDIUMAQUAMARINE},
      {"mediumblue", TokenType::MEDIUMBLUE},
      {"mediumorchid", TokenType::MEDIUMORCHID},
      {"mediumpurple", TokenType::MEDIUMPURPLE},
      {"mediumseagreen", TokenType::MEDIUMSEAGREEN},
      {"mediumslateblue", TokenType::MEDIUMSLATEBLUE},
      {"mediumspringgreen", TokenType::MEDIUMSPRINGGREEN},
      {"mediumturquoise", TokenType::MEDIUMTURQUOISE},
      {"mediumvioletred", TokenType::MEDIUMVIOLETRED},
      {"midnightblue", TokenType::MIDNIGHTBLUE},
      {"mintcream", TokenType::MINTCREAM},
      {"mistyrose", TokenType::MISTYROSE},
      {"moccasin", TokenType::MOCCASIN},
      {"navajowhite", TokenType::NAVAJOWHITE},
      {"navy", TokenType::NAVY},
      {"oldlace", TokenType::OLDLACE},
      {"olive", TokenType::OLIVE},
      {"olivedrab", TokenType::OLIVEDRAB},
      {"orange", TokenType::ORANGE},
      {"orangered", TokenType::ORANGERED},
      {"orchid", TokenType::ORCHID},
      {"palegoldenrod", TokenType::PALEGOLDENROD},
      {"palegreen", TokenType::PALEGREEN},
      {"paleturquoise", TokenType::PALETURQUOISE},
      {"palevioletred", TokenType::PALEVIOLETRED},
      {"papayawhip", TokenType::PAPAYAWHIP},
      {"peachpuff", TokenType::PEACHPUFF},
      {"peru", TokenType::PERU},
      {"pink", TokenType::PINK},
      {"plum", TokenType::PLUM},
      {"powderblue", TokenType::POWDERBLUE},
      {"purple", TokenType::PURPLE},
      {"red", TokenType::RED},
      {"rosybrown", TokenType::ROSYBROWN},
      {"royalblue", TokenType::ROYALBLUE},
      {"saddlebrown", TokenType::SADDLEBROWN},
      {"salmon", TokenType::SALMON},
      {"sandybrown", TokenType::SANDYBROWN},
      {"seagreen", TokenType::SEAGREEN},
      {"seashell", TokenType::SEASHELL},
      {"sienna", TokenType::SIENNA},
      {"silver", TokenType::SILVER},
      {"skyblue", TokenType::SKYBLUE},
      {"slateblue", TokenType::SLATEBLUE},
      {"slategray", TokenType::SLATEGRAY},
      {"slategrey", TokenType::SLATEGREY},
      {"snow", TokenType::SNOW},
      {"springgreen", TokenType::SPRINGGREEN},
      {"steelblue", TokenType::STEELBLUE},
      {"tan", TokenType::TAN},
      {"teal", TokenType::TEAL},
      {"thistle", TokenType::THISTLE},
      {"tomato", TokenType::TOMATO},
      {"turquoise", TokenType::TURQUOISE},
      {"violet", TokenType::VIOLET},
      {"wheat", TokenType::WHEAT},
      {"white", TokenType::WHITE},
      {"whitesmoke", TokenType::WHITESMOKE},
      {"yellow", TokenType::YELLOW},
      {"yellowgreen", TokenType::YELLOWGREEN},
      {"rotate", TokenType::ROTATE},
      {"rotatex", TokenType::ROTATE_X},
      {"rotatey", TokenType::ROTATE_Y},
      {"rotatez", TokenType::ROTATE_Z},
      {"translate", TokenType::TRANSLATE},
      {"translate3d", TokenType::TRANSLATE_3D},
      {"translatex", TokenType::TRANSLATE_X},
      {"translatey", TokenType::TRANSLATE_Y},
      {"translatez", TokenType::TRANSLATE_Z},
      {"scale", TokenType::SCALE},
      {"scalex", TokenType::SCALE_X},
      {"scaley", TokenType::SCALE_Y},
      {"skew", TokenType::SKEW},
      {"skewx", TokenType::SKEW_X},
      {"skewy", TokenType::SKEW_Y},
      {"matrix", TokenType::MATRIX},
      {"matrix3d", TokenType::MATRIX_3D},
      {"opacity", TokenType::OPACITY},
      {"scalexy", TokenType::SCALE_XY},
      {"width", TokenType::WIDTH},
      {"height", TokenType::HEIGHT},
      {"background-color", TokenType::BACKGROUND_COLOR},
      {"color", TokenType::COLOR},
      {"visibility", TokenType::VISIBILITY},
      {"transform", TokenType::TRANSFORM},
      {"all", TokenType::ALL},
      {"max-width", TokenType::MAX_WIDTH},
      {"max-height", TokenType::MAX_HEIGHT},
      {"min-width", TokenType::MIN_WIDTH},
      {"min-height", TokenType::MIN_HEIGHT},
      {"margin-left", TokenType::MARGIN_LEFT},
      {"margin-right", TokenType::MARGIN_RIGHT},
      {"margin-top", TokenType::MARGIN_TOP},
      {"margin-bottom", TokenType::MARGIN_BOTTOM},
      {"padding-left", TokenType::PADDING_LEFT},
      {"padding-right", TokenType::PADDING_RIGHT},
      {"padding-top", TokenType::PADDING_TOP},
      {"padding-bottom", TokenType::PADDING_BOTTOM},
      {"border-left-color", TokenType::BORDER_LEFT_COLOR},
      {"border-right-color", TokenType::BORDER_RIGHT_COLOR},
      {"border-top-color", TokenType::BORDER_TOP_COLOR},
      {"border-bottom-color", TokenType::BORDER_BOTTOM_COLOR},
      {"border-left-width", TokenType::BORDER_LEFT_WIDTH},
      {"border-right-width", TokenType::BORDER_RIGHT_WIDTH},
      {"border-top-width", TokenType::BORDER_TOP_WIDTH},
      {"border-bottom-width", TokenType::BORDER_BOTTOM_WIDTH},
      {"flex-basis", TokenType::FLEX_BASIS},
      {"flex-grow", TokenType::FLEX_GROW},
      {"border-width", TokenType::BORDER_WIDTH},
      {"border-color", TokenType::BORDER_COLOR},
      {"margin", TokenType::MARGIN},
      {"padding", TokenType::PADDING},
      {"filter", TokenType::FILTER},
      {"linear", TokenType::LINEAR},
      {"ease-in", TokenType::EASE_IN},
      {"ease-out", TokenType::EASE_OUT},
      {"ease-in-ease-out", TokenType::EASE_IN_EASE_OUT},
      {"ease", TokenType::EASE},
      {"ease-in-out", TokenType::EASE_IN_OUT},
      {"step-start", TokenType::STEP_START},
      {"step-end", TokenType::STEP_END},
      {"square-bezier", TokenType::SQUARE_BEZIER},
      {"cubic-bezier", TokenType::CUBIC_BEZIER},
      {"steps", TokenType::STEPS},
      {"ms", TokenType::MILLISECOND},
      {"s", TokenType::SECOND},
      {"reverse", TokenType::REVERSE},
      {"alternate", TokenType::ALTERNATE},
      {"alternate-reverse", TokenType::ALTERNATE_REVERSE},
      {"forwards", TokenType::FORWARDS},
      {"backwards", TokenType::BACKWARDS},
      {"both", TokenType::BOTH},
      {"infinite", TokenType::INFINITE},
      {"paused", TokenType::PAUSED},
      {"running", TokenType::RUNNING},
      {"true", TokenType::TOKEN_TRUE},
      {"false", TokenType::TOKEN_FALSE}};

  EXPECT_EQ(static_cast<int>(TokenType::TOKEN_FALSE) -
                static_cast<int>(TokenType::UNKNOWN),
            ret.size());
  for (const auto& [s, t] : ret) {
    EXPECT_EQ(GetTokenValue(s.c_str(), s.length())->type, t);
  }
}
}  // namespace tasm
}  // namespace lynx
