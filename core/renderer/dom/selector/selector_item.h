// Copyright 2022 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#ifndef CORE_RENDERER_DOM_SELECTOR_SELECTOR_ITEM_H_
#define CORE_RENDERER_DOM_SELECTOR_SELECTOR_ITEM_H_

namespace lynx {
namespace tasm {
class SelectorItem {
 protected:
  SelectorItem() = default;
  virtual ~SelectorItem() = default;
};
}  // namespace tasm
}  // namespace lynx

#endif  // CORE_RENDERER_DOM_SELECTOR_SELECTOR_ITEM_H_
