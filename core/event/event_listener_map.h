/*
 * Copyright (C) 1999 <PERSON> (<EMAIL>)
 *           (C) 1999 <PERSON><PERSON> (<EMAIL>)
 *           (C) 2001 <PERSON> (<EMAIL>)
 * Copyright (C) 2004, 2005, 2006, 2007, 2012 Apple Inc. All rights reserved.
 * Copyright (C) 2006 <PERSON><PERSON> (<EMAIL>)
 *           (C) 2007, 2008 <PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2011 <PERSON> (<EMAIL>)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY APPLE COMPUTER, INC. ``AS IS'' AND ANY
 * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL APPLE COMPUTER, INC. OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 * PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

// Copyright 2023 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "base/include/vector.h"
#include "core/event/event_listener.h"

#ifndef CORE_EVENT_EVENT_LISTENER_MAP_H_
#define CORE_EVENT_EVENT_LISTENER_MAP_H_

namespace lynx {
namespace event {

class EventListener;
using EventListenerVector =
    base::InlineVector<std::shared_ptr<EventListener>, 2>;

class EventListenerMap {
 public:
  struct AddOptions {
    // TODO(songshourui.null):
  };

  void Clear();
  bool IsEmpty() const;
  bool Contains(const std::string& type) const;

  bool Add(const std::string& type, std::shared_ptr<EventListener> listener,
           const AddOptions& options = AddOptions());
  bool Remove(const std::string& type, std::shared_ptr<EventListener> listener);

  EventListenerVector* Find(const std::string& type);

 private:
  base::InlineVector<std::pair<std::string, EventListenerVector>, 2> map_;
};

}  // namespace event
}  // namespace lynx

#endif  // CORE_EVENT_EVENT_LISTENER_MAP_H_
