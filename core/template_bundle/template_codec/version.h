// Copyright 2019 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#ifndef CORE_TEMPLATE_BUNDLE_TEMPLATE_CODEC_VERSION_H_
#define CORE_TEMPLATE_BUNDLE_TEMPLATE_CODEC_VERSION_H_

#include <cstdio>
#include <iostream>
#include <string>

#include "base/include/version_util.h"

namespace lynx {
namespace tasm {

inline constexpr base::Version V_1_0(1, 0);
inline constexpr base::Version V_1_1(1, 1);
inline constexpr base::Version V_1_2(1, 2);
inline constexpr base::Version V_1_3(1, 3);
inline constexpr base::Version V_1_4(1, 4);
inline constexpr base::Version V_1_6(1, 6);
inline constexpr base::Version V_2_0(2, 0);
inline constexpr base::Version V_2_1(2, 1);
inline constexpr base::Version V_2_2(2, 2);
inline constexpr base::Version V_2_3(2, 3);
inline constexpr base::Version V_2_4(2, 4);
inline constexpr base::Version V_2_5(2, 5);
inline constexpr base::Version V_2_6(2, 6);
inline constexpr base::Version V_2_7(2, 7);
inline constexpr base::Version V_2_8(2, 8);
inline constexpr base::Version V_2_9(2, 9);
inline constexpr base::Version V_2_10(2, 10);
inline constexpr base::Version V_2_11(2, 11);
inline constexpr base::Version V_2_12(2, 12);
inline constexpr base::Version V_2_13(2, 13);
inline constexpr base::Version V_2_14(2, 14);
inline constexpr base::Version V_2_15(2, 15);
inline constexpr base::Version V_2_16(2, 16);
inline constexpr base::Version V_2_17(2, 17);
inline constexpr base::Version V_2_18(2, 18);
inline constexpr base::Version V_3_0(3, 0);
inline constexpr base::Version V_3_1(3, 1);
inline constexpr base::Version V_3_2(3, 2);
inline constexpr base::Version V_3_3(3, 3);

}  // namespace tasm
}  // namespace lynx
#endif  // CORE_TEMPLATE_BUNDLE_TEMPLATE_CODEC_VERSION_H_
