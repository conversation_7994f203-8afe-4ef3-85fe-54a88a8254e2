# Copyright 2023 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/core/Lynx.gni")
import("//lynx/testing/test.gni")

# binary_decoder_shared_sources
binary_decoder_shared_sources = [
  # TODO(chenyouhui.duke): Use a better way
  "//${lynx_dir}/core/template_bundle/template_codec/binary_decoder/lynx_binary_base_template_reader.cc",
  "//${lynx_dir}/core/template_bundle/template_codec/binary_decoder/lynx_binary_config_helper.cc",
  "binary_decoder_trace_event_def.h",
  "element_binary_reader.cc",
  "element_binary_reader.h",
  "lynx_binary_base_css_reader.cc",
  "lynx_binary_base_css_reader.h",
  "lynx_binary_base_template_reader.h",
  "lynx_binary_config_decoder.cc",
  "lynx_binary_config_decoder.h",
  "lynx_binary_config_helper.h",
  "lynx_binary_lazy_reader_delegate.h",
  "lynx_binary_reader.cc",
  "lynx_binary_reader.h",
  "page_config.cc",
  "page_config.h",
  "parallel_parse_task_scheduler.cc",
  "parallel_parse_task_scheduler.h",
  "template_binary_reader.cc",
  "template_binary_reader.h",
]

# binary_decoder_shared_sources end

lynx_core_source_set("binary_decoder") {
  sources = binary_decoder_shared_sources
  deps = [
    "//lynx/base/src:base_log_headers",
    "//lynx/third_party/rapidjson:rapidjson",
  ]
}

unittest_set("binary_decoder_testset") {
  public_configs = [ "//lynx/core:lynx_public_config" ]
  sources = [
    "lynx_binary_config_decoder_unittest.cc",
    "lynx_binary_config_decoder_unittest.h",
  ]
  deps = [
    "//lynx/core/renderer:tasm",
    "//lynx/core/renderer/dom:dom",
    "//lynx/core/renderer/dom:renderer_dom",
    "//lynx/core/renderer/starlight",
    "//lynx/core/runtime/bindings/lepus",
    "//lynx/core/shell/testing:mock_tasm_delegate_testset",
    "//lynx/third_party/quickjs",
    "//lynx/third_party/rapidjson:rapidjson",
  ]
  public_deps = [ ":binary_decoder" ]
}

unittest_exec("binary_decoder_unittest_exec") {
  sources = []
  deps = [ ":binary_decoder_testset" ]
}
