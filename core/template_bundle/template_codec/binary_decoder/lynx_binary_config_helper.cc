// Copyright 2022 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "core/template_bundle/template_codec/binary_decoder/lynx_binary_config_helper.h"

namespace lynx {
namespace tasm {

void LynxBinaryConfigHelper::HandlePageConfig(
    const rapidjson::Document& doc, std::shared_ptr<PageConfig>& page_config) {}

}  // namespace tasm
}  // namespace lynx
