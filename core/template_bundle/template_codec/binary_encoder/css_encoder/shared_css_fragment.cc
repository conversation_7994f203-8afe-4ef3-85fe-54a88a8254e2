// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "core/template_bundle/template_codec/binary_encoder/css_encoder/shared_css_fragment.h"

namespace lynx {
namespace encoder {

// TODO(songshourui.null): Sink some logic down to encoder::SharedCSSFragment
// and place the function implementation in this file.

}  // namespace encoder
}  // namespace lynx
