js_v8_bridge_shared_sources =
    rebase_path(
        [
          "//lynx/core/runtime/jscache/v8/v8_cache_generator.cc",
          "//lynx/core/runtime/jscache/v8/v8_cache_generator.h",
          "//lynx/core/runtime/profile/v8/v8_runtime_profiler_wrapper.h",
          "//lynx/core/runtime/profile/v8/v8_runtime_profiler_wrapper_impl.cc",
          "//lynx/core/runtime/profile/v8/v8_runtime_profiler_wrapper_impl.h",
          "//$lynx_dir/core/runtime/jsi/v8/v8_isolate_wrapper_impl.cc",
          "//$lynx_dir/core/runtime/jsi/v8/v8_isolate_wrapper_impl.h",
          "v8_api.h",
          "v8_context_wrapper.h",
          "v8_context_wrapper_impl.cc",
          "v8_context_wrapper_impl.h",
          "v8_exception.cc",
          "v8_exception.h",
          "v8_helper.cc",
          "v8_helper.h",
          "v8_host_function.cc",
          "v8_host_function.h",
          "v8_host_object.cc",
          "v8_host_object.h",
          "v8_inspector_manager.h",
          "v8_isolate_wrapper.h",
          "v8_runtime.cc",
          "v8_runtime.h",
        ])
