// Copyright 2023 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "core/runtime/bindings/lepus/event/context_proxy_in_lepus_test.h"

namespace lynx {
namespace piper {
namespace test {

TEST_F(ContextProxyInLepusTest, ContextProxyInLepusTest0) {
  // TODO(songshourui.null): impl this later.
}

}  // namespace test
}  // namespace piper
}  // namespace lynx
