// Copyright 2023 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "core/runtime/bindings/lepus/event/lepus_event_listener_test.h"

namespace lynx {
namespace tasm {
namespace test {

TEST_F(LepusClosureEventListenerTest, LepusClosureEventListenerTest0) {
  // TODO(songshourui.null): impl this later.
}

}  // namespace test
}  // namespace tasm
}  // namespace lynx
