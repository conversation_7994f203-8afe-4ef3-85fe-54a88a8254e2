# Copyright 2023 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.
import("//lynx/core/Lynx.gni")
import("//lynx/testing/test.gni")

# lepus_runtime_shared_sources
lepus_runtime_shared_sources = [
  "context_proxy_in_lepus.cc",
  "context_proxy_in_lepus.h",
  "lepus_event_listener.cc",
  "lepus_event_listener.h",
]

lynx_core_source_set("lepus_runtime") {
  sources = lepus_runtime_shared_sources

  deps = []

  public_deps = [
    "//lynx/core/event",
    "//lynx/core/runtime/bindings/common/event:runtime_common",
  ]
}

unittest_set("lepus_runtime_testset") {
  sources = [
    "context_proxy_in_lepus_test.cc",
    "context_proxy_in_lepus_test.h",
    "lepus_event_listener_test.cc",
    "lepus_event_listener_test.h",
  ]
  public_deps = [
    ":lepus_runtime",
    "//lynx/core/renderer:tasm",
    "//lynx/core/renderer/dom:dom",
    "//lynx/core/renderer/dom:dom_testset",
    "//lynx/core/renderer/dom:renderer_dom",
    "//lynx/core/renderer/starlight",
    "//lynx/core/runtime/bindings/lepus",
    "//lynx/core/shell/testing:mock_tasm_delegate_testset",
    "//lynx/third_party/quickjs",
  ]
  data_deps = []
}

unittest_exec("lepus_runtime_unittests_exec") {
  sources = []
  deps = [ ":lepus_runtime_testset" ]
}

group("lepus_runtime_tests") {
  testonly = true
  deps = [ ":lepus_runtime_unittests_exec" ]
}
