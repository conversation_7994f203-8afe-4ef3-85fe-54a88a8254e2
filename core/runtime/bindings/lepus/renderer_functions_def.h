// Copyright 2020 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
#ifndef CORE_RUNTIME_BINDINGS_LEPUS_RENDERER_FUNCTIONS_DEF_H_
#define CORE_RUNTIME_BINDINGS_LEPUS_RENDERER_FUNCTIONS_DEF_H_

#define NORMAL_RENDERER_FUNCTIONS(V)  \
  V(GetTextInfo)                      \
  V(GetDevTool)                       \
  V(GetJSContext)                     \
  V(GetCoreContext)                   \
  V(GetUIContext)                     \
  V(GetNativeContext)                 \
  V(GetEngineContext)                 \
  V(RuntimeAddEventListener)          \
  V(RuntimeRemoveEventListener)       \
  V(DispatchEvent)                    \
  V(PostMessage)                      \
  V(ReplaceStyleSheetByIdWithBase64)  \
  V(RemoveStyleSheetById)             \
  V(IndexOf)                          \
  V(GetLength)                        \
  V(SetValueToMap)                    \
  V(AttachPage)                       \
  V(CreateVirtualNode)                \
  V(CreateVirtualPage)                \
  V(CreateVirtualComponent)           \
  V(AppendChild)                      \
  V(AppendSubTree)                    \
  V(CloneSubTree)                     \
  V(SetAttributeTo)                   \
  V(SetStaticStyleTo2)                \
  V(SetEventTo)                       \
  V(SetScriptEventTo)                 \
  V(CreateVirtualListNode)            \
  V(AppendListComponentInfo)          \
  V(CreateVirtualPlug)                \
  V(CreateVirtualPlugWithComponent)   \
  V(MarkComponentHasRenderer)         \
  V(ProcessComponentData)             \
  V(AddVirtualPlugToComponent)        \
  V(AppendVirtualPlugToComponent)     \
  V(GetComponentProps)                \
  V(GetComponentContextData)          \
  V(GetComponentData)                 \
  V(CreateComponentByName)            \
  V(CreateDynamicVirtualComponent)    \
  V(RenderDynamicComponent)           \
  V(AddFallbackToDynamicComponent)    \
  V(RegisterDataProcessor)            \
  V(RegisterElementWorklet)           \
  V(ThemedTranslation)                \
  V(ThemedTranslationLegacy)          \
  V(ThemedLanguageTranslation)        \
  V(SetContextData)                   \
  V(AddEventListener)                 \
  V(I18nResourceTranslation)          \
  V(ReFlushPage)                      \
  V(SetComponent)                     \
  V(GetGlobalProps)                   \
  V(HandleExceptionInLepus)           \
  V(MarkPageElement)                  \
  V(FilterI18nResource)               \
  V(SendGlobalEvent)                  \
  V(GetSystemInfo)                    \
  V(FiberCreateElement)               \
  V(FiberCreatePage)                  \
  V(FiberGetPageElement)              \
  V(FiberCreateComponent)             \
  V(FiberCreateList)                  \
  V(FiberCreateView)                  \
  V(FiberCreateScrollView)            \
  V(FiberCreateText)                  \
  V(FiberCreateImage)                 \
  V(FiberCreateRawText)               \
  V(FiberCreateNonElement)            \
  V(FiberCreateFrame)                 \
  V(FiberCreateWrapperElement)        \
  V(FiberAppendElement)               \
  V(FiberRemoveElement)               \
  V(FiberInsertElementBefore)         \
  V(FiberFirstElement)                \
  V(FiberLastElement)                 \
  V(FiberNextElement)                 \
  V(FiberReplaceElement)              \
  V(FiberReplaceElements)             \
  V(FiberSwapElement)                 \
  V(FiberGetParent)                   \
  V(FiberGetChildren)                 \
  V(FiberCloneElement)                \
  V(FiberMarkTemplateElement)         \
  V(FiberIsTemplateElement)           \
  V(FiberMarkPartElement)             \
  V(FiberIsPartElement)               \
  V(FiberGetTemplateParts)            \
  V(FiberElementIsEqual)              \
  V(FiberGetElementUniqueID)          \
  V(FiberGetTag)                      \
  V(FiberSetAttribute)                \
  V(FiberGetAttributes)               \
  V(FiberGetAttributeByName)          \
  V(FiberGetAttributeNames)           \
  V(FiberAddConfig)                   \
  V(FiberSetConfig)                   \
  V(FiberGetElementConfig)            \
  V(FiberAddClass)                    \
  V(FiberSetClasses)                  \
  V(FiberGetClasses)                  \
  V(FiberAddInlineStyle)              \
  V(FiberSetInlineStyles)             \
  V(FiberGetInlineStyles)             \
  V(FiberGetInlineStyle)              \
  V(FiberSetParsedStyles)             \
  V(FiberGetComputedStyles)           \
  V(FiberAddEvent)                    \
  V(FiberSetEvents)                   \
  V(FiberGetEvent)                    \
  V(FiberGetEvents)                   \
  V(FiberSetID)                       \
  V(FiberGetID)                       \
  V(FiberAddDataset)                  \
  V(FiberSetDataset)                  \
  V(FiberGetDataset)                  \
  V(FiberGetDataByKey)                \
  V(FiberGetComponentID)              \
  V(FiberUpdateComponentID)           \
  V(FiberUpdateComponentInfo)         \
  V(FiberUpdateListCallbacks)         \
  V(FiberFlushElementTree)            \
  V(FiberOnLifecycleEvent)            \
  V(FiberElementFromBinary)           \
  V(FiberElementFromBinaryAsync)      \
  V(FiberQueryComponent)              \
  V(FiberSetCSSId)                    \
  V(FiberQuerySelector)               \
  V(FiberQuerySelectorAll)            \
  V(FiberSetLepusInitData)            \
  V(FiberCreateSignal)                \
  V(FiberWriteSignal)                 \
  V(FiberReadSignal)                  \
  V(FiberCreateComputation)           \
  V(FiberCreateMemo)                  \
  V(FiberUnTrack)                     \
  V(FiberRunUpdates)                  \
  V(FiberCreateScope)                 \
  V(FiberGetScope)                    \
  V(FiberCleanUp)                     \
  V(FiberOnCleanUp)                   \
  V(SetSourceMapRelease)              \
  V(ReportError)                      \
  V(AirCreateElement)                 \
  V(AirGetElement)                    \
  V(AirCreatePage)                    \
  V(AirCreateComponent)               \
  V(AirCreateBlock)                   \
  V(AirCreateIf)                      \
  V(AirCreateRadonIf)                 \
  V(AirCreateFor)                     \
  V(AirCreatePlug)                    \
  V(AirCreateSlot)                    \
  V(AirAppendElement)                 \
  V(AirRemoveElement)                 \
  V(AirInsertElementBefore)           \
  V(AirGetElementUniqueID)            \
  V(AirGetElementTag)                 \
  V(AirSetAttribute)                  \
  V(AirSetInlineStyles)               \
  V(AirSetEvent)                      \
  V(AirSetID)                         \
  V(AirGetElementByID)                \
  V(AirGetElementByLepusID)           \
  V(AirUpdateIfNodeIndex)             \
  V(AirUpdateForNodeIndex)            \
  V(AirUpdateForChildCount)           \
  V(AirGetForNodeChildWithIndex)      \
  V(AirPushForNode)                   \
  V(AirPopForNode)                    \
  V(AirGetChildElementByIndex)        \
  V(AirPushDynamicNode)               \
  V(AirGetDynamicNode)                \
  V(AirSetComponentProp)              \
  V(AirRenderComponentInLepus)        \
  V(AirUpdateComponentInLepus)        \
  V(AirGetComponentInfo)              \
  V(AirUpdateComponentInfo)           \
  V(AirGetData)                       \
  V(AirGetProps)                      \
  V(AirSetData)                       \
  V(AirFlushElement)                  \
  V(AirFlushElementTree)              \
  V(TriggerLepusBridge)               \
  V(TriggerLepusBridgeSync)           \
  V(AirSetDataSet)                    \
  V(AirSendGlobalEvent)               \
  V(AirSetClasses)                    \
  V(SetTimeout)                       \
  V(ClearTimeout)                     \
  V(SetInterval)                      \
  V(ClearTimeInterval)                \
  V(AirGetElementByUniqueID)          \
  V(AirGetRootElement)                \
  V(RemoveEventListener)              \
  V(TriggerComponentEvent)            \
  V(AirCreateRawText)                 \
  V(AirPushComponentNode)             \
  V(AirPopComponentNode)              \
  V(AirGetParentForNode)              \
  V(AirFlushTree)                     \
  V(SetStaticAttrTo)                  \
  V(SetStyleTo)                       \
  V(SetDynamicStyleTo)                \
  V(CreateGestureDetector)            \
  V(FiberSetGestureDetector)          \
  V(FiberRemoveGestureDetector)       \
  V(SetStaticStyleTo)                 \
  V(SetDataSetTo)                     \
  V(SetStaticEventTo)                 \
  V(SetClassTo)                       \
  V(SetStaticClassTo)                 \
  V(SetId)                            \
  V(UpdateComponentInfo)              \
  V(GetComponentInfo)                 \
  V(CreateSlot)                       \
  V(SetProp)                          \
  V(SetData)                          \
  V(InvokeUIMethod)                   \
  V(FiberGetDiffData)                 \
  V(FiberGetElementByUniqueID)        \
  V(FiberUpdateIfNodeIndex)           \
  V(FiberUpdateForChildCount)         \
  V(FiberCreateIf)                    \
  V(FiberCreateFor)                   \
  V(FiberCreateBlock)                 \
  V(LoadLepusChunk)                   \
  V(FiberCreateElementWithProperties) \
  V(FiberSetGestureState)             \
  V(FiberConsumeGesture)              \
  V(RequestAnimationFrame)            \
  V(CancelAnimationFrame)             \
  V(GetCustomSectionSync)             \
  V(SetSessionStorageItem)            \
  V(GetSessionStorageItem)            \
  V(FiberAsyncResolveElement)         \
  V(BindPipelineIDWithTimingFlag)     \
  V(MarkTiming)                       \
  V(AddTimingListener)                \
  V(GeneratePipelineOptions)          \
  V(OnPipelineStart)                  \
  V(LynxAddReporterCustomInfo)        \
  V(ProfileStart)                     \
  V(ProfileEnd)                       \
  V(ProfileMark)                      \
  V(ProfileFlowId)                    \
  V(IsProfileRecording)
#endif  // CORE_RUNTIME_BINDINGS_LEPUS_RENDERER_FUNCTIONS_DEF_H_
