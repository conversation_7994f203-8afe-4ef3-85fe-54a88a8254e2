# Copyright 2022 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.
import("//lynx/core/Lynx.gni")

config("test_module_config") {
  include_dirs = [ "." ]
}

lynx_core_source_set("test_module") {
  sources = [
    "napi_test_context.cc",
    "napi_test_context.h",
    "napi_test_element.cc",
    "napi_test_element.h",
    "test_context.h",
    "test_element.h",
    "test_module.cc",
    "test_module.h",
  ]
  public_configs = [
    ":test_module_config",
    "//lynx/third_party/napi:napi_config",
  ]
  deps = [ "//lynx/core/runtime/bindings/napi:napi_binding_core" ]
}
