# Copyright 2024 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/core/Lynx.gni")
import("//lynx/core/runtime/vm/lepus/build.gni")
import("//lynx/testing/test.gni")

lynx_core_source_set("tasks") {
  sources = lynx_lepus_task_sources_path
  public_deps = [ "//lynx/core/base" ]
}

unittest_set("tasks_testset") {
  sources = [
    "//lynx/core/renderer/dom/air/testing/air_lepus_context_mock.h",
    "lepus_callback_manager_unittest.cc",
  ]
  public_deps = [
    ":tasks",
    "//lynx/core/base",
    "//lynx/third_party/quickjs",
  ]
  data_deps = []
}

unittest_exec("task_unittests_exec") {
  sources = []
  deps = [
    ":tasks_testset",
    "//lynx/core/renderer:tasm",
    "//lynx/core/renderer/dom:dom",
    "//lynx/core/renderer/ui_wrapper:ui_wrapper",
    "//lynx/core/renderer/utils:renderer_utils",
    "//lynx/core/runtime/bindings/lepus:lepus",
    "//lynx/core/runtime/vm/lepus:lepus",
    "//lynx/core/value_wrapper:value_wrapper",
  ]
}
