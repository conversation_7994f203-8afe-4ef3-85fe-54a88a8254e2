// Copyright 2023 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
#include "core/runtime/vm/lepus/lepus_global.h"

#include "base/include/log/logging.h"
#include "core/runtime/vm/lepus/json_parser.h"
#include "core/runtime/vm/lepus/vm_context.h"
namespace lynx {
namespace lepus {}  // namespace lepus
}  // namespace lynx
