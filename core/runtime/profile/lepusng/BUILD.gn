# Copyright 2024 The Lynx Authors. All rights reserved.
# Licensed under the Apache License Version 2.0 that can be found in the
# LICENSE file in the root directory of this source tree.

import("//lynx/core/Lynx.gni")
import("//lynx/testing/test.gni")

lynx_core_source_set("lepusng_profile") {
  sources = [
    "lepusng_profiler.cc",
    "lepusng_profiler.h",
  ]

  public_deps = [
    "//lynx/core/runtime/profile:profile_public_header",
    "//lynx/core/runtime/vm/lepus:lepus",
  ]
}

unittest_set("lepusng_profile_testset") {
  sources = [ "lepusng_profiler_unittest.cc" ]
  public_deps = [
    ":lepusng_profile",
    "//lynx/base/trace/native:trace",
    "//lynx/core/renderer:tasm",
    "//lynx/core/renderer/dom:dom",
    "//lynx/core/renderer/dom:renderer_dom",
    "//lynx/core/runtime/bindings/lepus",
    "//lynx/core/runtime/profile:profile",
    "//lynx/core/runtime/vm/lepus:lepus",
    "//lynx/core/runtime/vm/lepus:lepus_ng",
    "//lynx/testing/utils:testing_utils",
  ]
}

unittest_exec("lepusng_profile_unittests_exec") {
  sources = []
  deps = [ ":lepusng_profile_testset" ]
}

group("lepusng_profile_tests") {
  testonly = true
  deps = [
    ":lepusng_profile_testset",
    ":lepusng_profile_unittests_exec",
  ]
}
